#!/usr/bin/env python3
"""
SRT Caption Generator Utility

This utility converts transcription data (like from AI transcription services)
into SRT (SubRip Subtitle) format files that can be imported into DaVinci Resolve.

Compatible with the AI transcription output from this project.
"""

import os
import json
import argparse
from datetime import datetime
from typing import List, Dict, Any, Optional


class SRTGenerator:
    """Generates SRT caption files from transcription data"""
    
    def __init__(self):
        self.segments = []
    
    @staticmethod
    def format_timestamp(seconds: float) -> str:
        """
        Convert seconds to SRT timestamp format (HH:MM:SS,mmm)
        
        Args:
            seconds: Time in seconds
            
        Returns:
            Formatted SRT timestamp string
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"
    
    def add_segment(self, start_time: float, end_time: float, text: str, speaker: Optional[str] = None):
        """
        Add a caption segment
        
        Args:
            start_time: Start time in seconds
            end_time: End time in seconds
            text: Caption text
            speaker: Optional speaker identifier
        """
        segment = {
            'start': start_time,
            'end': end_time,
            'text': text.strip(),
            'speaker': speaker
        }
        self.segments.append(segment)
    
    def load_from_whisper_json(self, json_file: str) -> bool:
        """
        Load segments from Whisper JSON format
        
        Args:
            json_file: Path to Whisper JSON file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Clear existing segments
            self.segments = []
            
            # Load segments from Whisper format
            for segment in data.get('segments', []):
                self.add_segment(
                    start_time=segment['start'],
                    end_time=segment['end'],
                    text=segment['text']
                )
            
            print(f"✓ Loaded {len(self.segments)} segments from {json_file}")
            return True
            
        except Exception as e:
            print(f"✗ Error loading Whisper JSON: {e}")
            return False
    
    def load_from_transcription_data(self, transcription_data: Dict[str, Any]) -> bool:
        """
        Load segments from transcription data dictionary
        
        Args:
            transcription_data: Dictionary with transcription results
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Clear existing segments
            self.segments = []
            
            # Handle different transcription formats
            if 'segments' in transcription_data:
                # Standard format with segments
                for segment in transcription_data['segments']:
                    self.add_segment(
                        start_time=segment.get('start', 0),
                        end_time=segment.get('end', 0),
                        text=segment.get('text', ''),
                        speaker=segment.get('speaker')
                    )
            elif 'results' in transcription_data:
                # Alternative format
                for result in transcription_data['results']:
                    if 'alternatives' in result and result['alternatives']:
                        text = result['alternatives'][0].get('transcript', '')
                        # Estimate timing if not provided
                        start_time = result.get('start_time', 0)
                        end_time = result.get('end_time', start_time + 5.0)  # Default 5 second duration
                        
                        self.add_segment(start_time, end_time, text)
            else:
                print("✗ Unknown transcription data format")
                return False
            
            print(f"✓ Loaded {len(self.segments)} segments from transcription data")
            return True
            
        except Exception as e:
            print(f"✗ Error loading transcription data: {e}")
            return False
    
    def generate_srt(self, include_speaker_labels: bool = False, max_line_length: int = 80) -> str:
        """
        Generate SRT format content
        
        Args:
            include_speaker_labels: Whether to include speaker labels in captions
            max_line_length: Maximum characters per line before wrapping
            
        Returns:
            SRT formatted string
        """
        if not self.segments:
            print("✗ No segments available for SRT generation")
            return ""
        
        srt_content = []
        
        for i, segment in enumerate(self.segments, 1):
            # Add sequence number
            srt_content.append(str(i))
            
            # Add timestamp
            start_time = self.format_timestamp(segment['start'])
            end_time = self.format_timestamp(segment['end'])
            srt_content.append(f"{start_time} --> {end_time}")
            
            # Add text content
            text = segment['text']
            
            # Add speaker label if requested and available
            if include_speaker_labels and segment.get('speaker'):
                text = f"[{segment['speaker']}] {text}"
            
            # Handle line wrapping
            if len(text) > max_line_length:
                words = text.split()
                lines = []
                current_line = ""
                
                for word in words:
                    if len(current_line + " " + word) <= max_line_length:
                        current_line += (" " + word if current_line else word)
                    else:
                        if current_line:
                            lines.append(current_line)
                        current_line = word
                
                if current_line:
                    lines.append(current_line)
                
                text = "\n".join(lines)
            
            srt_content.append(text)
            srt_content.append("")  # Empty line between segments
        
        return "\n".join(srt_content)
    
    def save_srt(self, output_file: str, include_speaker_labels: bool = False, max_line_length: int = 80) -> bool:
        """
        Save SRT content to file
        
        Args:
            output_file: Output file path
            include_speaker_labels: Whether to include speaker labels
            max_line_length: Maximum characters per line
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Generate SRT content
            srt_content = self.generate_srt(include_speaker_labels, max_line_length)
            
            if not srt_content:
                return False
            
            # Ensure output directory exists
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # Save to file
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(srt_content)
            
            print(f"✓ Saved SRT file: {output_file}")
            print(f"  Segments: {len(self.segments)}")
            print(f"  Size: {len(srt_content)} characters")
            
            return True
            
        except Exception as e:
            print(f"✗ Error saving SRT file: {e}")
            return False
    
    def validate_segments(self) -> bool:
        """
        Validate segment data for SRT compliance
        
        Returns:
            True if valid, False otherwise
        """
        if not self.segments:
            print("✗ No segments to validate")
            return False
        
        issues = []
        
        for i, segment in enumerate(self.segments):
            # Check timing
            if segment['start'] >= segment['end']:
                issues.append(f"Segment {i+1}: Start time >= End time")
            
            # Check text content
            if not segment['text'].strip():
                issues.append(f"Segment {i+1}: Empty text content")
            
            # Check for overlapping segments (basic check)
            if i > 0 and segment['start'] < self.segments[i-1]['end']:
                issues.append(f"Segment {i+1}: Overlaps with previous segment")
        
        if issues:
            print("✗ Validation issues found:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print(f"✓ All {len(self.segments)} segments validated successfully")
            return True


def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description="Generate SRT caption files from transcription data")
    parser.add_argument("input", help="Input transcription file (JSON format)")
    parser.add_argument("-o", "--output", help="Output SRT file path", default=None)
    parser.add_argument("--speaker-labels", action="store_true", help="Include speaker labels in captions")
    parser.add_argument("--max-line-length", type=int, default=80, help="Maximum characters per line")
    parser.add_argument("--validate", action="store_true", help="Validate segments before saving")
    parser.add_argument("--info", action="store_true", help="Show information about transcription file")
    
    args = parser.parse_args()
    
    # Check input file
    if not os.path.exists(args.input):
        print(f"✗ Input file not found: {args.input}")
        return 1
    
    # Initialize generator
    generator = SRTGenerator()
    
    # Load transcription data
    print(f"Loading transcription data from: {args.input}")
    success = generator.load_from_whisper_json(args.input)
    
    if not success:
        return 1
    
    # Show info if requested
    if args.info:
        print(f"\nTranscription Information:")
        print(f"  Segments: {len(generator.segments)}")
        if generator.segments:
            print(f"  Duration: {generator.segments[-1]['end']:.2f} seconds")
            print(f"  First segment: {generator.segments[0]['text'][:50]}...")
            print(f"  Last segment: {generator.segments[-1]['text'][:50]}...")
    
    # Validate if requested
    if args.validate:
        if not generator.validate_segments():
            return 1
    
    # Determine output file
    if args.output:
        output_file = args.output
    else:
        # Generate output filename from input
        base_name = os.path.splitext(args.input)[0]
        output_file = f"{base_name}.srt"
    
    # Generate and save SRT
    success = generator.save_srt(
        output_file, 
        include_speaker_labels=args.speaker_labels,
        max_line_length=args.max_line_length
    )
    
    if success:
        print(f"\n✓ SRT generation completed successfully")
        return 0
    else:
        print(f"\n✗ SRT generation failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())