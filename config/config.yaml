# DaVinci Resolve AI Assistant Configuration

ai:
  # Primary AI model configuration
  primary_model: "anthropic/claude-3.5-sonnet"
  fallback_models:
    - "meta-llama/llama-3.3-70b-instruct"
    - "anthropic/claude-3.5-haiku"
    - "deepseek/deepseek-coder"
  
  # AI behavior settings
  max_tokens: 4000
  temperature: 0.7
  top_p: 0.9
  context_window: 200000  # Claude 3.5 Sonnet context window
  
  # API settings
  api_timeout: 60
  max_retries: 3
  retry_delay: 5

transcription:
  # Transcription engine settings
  engine: "faster-whisper"  # faster-whisper for optimal Apple Silicon performance
  model_size: "base"        # tiny, base, small, medium, large, large-v2, large-v3
  language: "auto"          # auto-detect or specify language code (en, es, fr, de, it, pt, ru, zh, ja)
  device: "auto"            # auto, cpu, cuda, mps (Metal Performance Shaders on Apple Silicon)
  compute_type: "int8"      # int8, int16, float16, float32
  
  # Apple Silicon optimizations
  use_metal: true           # Use Metal Performance Shaders on Apple Silicon
  optimize_for_apple_silicon: true  # Enable Apple Silicon specific optimizations
  
  # Audio processing
  sample_rate: 16000
  chunk_length: 30          # seconds for audio chunking
  overlap: 0.1              # 10% overlap between chunks for better context
  
  # Quality settings - optimized to reduce hallucinations
  beam_size: 1              # Lower beam size reduces hallucinations on silence
  best_of: 1                # Single best result to avoid over-generation
  temperature: 0.0          # Keep deterministic for consistency
  condition_on_previous_text: false  # Disable to prevent repetitive hallucinations
  
  # Output settings
  output_format: "txt"      # txt, srt, vtt, json
  include_word_timestamps: false
  
  # Error handling
  max_retries: 3
  fallback_model: "small"   # Fallback model if primary fails

resolve:
  # DaVinci Resolve connection settings
  auto_connect: true
  connection_timeout: 30
  retry_attempts: 5
  
  # Script execution
  script_timeout: 120
  headless_mode: false
  
  # Platform-specific paths (will be auto-detected)
  # windows_path: "C:\\Program Files\\Blackmagic Design\\DaVinci Resolve\\fusionscript.dll"
  # macos_path: "/Applications/DaVinci Resolve.app/Contents/Libraries/Fusion/fusionscript.so"
  # linux_path: "/opt/resolve/libs/Fusion/fusionscript.so"

ui:
  # User interface settings
  interface_type: "console"  # console, gui (future)
  theme: "dark"
  language: "en"
  
  # Display settings
  max_suggestions: 10
  show_confidence_scores: true
  auto_refresh: true
  refresh_interval: 5  # seconds

logging:
  # Logging configuration
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file: "logs/ai_assistant.log"
  max_size: "10MB"
  backup_count: 5
  
  # Console output
  console_output: true
  colored_output: true
  
performance:
  # Performance optimization
  max_concurrent_transcriptions: 2
  cache_size: "1GB"
  memory_limit: "4GB"
  
  # Processing settings
  batch_size: 10
  parallel_processing: true
  gpu_acceleration: true

security:
  # Security settings
  encrypt_api_keys: true
  secure_temp_files: true
  validate_inputs: true
  
  # File access restrictions
  allowed_extensions: [".mp4", ".mov", ".avi", ".mkv", ".wav", ".mp3", ".m4a", ".flac", ".ogg", ".aac"]
  max_file_size: "10GB"
  temp_file_cleanup: true