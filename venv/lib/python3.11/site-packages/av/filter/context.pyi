from av.filter import Graph
from av.frame import Frame

class FilterContext:
    name: str | None

    def init(self, args: str | None = None, **kwargs: str | None) -> None: ...
    def link_to(
        self, input_: FilterContext, output_idx: int = 0, input_idx: int = 0
    ) -> None: ...
    @property
    def graph(self) -> Graph: ...
    def push(self, frame: Frame) -> None: ...
    def pull(self) -> Frame: ...
