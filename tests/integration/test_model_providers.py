"""
Integration tests for model provider functionality.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))


class TestModelProviderIntegration:
    """Integration tests for model provider functionality."""
    
    @pytest.fixture
    def sample_config(self):
        """Sample configuration for testing."""
        return {
            'ai': {
                'primary_provider': 'openrouter',
                'fallback_provider': 'openai',
                'primary_model': 'anthropic/claude-3.5-sonnet',
                'fallback_model': 'gpt-4',
                'api_keys': {
                    'openrouter': 'test-openrouter-key',
                    'openai': 'test-openai-key',
                    'anthropic': 'test-anthropic-key',
                    'google': 'test-google-key'
                },
                'base_urls': {
                    'openrouter': 'https://openrouter.ai/api/v1',
                    'openai': 'https://api.openai.com/v1',
                    'anthropic': 'https://api.anthropic.com',
                    'google': 'https://generativelanguage.googleapis.com/v1beta'
                },
                'local_settings': {
                    'lm_studio_port': '1234',
                    'ollama_port': '11434',
                    'local_model_path': '/path/to/local/model'
                },
                'max_tokens': 4000,
                'temperature': 0.7,
                'top_p': 0.9,
                'presence_penalty': 0.0,
                'frequency_penalty': 0.0
            }
        }
    
    def test_config_loading_and_validation(self, sample_config):
        """Test configuration loading and validation."""
        ai_config = sample_config['ai']
        
        # Test required fields exist
        assert 'primary_provider' in ai_config
        assert 'fallback_provider' in ai_config
        assert 'api_keys' in ai_config
        assert 'base_urls' in ai_config
        assert 'local_settings' in ai_config
        
        # Test provider consistency
        primary_provider = ai_config['primary_provider']
        assert primary_provider in ai_config['api_keys']
        assert primary_provider in ai_config['base_urls']
        
        fallback_provider = ai_config['fallback_provider']
        assert fallback_provider in ai_config['api_keys']
        assert fallback_provider in ai_config['base_urls']
    
    def test_model_provider_switching(self, sample_config):
        """Test model provider switching functionality."""
        ai_config = sample_config['ai']
        
        # Test switching from OpenRouter to OpenAI
        original_provider = ai_config['primary_provider']
        new_provider = ai_config['fallback_provider']
        
        # Simulate provider switch
        ai_config['primary_provider'] = new_provider
        ai_config['primary_model'] = ai_config['fallback_model']
        
        assert ai_config['primary_provider'] == new_provider
        assert ai_config['primary_provider'] != original_provider
    
    def test_api_key_validation(self, sample_config):
        """Test API key validation for different providers."""
        api_keys = sample_config['ai']['api_keys']
        
        for provider, api_key in api_keys.items():
            # Test API key format (basic validation)
            assert api_key is not None
            assert len(api_key) > 0
            assert isinstance(api_key, str)
            
            # Test that API key doesn't contain obvious placeholders
            assert api_key != 'your-api-key-here'
            assert api_key != 'placeholder'
            assert api_key != ''
    
    def test_base_url_validation(self, sample_config):
        """Test base URL validation for different providers."""
        base_urls = sample_config['ai']['base_urls']
        
        for provider, base_url in base_urls.items():
            # Test URL format
            assert base_url is not None
            assert base_url.startswith('https://')
            assert len(base_url) > 8  # More than just 'https://'
            
            # Test provider-specific URL patterns
            if provider == 'openrouter':
                assert 'openrouter.ai' in base_url
            elif provider == 'openai':
                assert 'api.openai.com' in base_url
            elif provider == 'anthropic':
                assert 'api.anthropic.com' in base_url
            elif provider == 'google':
                assert 'googleapis.com' in base_url
    
    def test_local_model_configuration(self, sample_config):
        """Test local model configuration."""
        local_settings = sample_config['ai']['local_settings']
        
        # Test port configurations
        lm_studio_port = local_settings['lm_studio_port']
        ollama_port = local_settings['ollama_port']
        
        assert lm_studio_port.isdigit()
        assert ollama_port.isdigit()
        assert 1000 <= int(lm_studio_port) <= 65535
        assert 1000 <= int(ollama_port) <= 65535
        
        # Test model path
        model_path = local_settings['local_model_path']
        assert model_path is not None
        assert len(model_path) > 0
    
    def test_model_parameter_ranges(self, sample_config):
        """Test model parameter validation ranges."""
        ai_config = sample_config['ai']
        
        # Test temperature range
        temperature = ai_config['temperature']
        assert 0.0 <= temperature <= 2.0
        
        # Test top_p range
        top_p = ai_config['top_p']
        assert 0.0 <= top_p <= 1.0
        
        # Test penalty ranges
        presence_penalty = ai_config['presence_penalty']
        frequency_penalty = ai_config['frequency_penalty']
        assert -2.0 <= presence_penalty <= 2.0
        assert -2.0 <= frequency_penalty <= 2.0
        
        # Test max_tokens
        max_tokens = ai_config['max_tokens']
        assert max_tokens > 0
        assert max_tokens <= 32000  # Reasonable upper limit


class TestProviderSpecificIntegration:
    """Test provider-specific integration scenarios."""
    
    def test_openrouter_integration(self):
        """Test OpenRouter-specific integration."""
        openrouter_config = {
            'provider_type': 'openrouter',
            'base_url': 'https://openrouter.ai/api/v1',
            'api_key': 'test-openrouter-key',
            'models': [
                'anthropic/claude-3.5-sonnet',
                'openai/gpt-4',
                'meta-llama/llama-3.1-70b-instruct'
            ]
        }
        
        # Test model format validation
        for model in openrouter_config['models']:
            assert '/' in model  # OpenRouter uses provider/model format
            provider, model_name = model.split('/', 1)
            assert len(provider) > 0
            assert len(model_name) > 0
    
    def test_openai_integration(self):
        """Test OpenAI-specific integration."""
        openai_config = {
            'provider_type': 'openai',
            'base_url': 'https://api.openai.com/v1',
            'api_key': 'test-openai-key',
            'models': [
                'gpt-4',
                'gpt-4-turbo',
                'gpt-3.5-turbo'
            ]
        }
        
        # Test model format validation
        for model in openai_config['models']:
            assert model.startswith('gpt-')
            assert '/' not in model  # OpenAI uses direct model names
    
    def test_anthropic_integration(self):
        """Test Anthropic-specific integration."""
        anthropic_config = {
            'provider_type': 'anthropic',
            'base_url': 'https://api.anthropic.com',
            'api_key': 'test-anthropic-key',
            'models': [
                'claude-3-5-sonnet-20241022',
                'claude-3-opus-20240229',
                'claude-3-haiku-20240307'
            ]
        }
        
        # Test model format validation
        for model in anthropic_config['models']:
            assert model.startswith('claude-')
            assert '/' not in model  # Anthropic uses direct model names
    
    def test_local_model_integration(self):
        """Test local model integration."""
        local_config = {
            'provider_type': 'local',
            'lm_studio': {
                'port': '1234',
                'base_url': 'http://localhost:1234/v1'
            },
            'ollama': {
                'port': '11434',
                'base_url': 'http://localhost:11434/api'
            },
            'models': [
                'llama-3.1-8b-instruct',
                'mistral-7b-instruct',
                'codellama-13b-instruct'
            ]
        }
        
        # Test local configuration
        assert local_config['lm_studio']['port'].isdigit()
        assert local_config['ollama']['port'].isdigit()
        assert 'localhost' in local_config['lm_studio']['base_url']
        assert 'localhost' in local_config['ollama']['base_url']
        
        # Test model format validation
        for model in local_config['models']:
            assert '/' not in model  # Local models use direct names
            assert model.endswith('-instruct') or 'llama' in model or 'mistral' in model


class TestConnectionTesting:
    """Test connection testing functionality."""
    
    @pytest.fixture
    def mock_http_client(self):
        """Mock HTTP client for testing."""
        mock_client = Mock()
        return mock_client
    
    def test_successful_connection_test(self, mock_http_client):
        """Test successful connection to provider."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status = 200
        mock_response.json.return_value = {
            'models': ['anthropic/claude-3.5-sonnet'],
            'usage': {'requests': 100, 'tokens': 50000}
        }
        mock_http_client.get.return_value = mock_response
        
        # Simulate connection test
        connection_result = {
            'success': True,
            'status_code': 200,
            'latency': 150,
            'models_available': True
        }
        
        assert connection_result['success'] is True
        assert connection_result['status_code'] == 200
        assert connection_result['latency'] > 0
        assert connection_result['models_available'] is True
    
    def test_failed_connection_test(self, mock_http_client):
        """Test failed connection to provider."""
        # Mock failed response
        mock_response = Mock()
        mock_response.status = 401
        mock_response.json.return_value = {
            'error': 'Invalid API key'
        }
        mock_http_client.get.return_value = mock_response
        
        # Simulate connection test
        connection_result = {
            'success': False,
            'status_code': 401,
            'error': 'Invalid API key',
            'models_available': False
        }
        
        assert connection_result['success'] is False
        assert connection_result['status_code'] == 401
        assert 'error' in connection_result
        assert connection_result['models_available'] is False
    
    def test_connection_timeout(self, mock_http_client):
        """Test connection timeout handling."""
        # Mock timeout
        mock_http_client.get.side_effect = Exception("Connection timeout")
        
        # Simulate connection test
        connection_result = {
            'success': False,
            'error': 'Connection timeout',
            'timeout': True
        }
        
        assert connection_result['success'] is False
        assert connection_result['timeout'] is True
        assert 'timeout' in connection_result['error'].lower()


class TestConfigurationPersistence:
    """Test configuration persistence and loading."""
    
    def test_config_save_and_load(self, tmp_path):
        """Test saving and loading configuration."""
        config_file = tmp_path / "test_config.json"
        
        # Sample configuration
        test_config = {
            'ai': {
                'primary_provider': 'openrouter',
                'primary_model': 'anthropic/claude-3.5-sonnet',
                'api_keys': {
                    'openrouter': 'test-key'
                },
                'base_urls': {
                    'openrouter': 'https://openrouter.ai/api/v1'
                }
            }
        }
        
        # Save configuration
        with open(config_file, 'w') as f:
            json.dump(test_config, f, indent=2)
        
        # Load configuration
        with open(config_file, 'r') as f:
            loaded_config = json.load(f)
        
        # Verify configuration integrity
        assert loaded_config == test_config
        assert loaded_config['ai']['primary_provider'] == 'openrouter'
        assert loaded_config['ai']['api_keys']['openrouter'] == 'test-key'
    
    def test_config_migration(self):
        """Test configuration migration from old format."""
        # Old format configuration
        old_config = {
            'ai': {
                'primary_model': 'gpt-4',
                'fallback_model': 'gpt-3.5-turbo',
                'api_key': 'old-openai-key',
                'max_tokens': 4000
            }
        }
        
        # Simulate migration to new format
        new_config = {
            'ai': {
                'primary_provider': 'openai',
                'fallback_provider': 'openai',
                'primary_model': old_config['ai']['primary_model'],
                'fallback_model': old_config['ai']['fallback_model'],
                'api_keys': {
                    'openai': old_config['ai']['api_key']
                },
                'base_urls': {
                    'openai': 'https://api.openai.com/v1'
                },
                'max_tokens': old_config['ai']['max_tokens']
            }
        }
        
        # Verify migration
        assert new_config['ai']['primary_provider'] == 'openai'
        assert new_config['ai']['api_keys']['openai'] == 'old-openai-key'
        assert new_config['ai']['primary_model'] == 'gpt-4'
    
    def test_config_validation_on_load(self):
        """Test configuration validation when loading."""
        # Invalid configuration
        invalid_config = {
            'ai': {
                'primary_provider': 'invalid_provider',
                'temperature': 5.0,  # Invalid: > 2.0
                'top_p': 2.0,       # Invalid: > 1.0
                'max_tokens': -100   # Invalid: < 0
            }
        }
        
        # Validation should catch these errors
        ai_config = invalid_config['ai']
        
        # Test validation logic
        validation_errors = []
        
        if ai_config.get('temperature', 0) > 2.0:
            validation_errors.append('Temperature must be <= 2.0')
        
        if ai_config.get('top_p', 0) > 1.0:
            validation_errors.append('Top P must be <= 1.0')
        
        if ai_config.get('max_tokens', 0) <= 0:
            validation_errors.append('Max tokens must be > 0')
        
        # Should have validation errors
        assert len(validation_errors) > 0
        assert any('Temperature' in error for error in validation_errors)
        assert any('Top P' in error for error in validation_errors)
        assert any('Max tokens' in error for error in validation_errors)