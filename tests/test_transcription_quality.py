"""
Quality-focused tests for transcription endpoint that validate accuracy and detect hallucinations.
"""
import pytest
import requests
import tempfile
import os
import numpy as np
import soundfile as sf  # type: ignore
from typing import Dict, Any


class TestTranscriptionQuality:
    """Test suite focused on transcription quality and accuracy."""
    
    BASE_URL = "http://127.0.0.1:5000"
    
    def create_test_audio(self, duration: float, sample_rate: int = 16000, 
                         audio_type: str = "sine") -> str:
        """Create test audio file and return path."""
        samples = int(duration * sample_rate)
        
        if audio_type == "sine":
            # Pure sine wave at 440Hz
            t = np.linspace(0, duration, samples, False)
            audio = 0.3 * np.sin(2 * np.pi * 440 * t)
        elif audio_type == "silence":
            # Complete silence
            audio = np.zeros(samples)
        elif audio_type == "noise":
            # White noise
            audio = 0.1 * np.random.normal(0, 1, samples)
        else:
            raise ValueError(f"Unknown audio type: {audio_type}")
        
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
        sf.write(temp_file.name, audio, sample_rate)
        temp_file.close()
        
        return temp_file.name
    
    def upload_audio_file(self, file_path: str, **kwargs) -> requests.Response:
        """Upload audio file to transcription endpoint."""
        with open(file_path, 'rb') as f:
            files = {'audio': f}
            data = kwargs
            response = requests.post(f"{self.BASE_URL}/api/transcribe", 
                                   files=files, data=data)
        return response
    
    def cleanup_file(self, file_path: str):
        """Clean up temporary file."""
        if os.path.exists(file_path):
            os.unlink(file_path)
    
    def test_server_status(self):
        """Test that the server is running."""
        response = requests.get(f"{self.BASE_URL}/api/health")
        assert response.status_code == 200
    
    def test_silence_should_return_empty_or_minimal_text(self):
        """Silent audio should not produce hallucinated text."""
        file_path = self.create_test_audio(2.0, audio_type="silence")
        
        try:
            response = self.upload_audio_file(file_path)
            assert response.status_code == 200
            
            data = response.json()
            transcription = data.get('transcription', {})
            text = transcription.get('text', '')
            
            # Silent audio should produce minimal or no text
            # If text is produced, it should be very short (likely noise artifacts)
            assert len(text) <= 10, f"Silent audio produced too much text: '{text}'"
            
            # Check for common hallucination patterns
            hallucination_patterns = [
                "i'm sorry" * 5,  # Repetitive apologies
                "thank you" * 5,  # Repetitive thanks
                "hello" * 5,      # Repetitive greetings
            ]
            
            text_lower = text.lower()
            for pattern in hallucination_patterns:
                assert pattern not in text_lower, f"Detected hallucination pattern: '{pattern}'"
                
        finally:
            self.cleanup_file(file_path)
    
    def test_pure_sine_wave_should_not_hallucinate(self):
        """Pure sine waves should not produce meaningful speech."""
        file_path = self.create_test_audio(3.0, audio_type="sine")
        
        try:
            response = self.upload_audio_file(file_path)
            assert response.status_code == 200
            
            data = response.json()
            transcription = data.get('transcription', {})
            text = transcription.get('text', '')
            
            # Pure sine wave should not produce meaningful speech
            # Allow for some noise artifacts but not full sentences
            word_count = len(text.split()) if text else 0
            assert word_count <= 3, f"Sine wave produced too many words: '{text}'"
            
            # Check for repetitive hallucinations
            if text:
                words = text.lower().split()
                if len(words) > 1:
                    # Check if more than 50% of words are the same (repetitive)
                    most_common_word = max(set(words), key=words.count)
                    repetition_ratio = words.count(most_common_word) / len(words)
                    assert repetition_ratio < 0.7, f"Detected repetitive hallucination: '{text}'"
                    
        finally:
            self.cleanup_file(file_path)
    
    def test_very_short_audio_quality(self):
        """Very short audio should not produce excessive hallucinations."""
        # Create very short audio (0.1 seconds)
        file_path = self.create_test_audio(0.1, audio_type="noise")
        
        try:
            response = self.upload_audio_file(file_path)
            assert response.status_code == 200
            
            data = response.json()
            transcription = data.get('transcription', {})
            text = transcription.get('text', '')
            
            # Very short audio should not produce long transcriptions
            assert len(text) <= 100, f"Very short audio produced too much text: '{text}'"
            
            # Check for excessive repetition
            if text:
                words = text.split()
                if len(words) > 5:
                    # Count unique words vs total words
                    unique_words = len(set(word.lower() for word in words))
                    repetition_ratio = unique_words / len(words)
                    assert repetition_ratio > 0.3, f"Excessive repetition detected: '{text}'"
                    
        finally:
            self.cleanup_file(file_path)
    
    def test_noise_audio_quality(self):
        """White noise should not produce coherent speech."""
        file_path = self.create_test_audio(2.0, audio_type="noise")
        
        try:
            response = self.upload_audio_file(file_path)
            assert response.status_code == 200
            
            data = response.json()
            transcription = data.get('transcription', {})
            text = transcription.get('text', '')
            
            # White noise should not produce coherent sentences
            if text:
                # Check for coherent sentence patterns (multiple words with proper structure)
                words = text.split()
                if len(words) > 5:
                    # Simple heuristic: if we have many common English words, it might be hallucination
                    common_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
                    common_word_count = sum(1 for word in words if word.lower() in common_words)
                    common_ratio = common_word_count / len(words)
                    
                    # If more than 30% are common words, it might be hallucination
                    assert common_ratio < 0.3, f"Noise audio produced coherent-looking text: '{text}'"
                    
        finally:
            self.cleanup_file(file_path)
    
    def test_confidence_scores_reasonable(self):
        """Confidence scores should be reasonable for different audio types."""
        test_cases = [
            ("silence", 1.0),
            ("sine", 2.0),
            ("noise", 1.5)
        ]
        
        for audio_type, duration in test_cases:
            file_path = self.create_test_audio(duration, audio_type=audio_type)
            
            try:
                response = self.upload_audio_file(file_path)
                assert response.status_code == 200
                
                data = response.json()
                transcription = data.get('transcription', {})
                confidence = transcription.get('confidence', 0)
                text = transcription.get('text', '')
                
                # If there's no meaningful text, confidence should reflect uncertainty
                if not text.strip() or len(text.strip()) <= 3:
                    # Empty or very short text should have reasonable confidence
                    assert 0.0 <= confidence <= 1.0, f"Invalid confidence score: {confidence}"
                else:
                    # If text is produced, confidence should be reasonable
                    # Very high confidence (>0.95) on noise/sine might indicate overconfidence
                    if audio_type in ["noise", "sine"]:
                        assert confidence <= 0.95, f"Overconfident on {audio_type} audio: {confidence}"
                        
            finally:
                self.cleanup_file(file_path)
    
    def test_processing_time_reasonable(self):
        """Processing time should be reasonable for different audio lengths."""
        test_cases = [0.5, 1.0, 2.0, 5.0]  # Different durations
        
        for duration in test_cases:
            file_path = self.create_test_audio(duration, audio_type="sine")
            
            try:
                response = self.upload_audio_file(file_path)
                assert response.status_code == 200
                
                data = response.json()
                transcription = data.get('transcription', {})
                processing_time = transcription.get('processing_time', 0)
                
                # Processing time should be positive and reasonable
                assert processing_time > 0, "Processing time should be positive"
                assert processing_time < 30, f"Processing time too long: {processing_time}s for {duration}s audio"
                
                # Rough heuristic: processing should not take more than 10x the audio duration
                assert processing_time < duration * 10, f"Processing too slow: {processing_time}s for {duration}s audio"
                
            finally:
                self.cleanup_file(file_path)


if __name__ == "__main__":
    # Run tests directly
    import sys
    
    test_instance = TestTranscriptionQuality()
    
    tests = [
        test_instance.test_server_status,
        test_instance.test_silence_should_return_empty_or_minimal_text,
        test_instance.test_pure_sine_wave_should_not_hallucinate,
        test_instance.test_very_short_audio_quality,
        test_instance.test_noise_audio_quality,
        test_instance.test_confidence_scores_reasonable,
        test_instance.test_processing_time_reasonable,
    ]
    
    passed = 0
    failed = 0
    
    print("🔍 Running Transcription Quality Tests")
    print("=" * 50)
    
    for test in tests:
        try:
            test()
            print(f"✅ {test.__name__}")
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__}: {e}")
            failed += 1
    
    print("=" * 50)
    print(f"📊 Results: {passed} passed, {failed} failed")
    
    if failed > 0:
        sys.exit(1)