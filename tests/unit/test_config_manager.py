"""
Unit tests for Configuration Manager
"""

import os
import json
import yaml
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add src to path for imports
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from src.core.config_manager import ConfigManager, ConfigDefaults


class TestConfigManager(unittest.TestCase):
    """Test cases for ConfigManager class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create a temporary directory for test configs
        self.temp_dir = tempfile.mkdtemp()
        self.test_config_path = os.path.join(self.temp_dir, 'test_config.yaml')
        
        # Clean up environment variables that might interfere
        self._clean_env_vars()
        
    def tearDown(self):
        """Clean up after each test method."""
        # Clean up temporary files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
        # Restore environment variables
        self._restore_env_vars()
    
    def _clean_env_vars(self):
        """Clean up environment variables that might affect tests."""
        self._original_env = {}
        env_vars_to_clean = [
            'DAVINCI_AI_LOG_LEVEL', 'DAVINCI_AI_LOG_FILE', 'DAVINCI_AI_TRANSCRIPTION_MODEL',
            'DAVINCI_AI_TRANSCRIPTION_LANGUAGE', 'DAVINCI_AI_AI_MODEL', 'DAVINCI_AI_MAX_TOKENS',
            'DAVINCI_AI_TEMPERATURE', 'DAVINCI_AI_RESOLVE_TIMEOUT', 'DAVINCI_AI_RESOLVE_SCRIPT_PATH',
            'DAVINCI_AI_OPTIMIZE_APPLE_SILICON', 'DAVINCI_AI_MAX_CONCURRENT_TRANSCRIPTIONS',
            'DAVINCI_AI_CACHE_SIZE', 'DAVINCI_AI_MEMORY_LIMIT_MB', 'DAVINCI_AI_CPU_LIMIT_PERCENT'
        ]
        
        for var in env_vars_to_clean:
            if var in os.environ:
                self._original_env[var] = os.environ[var]
                del os.environ[var]
    
    def _restore_env_vars(self):
        """Restore original environment variables."""
        for var, value in self._original_env.items():
            os.environ[var] = value
    
    def test_init_with_default_config(self):
        """Test initialization with default configuration."""
        # Create config manager with non-existent path to trigger defaults
        config_manager = ConfigManager(config_path="non_existent_config.yaml")
        
        # Check that default values are loaded
        self.assertEqual(config_manager.get('ai.primary_model'), ConfigDefaults.AI_PRIMARY_MODEL)
        self.assertEqual(config_manager.get('transcription.engine'), ConfigDefaults.TRANSCRIPTION_ENGINE)
        self.assertEqual(config_manager.get('transcription.model_size'), ConfigDefaults.TRANSCRIPTION_MODEL)
        self.assertEqual(config_manager.get('logging.level'), ConfigDefaults.LOG_LEVEL)
    
    def test_init_with_existing_config_file(self):
        """Test initialization with existing configuration file."""
        # Create a test config file
        test_config = {
            'ai': {
                'primary_model': 'test-model',
                'max_tokens': 2000
            },
            'transcription': {
                'engine': 'whisper',
                'model_size': 'small'
            }
        }
        
        with open(self.test_config_path, 'w') as f:
            yaml.dump(test_config, f)
        
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        # Check that config values are loaded correctly
        self.assertEqual(config_manager.get('ai.primary_model'), 'test-model')
        self.assertEqual(config_manager.get('ai.max_tokens'), 2000)
        self.assertEqual(config_manager.get('transcription.engine'), 'whisper')
        self.assertEqual(config_manager.get('transcription.model_size'), 'small')
    
    def test_load_config_from_json(self):
        """Test loading configuration from JSON file."""
        json_config_path = os.path.join(self.temp_dir, 'test_config.json')
        
        test_config = {
            'ai': {
                'primary_model': 'json-test-model'
            },
            'logging': {
                'level': 'DEBUG'
            }
        }
        
        with open(json_config_path, 'w') as f:
            json.dump(test_config, f)
        
        config_manager = ConfigManager(config_path=json_config_path)
        
        self.assertEqual(config_manager.get('ai.primary_model'), 'json-test-model')
        self.assertEqual(config_manager.get('logging.level'), 'DEBUG')
    
    def test_get_nested_value(self):
        """Test getting nested configuration values."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        # Test getting nested values
        transcription_engine = config_manager.get('transcription.engine')
        self.assertIsNotNone(transcription_engine)
        self.assertIn(transcription_engine, ['faster-whisper', 'whisper', 'whisperx'])
        
        # Test getting non-existent key with default
        non_existent = config_manager.get('non.existent.key', 'default_value')
        self.assertEqual(non_existent, 'default_value')
        
        # Test getting non-existent key without default
        non_existent_none = config_manager.get('non.existent.key')
        self.assertIsNone(non_existent_none)
    
    def test_set_nested_value(self):
        """Test setting nested configuration values."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        # Test setting nested values
        config_manager.set('ai.primary_model', 'new-model')
        self.assertEqual(config_manager.get('ai.primary_model'), 'new-model')
        
        # Test setting new nested structure
        config_manager.set('new.section.key', 'new-value')
        self.assertEqual(config_manager.get('new.section.key'), 'new-value')
    
    def test_get_section(self):
        """Test getting entire configuration sections."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        # Test getting AI section
        ai_section = config_manager.get_section('ai')
        self.assertIsInstance(ai_section, dict)
        self.assertIn('primary_model', ai_section)
        
        # Test getting non-existent section
        non_existent_section = config_manager.get_section('non_existent')
        self.assertEqual(non_existent_section, {})
    
    def test_update_section(self):
        """Test updating entire configuration sections."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        # Test updating AI section
        new_ai_config = {
            'primary_model': 'updated-model',
            'max_tokens': 5000,
            'temperature': 0.5
        }
        
        config_manager.update_section('ai', new_ai_config)
        updated_ai_section = config_manager.get_section('ai')
        
        self.assertEqual(updated_ai_section['primary_model'], 'updated-model')
        self.assertEqual(updated_ai_section['max_tokens'], 5000)
        self.assertEqual(updated_ai_section['temperature'], 0.5)
    
    def test_load_environment_variables(self):
        """Test loading configuration from environment variables."""
        # Set environment variables
        os.environ['DAVINCI_AI_LOG_LEVEL'] = 'DEBUG'
        os.environ['DAVINCI_AI_MAX_TOKENS'] = '8192'
        os.environ['DAVINCI_AI_TEMPERATURE'] = '0.9'
        os.environ['DAVINCI_AI_OPTIMIZE_APPLE_SILICON'] = 'true'
        
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        # Check that environment variables are loaded
        self.assertEqual(config_manager.get('logging.level'), 'DEBUG')
        self.assertEqual(config_manager.get('ai.max_tokens'), 8192)
        self.assertEqual(config_manager.get('ai.temperature'), 0.9)
        self.assertTrue(config_manager.get('transcription.optimize_for_apple_silicon'))
    
    def test_invalid_environment_variable_values(self):
        """Test handling of invalid environment variable values."""
        # Set invalid values
        os.environ['DAVINCI_AI_MAX_TOKENS'] = 'invalid_int'
        os.environ['DAVINCI_AI_TEMPERATURE'] = 'invalid_float'
        os.environ['DAVINCI_AI_OPTIMIZE_APPLE_SILICON'] = 'invalid_bool'
        
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        # Check that invalid values are ignored and defaults are used
        self.assertEqual(config_manager.get('ai.max_tokens'), ConfigDefaults.AI_MAX_TOKENS)
        self.assertEqual(config_manager.get('ai.temperature'), ConfigDefaults.AI_TEMPERATURE)
        self.assertEqual(config_manager.get('transcription.optimize_for_apple_silicon'), 
                        ConfigDefaults.TRANSCRIPTION_OPTIMIZE_APPLE_SILICON)
    
    def test_validate_config_valid(self):
        """Test configuration validation with valid settings."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        errors = config_manager.validate_config()
        self.assertEqual(errors, [])
    
    def test_validate_config_invalid_transcription_engine(self):
        """Test configuration validation with invalid transcription engine."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        config_manager.set('transcription.engine', 'invalid-engine')
        
        errors = config_manager.validate_config()
        self.assertTrue(any('Invalid transcription engine' in error for error in errors))
    
    def test_validate_config_invalid_model_size(self):
        """Test configuration validation with invalid model size."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        config_manager.set('transcription.model_size', 'invalid-size')
        
        errors = config_manager.validate_config()
        self.assertTrue(any('Invalid model size' in error for error in errors))
    
    def test_validate_config_invalid_ai_model(self):
        """Test configuration validation with invalid AI model."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        config_manager.set('ai.primary_model', '')
        
        errors = config_manager.validate_config()
        self.assertTrue(any('Invalid AI primary model configuration' in error for error in errors))
    
    def test_validate_config_invalid_resolve_path(self):
        """Test configuration validation with invalid DaVinci Resolve path."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        config_manager.set('resolve.script_path', '/non/existent/path')
        
        errors = config_manager.validate_config()
        self.assertTrue(any('DaVinci Resolve script path does not exist' in error for error in errors))
    
    def test_is_apple_silicon_optimized(self):
        """Test Apple Silicon optimization check."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        # Test default value
        self.assertEqual(config_manager.is_apple_silicon_optimized(), 
                        ConfigDefaults.TRANSCRIPTION_OPTIMIZE_APPLE_SILICON)
        
        # Test when set to true
        config_manager.set('transcription.optimize_for_apple_silicon', True)
        self.assertTrue(config_manager.is_apple_silicon_optimized())
        
        # Test when set to false
        config_manager.set('transcription.optimize_for_apple_silicon', False)
        self.assertFalse(config_manager.is_apple_silicon_optimized())
    
    def test_get_transcription_config(self):
        """Test getting transcription configuration section."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        transcription_config = config_manager.get_transcription_config()
        
        self.assertIsInstance(transcription_config, dict)
        self.assertIn('engine', transcription_config)
        self.assertIn('model_size', transcription_config)
        self.assertIn('language', transcription_config)
    
    def test_config_file_creation_on_missing(self):
        """Test that config file is created when missing."""
        non_existent_path = os.path.join(self.temp_dir, 'missing_config.yaml')
        
        config_manager = ConfigManager(config_path=non_existent_path)
        
        # Check that file was created
        self.assertTrue(os.path.exists(non_existent_path))
        
        # Check that default config was saved
        with open(non_existent_path, 'r') as f:
            saved_config = yaml.safe_load(f)
        
        self.assertIn('ai', saved_config)
        self.assertIn('transcription', saved_config)
        self.assertIn('logging', saved_config)
    
    def test_load_config_method(self):
        """Test the public load_config method."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        # Modify the config file
        new_config = {'ai': {'primary_model': 'reloaded-model'}}
        with open(self.test_config_path, 'w') as f:
            yaml.dump(new_config, f)
        
        # Reload config
        config_manager.load_config()
        
        # Check that new config is loaded
        self.assertEqual(config_manager.get('ai.primary_model'), 'reloaded-model')
    
    def test_deep_merge_functionality(self):
        """Test deep merge functionality."""
        config_manager = ConfigManager(config_path=self.test_config_path)
        
        # Test that user config merges with defaults
        user_config = {
            'ai': {
                'primary_model': 'user-model'
            },
            'transcription': {
                'engine': 'user-engine'
            }
        }
        
        with open(self.test_config_path, 'w') as f:
            yaml.dump(user_config, f)
        
        config_manager.load_config()
        
        # Check that user values override defaults
        self.assertEqual(config_manager.get('ai.primary_model'), 'user-model')
        self.assertEqual(config_manager.get('transcription.engine'), 'user-engine')
        
        # Check that other defaults are preserved
        self.assertEqual(config_manager.get('ai.max_tokens'), ConfigDefaults.AI_MAX_TOKENS)
        self.assertEqual(config_manager.get('transcription.model_size'), ConfigDefaults.TRANSCRIPTION_MODEL)


if __name__ == '__main__':
    unittest.main()