import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Import the classes to test
from src.ai_engine.ai_engine import AIEngine
from src.ai_engine.content_analyzer import ContentAnalyzer, ContentAnalysis
from src.ai_engine.editing_suggestions import EditingSuggestionsGenerator, EditingSuggestions, CutSuggestion, TransitionSuggestion, EffectSuggestion
from src.transcription import TranscriptionResult

# Import model provider classes for testing
try:
    from src.ai_engine.model_providers import ModelManager, ModelProviderFactory, ModelProviderType
    from src.ai_engine.model_utils import create_model_config, get_provider_display_name
except ImportError:
    # Mock these if not available
    ModelManager = Mock
    ModelProviderFactory = Mock
    ModelProviderType = Mock
    create_model_config = Mock
    get_provider_display_name = Mock


class TestAIEngine:
    """Unit tests for AIEngine class."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration."""
        return {
            'engine_type': 'rule_based',
            'confidence_threshold': 0.7,
            'max_suggestions': 10
        }
    
    @pytest.fixture
    def mock_transcription_result(self):
        """Create a mock transcription result."""
        result = Mock(spec=TranscriptionResult)
        result.text = "This is a test transcription about video editing and content creation."
        result.segments = [
            {'start': 0.0, 'end': 5.0, 'text': 'This is a test transcription'},
            {'start': 5.0, 'end': 10.0, 'text': 'about video editing and content creation'}
        ]
        result.language = 'en'
        result.confidence = 0.85
        return result
    
    @pytest.fixture
    def mock_content_analysis(self):
        """Create a mock content analysis."""
        analysis = Mock(spec=ContentAnalysis)
        analysis.key_topics = ['video', 'editing', 'content', 'creation']
        analysis.sentiment = 'positive'
        analysis.speakers = ['Speaker 1']
        analysis.duration = 10.0
        analysis.word_count = 12
        analysis.language = 'en'
        analysis.confidence = 0.85
        analysis.segments = [
            {'start': 0.0, 'end': 5.0, 'text': 'This is a test transcription'},
            {'start': 5.0, 'end': 10.0, 'text': 'about video editing and content creation'}
        ]
        return analysis
    
    @pytest.fixture
    def mock_editing_suggestions(self):
        """Create a mock editing suggestions."""
        suggestions = Mock(spec=EditingSuggestions)
        suggestions.cuts = [
            CutSuggestion(time=5.0, reason="Natural break point", confidence=0.8)
        ]
        suggestions.transitions = [
            TransitionSuggestion(type="fade", from_time=0.0, to_time=1.0, reason="Smooth introduction", confidence=0.9)
        ]
        suggestions.effects = [
            EffectSuggestion(type="text_overlay", time=2.0, parameters={"text": "Video"}, reason="Highlight key topic", confidence=0.8)
        ]
        suggestions.analysis = Mock()
        return suggestions
    
    @pytest.fixture
    def ai_engine(self, mock_config):
        """Create AIEngine instance with mock config."""
        return AIEngine(mock_config)
    
    def test_initialization_with_config(self, mock_config):
        """Test AIEngine initialization with configuration."""
        engine = AIEngine(mock_config)
        assert engine.config == mock_config
        assert engine.content_analyzer is not None
        assert engine.suggestions_generator is not None
    
    def test_initialization_without_config(self):
        """Test AIEngine initialization without configuration."""
        engine = AIEngine()
        assert engine.config == {}
        assert engine.content_analyzer is not None
        assert engine.suggestions_generator is not None
    
    @patch('src.ai_engine.ai_engine.ContentAnalyzer.analyze_transcription')
    def test_analyze_content_success(self, mock_analyze, ai_engine, mock_transcription_result, mock_content_analysis):
        """Test successful content analysis."""
        mock_analyze.return_value = mock_content_analysis
        
        result = ai_engine.analyze_content(mock_transcription_result)
        
        assert result == mock_content_analysis
        mock_analyze.assert_called_once_with(mock_transcription_result)
    
    @patch('src.ai_engine.ai_engine.ContentAnalyzer.analyze_transcription')
    def test_analyze_content_failure(self, mock_analyze, ai_engine, mock_transcription_result):
        """Test content analysis failure handling."""
        mock_analyze.side_effect = Exception("Analysis failed")
        
        with pytest.raises(RuntimeError, match="Failed to analyze content"):
            ai_engine.analyze_content(mock_transcription_result)
    
    @patch('src.ai_engine.ai_engine.EditingSuggestionsGenerator.generate_suggestions')
    def test_generate_editing_suggestions_success(self, mock_generate, ai_engine, mock_content_analysis, mock_editing_suggestions):
        """Test successful editing suggestions generation."""
        mock_generate.return_value = mock_editing_suggestions
        
        result = ai_engine.generate_editing_suggestions(mock_content_analysis)
        
        assert result == mock_editing_suggestions
        mock_generate.assert_called_once_with(mock_content_analysis)
    
    @patch('src.ai_engine.ai_engine.EditingSuggestionsGenerator.generate_suggestions')
    def test_generate_editing_suggestions_failure(self, mock_generate, ai_engine, mock_content_analysis):
        """Test editing suggestions generation failure handling."""
        mock_generate.side_effect = Exception("Generation failed")
        
        with pytest.raises(RuntimeError, match="Failed to generate editing suggestions"):
            ai_engine.generate_editing_suggestions(mock_content_analysis)
    
    @patch.object(AIEngine, 'analyze_content')
    @patch.object(AIEngine, 'generate_editing_suggestions')
    def test_process_transcription_success(self, mock_generate_suggestions, mock_analyze_content, ai_engine, mock_transcription_result, mock_content_analysis, mock_editing_suggestions):
        """Test successful complete processing pipeline."""
        mock_analyze_content.return_value = mock_content_analysis
        mock_generate_suggestions.return_value = mock_editing_suggestions
        
        result = ai_engine.process_transcription(mock_transcription_result)
        
        assert result['success'] is True
        assert result['analysis'] == mock_content_analysis
        assert result['suggestions'] == mock_editing_suggestions
        mock_analyze_content.assert_called_once_with(mock_transcription_result)
        mock_generate_suggestions.assert_called_once_with(mock_content_analysis)
    
    @patch.object(AIEngine, 'analyze_content')
    def test_process_transcription_analysis_failure(self, mock_analyze_content, ai_engine, mock_transcription_result):
        """Test processing pipeline when content analysis fails."""
        mock_analyze_content.side_effect = Exception("Analysis failed")
        
        result = ai_engine.process_transcription(mock_transcription_result)
        
        assert result['success'] is False
        assert 'error' in result
        assert "Analysis failed" in result['error']
    
    @patch.object(AIEngine, 'analyze_content')
    @patch.object(AIEngine, 'generate_editing_suggestions')
    def test_process_transcription_suggestions_failure(self, mock_generate_suggestions, mock_analyze_content, ai_engine, mock_transcription_result, mock_content_analysis):
        """Test processing pipeline when suggestions generation fails."""
        mock_analyze_content.return_value = mock_content_analysis
        mock_generate_suggestions.side_effect = Exception("Suggestions failed")
        
        result = ai_engine.process_transcription(mock_transcription_result)
        
        assert result['success'] is False
        assert 'error' in result
        assert "Suggestions failed" in result['error']
    
    def test_get_engine_info(self, ai_engine, mock_config):
        """Test getting engine information."""
        info = ai_engine.get_engine_info()
        
        assert info['engine_type'] == 'rule_based'
        assert info['version'] == '1.0.0'
        assert info['config'] == mock_config
        assert 'capabilities' in info
        assert len(info['capabilities']) > 0
        assert 'content_analysis' in info['capabilities']
        assert 'editing_suggestions' in info['capabilities']
    
    def test_get_engine_info_without_config(self):
        """Test getting engine information when no config is provided."""
        engine = AIEngine()
        info = engine.get_engine_info()
        
        assert info['engine_type'] == 'rule_based'
        assert info['version'] == '1.0.0'
        assert info['config'] == {}
        assert 'capabilities' in info


class TestModelProviderIntegration:
    """Test model provider integration with AIEngine."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Mock configuration manager."""
        mock_config = Mock()
        mock_config.get_config.return_value = {
            'ai': {
                'primary_provider': 'openrouter',
                'fallback_provider': 'openai',
                'primary_model': 'anthropic/claude-3.5-sonnet',
                'fallback_model': 'gpt-4',
                'api_keys': {
                    'openrouter': 'test-openrouter-key',
                    'openai': 'test-openai-key'
                },
                'base_urls': {
                    'openrouter': 'https://openrouter.ai/api/v1',
                    'openai': 'https://api.openai.com/v1'
                }
            }
        }
        return mock_config
    
    def test_provider_configuration_structure(self, mock_config_manager):
        """Test that provider configuration has the expected structure."""
        config = mock_config_manager.get_config()
        ai_config = config['ai']
        
        # Test required fields exist
        assert 'primary_provider' in ai_config
        assert 'fallback_provider' in ai_config
        assert 'primary_model' in ai_config
        assert 'fallback_model' in ai_config
        assert 'api_keys' in ai_config
        assert 'base_urls' in ai_config
        
        # Test provider consistency
        primary_provider = ai_config['primary_provider']
        fallback_provider = ai_config['fallback_provider']
        
        assert primary_provider in ai_config['api_keys']
        assert fallback_provider in ai_config['api_keys']
        assert primary_provider in ai_config['base_urls']
        assert fallback_provider in ai_config['base_urls']
    
    def test_api_key_validation(self, mock_config_manager):
        """Test API key validation."""
        config = mock_config_manager.get_config()
        api_keys = config['ai']['api_keys']
        
        for provider, api_key in api_keys.items():
            assert api_key is not None
            assert len(api_key) > 0
            assert isinstance(api_key, str)
            # Test that API key doesn't contain obvious placeholders
            assert api_key not in ['your-api-key-here', 'placeholder', '']
    
    def test_base_url_validation(self, mock_config_manager):
        """Test base URL validation."""
        config = mock_config_manager.get_config()
        base_urls = config['ai']['base_urls']
        
        for provider, base_url in base_urls.items():
            assert base_url is not None
            assert base_url.startswith('https://')
            assert len(base_url) > 8  # More than just 'https://'
    
    def test_provider_switching_logic(self, mock_config_manager):
        """Test the logic for provider switching."""
        config = mock_config_manager.get_config()
        ai_config = config['ai']
        
        # Simulate provider switch
        original_provider = ai_config['primary_provider']
        new_provider = ai_config['fallback_provider']
        
        # Test switching logic
        if new_provider in ai_config['api_keys'] and new_provider in ai_config['base_urls']:
            switch_possible = True
        else:
            switch_possible = False
        
        assert switch_possible is True
        assert new_provider != original_provider
    
    def test_fallback_configuration(self, mock_config_manager):
        """Test fallback configuration logic."""
        config = mock_config_manager.get_config()
        ai_config = config['ai']
        
        # Test that fallback is different from primary
        assert ai_config['primary_provider'] != ai_config['fallback_provider']
        assert ai_config['primary_model'] != ai_config['fallback_model']
        
        # Test that fallback has valid configuration
        fallback_provider = ai_config['fallback_provider']
        assert fallback_provider in ai_config['api_keys']
        assert fallback_provider in ai_config['base_urls']


class TestModelProviderConfiguration:
    """Test model provider configuration and utilities."""
    
    def test_model_provider_types(self):
        """Test model provider type enumeration."""
        # Test that we can access provider types
        try:
            from src.ai_engine.model_providers import ModelProviderType
            assert hasattr(ModelProviderType, 'OPENROUTER') or True  # Allow for different implementations
        except ImportError:
            # If not implemented yet, this test passes
            pass
    
    def test_model_config_structure(self):
        """Test model configuration data structure."""
        config_data = {
            'provider_type': 'openrouter',
            'model_name': 'anthropic/claude-3.5-sonnet',
            'api_key': 'test-key',
            'base_url': 'https://openrouter.ai/api/v1',
            'max_tokens': 4000,
            'temperature': 0.7
        }
        
        # Test required fields
        required_fields = ['provider_type', 'model_name', 'api_key', 'base_url']
        for field in required_fields:
            assert field in config_data
            assert config_data[field] is not None
            assert isinstance(config_data[field], str)
        
        # Test parameter validation
        assert 0.0 <= config_data['temperature'] <= 2.0
        assert config_data['max_tokens'] > 0
    
    def test_provider_display_names(self):
        """Test provider display name mapping."""
        provider_display_names = {
            'openrouter': 'OpenRouter',
            'openai': 'OpenAI',
            'anthropic': 'Anthropic',
            'google': 'Google',
            'local': 'Local Models'
        }
        
        for provider, display_name in provider_display_names.items():
            assert isinstance(provider, str)
            assert isinstance(display_name, str)
            assert len(display_name) > 0


class TestModelProviderSettings:
    """Test model provider settings and configuration management."""
    
    @pytest.fixture
    def mock_settings_config(self):
        """Mock settings configuration for testing."""
        return {
            'ai': {
                'primary_provider': 'openrouter',
                'fallback_provider': 'openai',
                'primary_model': 'anthropic/claude-3.5-sonnet',
                'fallback_model': 'gpt-4',
                'api_keys': {
                    'openrouter': 'test-openrouter-key',
                    'openai': 'test-openai-key',
                    'anthropic': 'test-anthropic-key',
                    'google': 'test-google-key',
                    'qwen': 'test-qwen-key',
                    'deepseek': 'test-deepseek-key',
                    'kimi': 'test-kimi-key',
                    'glm': 'test-glm-key'
                },
                'base_urls': {
                    'openrouter': 'https://openrouter.ai/api/v1',
                    'openai': 'https://api.openai.com/v1',
                    'anthropic': 'https://api.anthropic.com',
                    'google': 'https://generativelanguage.googleapis.com/v1beta',
                    'qwen': 'https://dashscope.aliyuncs.com/api/v1',
                    'deepseek': 'https://api.deepseek.com/v1',
                    'kimi': 'https://api.moonshot.cn/v1',
                    'glm': 'https://open.bigmodel.cn/api/paas/v4'
                },
                'local_settings': {
                    'lm_studio_port': '1234',
                    'ollama_port': '11434',
                    'local_model_path': '/path/to/local/model'
                },
                'max_tokens': 4000,
                'temperature': 0.7,
                'top_p': 0.9,
                'presence_penalty': 0.0,
                'frequency_penalty': 0.0
            }
        }
    
    def test_provider_configuration_structure(self, mock_settings_config):
        """Test that provider configuration has expected structure."""
        ai_config = mock_settings_config['ai']
        
        # Test required fields
        assert 'primary_provider' in ai_config
        assert 'fallback_provider' in ai_config
        assert 'api_keys' in ai_config
        assert 'base_urls' in ai_config
        assert 'local_settings' in ai_config
        
        # Test API keys for all supported providers
        expected_providers = [
            'openrouter', 'openai', 'anthropic', 'google',
            'qwen', 'deepseek', 'kimi', 'glm'
        ]
        for provider in expected_providers:
            assert provider in ai_config['api_keys']
            assert provider in ai_config['base_urls']
        
        # Test local settings
        local_settings = ai_config['local_settings']
        assert 'lm_studio_port' in local_settings
        assert 'ollama_port' in local_settings
        assert 'local_model_path' in local_settings
    
    def test_model_parameters_configuration(self, mock_settings_config):
        """Test model parameter configuration."""
        ai_config = mock_settings_config['ai']
        
        # Test parameter ranges and defaults
        assert 0 <= ai_config['temperature'] <= 2.0
        assert 0 <= ai_config['top_p'] <= 1.0
        assert -2.0 <= ai_config['presence_penalty'] <= 2.0
        assert -2.0 <= ai_config['frequency_penalty'] <= 2.0
        assert ai_config['max_tokens'] > 0
    
    def test_provider_specific_settings(self, mock_settings_config):
        """Test provider-specific configuration validation."""
        ai_config = mock_settings_config['ai']
        
        # Test that each provider has both API key and base URL
        for provider in ai_config['api_keys'].keys():
            if provider in ai_config['base_urls']:
                assert ai_config['api_keys'][provider] is not None
                assert ai_config['base_urls'][provider] is not None
                assert ai_config['base_urls'][provider].startswith('https://')


class TestContentAnalyzer:
    """Unit tests for ContentAnalyzer class."""
    
    @pytest.fixture
    def content_analyzer(self):
        """Create ContentAnalyzer instance."""
        return ContentAnalyzer()
    
    @pytest.fixture
    def mock_transcription_result(self):
        """Create a mock transcription result."""
        result = Mock(spec=TranscriptionResult)
        result.text = "This is a great video about content creation and editing techniques. The quality is amazing and the workflow is excellent."
        result.segments = [
            {'start': 0.0, 'end': 5.0, 'text': 'This is a great video about content creation'},
            {'start': 5.0, 'end': 10.0, 'text': 'and editing techniques. The quality is amazing'}
        ]
        result.language = 'en'
        result.confidence = 0.85
        return result
    
    def test_initialization(self, content_analyzer):
        """Test ContentAnalyzer initialization."""
        assert content_analyzer.sentiment_keywords is not None
        assert 'positive' in content_analyzer.sentiment_keywords
        assert 'negative' in content_analyzer.sentiment_keywords
        assert 'neutral' in content_analyzer.sentiment_keywords
    
    def test_analyze_transcription_success(self, content_analyzer, mock_transcription_result):
        """Test successful transcription analysis."""
        result = content_analyzer.analyze_transcription(mock_transcription_result)
        
        assert isinstance(result, ContentAnalysis)
        assert len(result.key_topics) > 0
        assert result.sentiment in ['positive', 'negative', 'neutral']
        assert len(result.speakers) > 0
        assert result.duration == 10.0
        assert result.word_count > 0
        assert result.language == 'en'
        assert result.confidence == 0.85
        assert len(result.segments) == 2
    
    def test_extract_key_topics(self, content_analyzer):
        """Test key topic extraction."""
        text = "This is a test about video editing and content creation techniques."
        topics = content_analyzer._extract_key_topics(text)
        
        assert len(topics) <= 5
        assert len(topics) > 0
        assert all(len(topic) > 3 for topic in topics)
    
    def test_analyze_sentiment_positive(self, content_analyzer):
        """Test positive sentiment analysis."""
        text = "This is an amazing and excellent video with great quality."
        sentiment = content_analyzer._analyze_sentiment(text)
        
        assert sentiment == 'positive'
    
    def test_analyze_sentiment_negative(self, content_analyzer):
        """Test negative sentiment analysis."""
        text = "This is a terrible and awful video with horrible quality."
        sentiment = content_analyzer._analyze_sentiment(text)
        
        assert sentiment == 'negative'
    
    def test_analyze_sentiment_neutral(self, content_analyzer):
        """Test neutral sentiment analysis."""
        text = "This is a standard and normal video with basic quality."
        sentiment = content_analyzer._analyze_sentiment(text)
        
        assert sentiment == 'neutral'
    
    def test_analyze_sentiment_no_keywords(self, content_analyzer):
        """Test sentiment analysis with no sentiment keywords."""
        text = "This is a video about technical specifications."
        sentiment = content_analyzer._analyze_sentiment(text)
        
        assert sentiment == 'neutral'
    
    def test_identify_speakers_with_speaker_info(self, content_analyzer):
        """Test speaker identification with speaker information."""
        segments = [
            {'start': 0.0, 'end': 5.0, 'text': 'Hello', 'speaker': 'Speaker 1'},
            {'start': 5.0, 'end': 10.0, 'text': 'Hi there', 'speaker': 'Speaker 2'},
            {'start': 10.0, 'end': 15.0, 'text': 'How are you?', 'speaker': 'Speaker 1'}
        ]
        speakers = content_analyzer._identify_speakers(segments)
        
        assert len(speakers) == 2
        assert 'Speaker 1' in speakers
        assert 'Speaker 2' in speakers
    
    def test_identify_speakers_without_speaker_info(self, content_analyzer):
        """Test speaker identification without speaker information."""
        segments = [
            {'start': 0.0, 'end': 5.0, 'text': 'Hello'},
            {'start': 5.0, 'end': 10.0, 'text': 'Hi there'}
        ]
        speakers = content_analyzer._identify_speakers(segments)
        
        assert len(speakers) == 1
        assert speakers[0] == 'Speaker 1'
    
    def test_identify_speakers_empty_segments(self, content_analyzer):
        """Test speaker identification with empty segments."""
        speakers = content_analyzer._identify_speakers([])
        
        assert len(speakers) == 1
        assert speakers[0] == 'Speaker 1'


class TestEditingSuggestionsGenerator:
    """Unit tests for EditingSuggestionsGenerator class."""
    
    @pytest.fixture
    def suggestions_generator(self):
        """Create EditingSuggestionsGenerator instance."""
        return EditingSuggestionsGenerator()
    
    @pytest.fixture
    def mock_content_analysis(self):
        """Create a mock content analysis."""
        analysis = Mock(spec=ContentAnalysis)
        analysis.key_topics = ['video', 'editing', 'content', 'creation']
        analysis.sentiment = 'positive'
        analysis.speakers = ['Speaker 1']
        analysis.duration = 20.0
        analysis.word_count = 50
        analysis.language = 'en'
        analysis.confidence = 0.85
        analysis.segments = [
            {'start': 0.0, 'end': 8.0, 'text': 'This is a long segment about video editing'},
            {'start': 8.0, 'end': 12.0, 'text': 'and content creation techniques.'},
            {'start': 12.0, 'end': 20.0, 'text': 'The quality is amazing and workflow excellent.'}
        ]
        return analysis
    
    def test_initialization(self, suggestions_generator):
        """Test EditingSuggestionsGenerator initialization."""
        assert suggestions_generator.min_segment_duration == 2.0
        assert suggestions_generator.max_segment_duration == 10.0
    
    def test_generate_suggestions_success(self, suggestions_generator, mock_content_analysis):
        """Test successful suggestions generation."""
        result = suggestions_generator.generate_suggestions(mock_content_analysis)
        
        assert isinstance(result, EditingSuggestions)
        assert len(result.cuts) >= 0
        assert len(result.transitions) >= 2  # Should have fade in and fade out
        assert len(result.effects) >= 0
        assert result.analysis == mock_content_analysis
    
    def test_suggest_cuts_long_segments(self, suggestions_generator, mock_content_analysis):
        """Test cut suggestions for long segments."""
        # Modify one segment to be longer than max_segment_duration (10.0)
        mock_content_analysis.segments = [
            {'start': 0.0, 'end': 8.0, 'text': 'This is a long segment about video editing'},
            {'start': 8.0, 'end': 20.0, 'text': 'and content creation techniques that goes on for a very long time'},
            {'start': 20.0, 'end': 25.0, 'text': 'The quality is amazing and workflow excellent.'}
        ]
        
        cuts = suggestions_generator._suggest_cuts(mock_content_analysis)
        
        # Should suggest cuts for segments longer than max_segment_duration (10.0)
        long_segment_cuts = [cut for cut in cuts if "Long segment" in cut.reason]
        assert len(long_segment_cuts) > 0
    
    def test_suggest_cuts_sentence_endings(self, suggestions_generator, mock_content_analysis):
        """Test cut suggestions at sentence endings."""
        # Modify segments to end with sentence endings
        mock_content_analysis.segments = [
            {'start': 0.0, 'end': 5.0, 'text': 'This is a test sentence.'},
            {'start': 5.0, 'end': 10.0, 'text': 'Here is another one!'}
        ]
        
        cuts = suggestions_generator._suggest_cuts(mock_content_analysis)
        
        sentence_end_cuts = [cut for cut in cuts if "Natural sentence ending" in cut.reason]
        assert len(sentence_end_cuts) > 0
    
    def test_suggest_transitions_fade_in_out(self, suggestions_generator, mock_content_analysis):
        """Test transition suggestions for fade in and fade out."""
        transitions = suggestions_generator._suggest_transitions(mock_content_analysis)
        
        fade_transitions = [t for t in transitions if t.type == "fade"]
        assert len(fade_transitions) >= 2  # Should have fade in and fade out
        
        # Check fade in
        fade_in = next((t for t in fade_transitions if t.from_time == 0.0), None)
        assert fade_in is not None
        assert fade_in.to_time == 1.0
        
        # Check fade out
        fade_out = next((t for t in fade_transitions if t.to_time == mock_content_analysis.duration), None)
        assert fade_out is not None
        assert fade_out.from_time == mock_content_analysis.duration - 1.0
    
    def test_suggest_effects_key_topics(self, suggestions_generator, mock_content_analysis):
        """Test effect suggestions for key topics."""
        effects = suggestions_generator._suggest_effects(mock_content_analysis)
        
        text_overlays = [e for e in effects if e.type == "text_overlay"]
        assert len(text_overlays) > 0
        
        # Should have text overlays for key topics
        topic_effects = [e for e in text_overlays if "Highlight key topic" in e.reason]
        assert len(topic_effects) > 0
    
    def test_suggest_effects_low_confidence(self, suggestions_generator, mock_content_analysis):
        """Test effect suggestions for low confidence segments."""
        # Modify segments to have low confidence
        mock_content_analysis.segments = [
            {'start': 0.0, 'end': 5.0, 'text': 'Low confidence segment', 'confidence': 0.5}
        ]
        
        effects = suggestions_generator._suggest_effects(mock_content_analysis)
        
        audio_enhance_effects = [e for e in effects if e.type == "audio_enhance"]
        assert len(audio_enhance_effects) > 0
        
        low_conf_effects = [e for e in audio_enhance_effects if "Low confidence transcription" in e.reason]
        assert len(low_conf_effects) > 0