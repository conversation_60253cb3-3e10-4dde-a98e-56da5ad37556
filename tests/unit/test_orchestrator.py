import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from pathlib import Path

# Add the src directory to the path for imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Import the classes to test
from src.core.orchestrator import AIOrchestrator
from src.core.config_manager import ConfigManager
from src.transcription import TranscriptionEngine, TranscriptionConfig, TranscriptionResult
from src.resolve_bridge import DaVinciResolveBridge
from src.ai_engine import AIEngine


class TestAIOrchestrator:
    """Unit tests for AIOrchestrator class."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create a mock configuration manager."""
        config_manager = Mock(spec=ConfigManager)
        config_manager.get.side_effect = lambda key, default=None: {
            'logging.level': 'INFO',
            'transcription': {
                'model_size': 'base',
                'language': 'en',
                'device': 'auto',
                'compute_type': 'float16',
                'optimize_for_apple_silicon': True
            },
            'ai_engine': {'engine_type': 'rule_based'},
            'transcription.optimize_for_apple_silicon': True
        }.get(key, default)
        return config_manager
    
    @pytest.fixture
    def mock_transcription_result(self):
        """Create a mock transcription result."""
        result = Mock(spec=TranscriptionResult)
        result.text = "This is a test transcription about video editing."
        result.segments = [
            {'start': 0.0, 'end': 5.0, 'text': 'This is a test transcription'},
            {'start': 5.0, 'end': 10.0, 'text': 'about video editing'}
        ]
        result.language = 'en'
        result.confidence = 0.85
        return result
    
    @pytest.fixture
    def orchestrator(self, mock_config_manager):
        """Create AIOrchestrator instance with mock config manager."""
        return AIOrchestrator(mock_config_manager)
    
    def test_initialization_with_config_manager(self, mock_config_manager):
        """Test AIOrchestrator initialization with config manager."""
        orchestrator = AIOrchestrator(mock_config_manager)
        assert orchestrator.config_manager == mock_config_manager
        assert orchestrator.transcription_engine is None
        assert orchestrator.resolve_bridge is None
        assert orchestrator.ai_engine is None
    
    def test_initialization_without_config_manager(self):
        """Test AIOrchestrator initialization without config manager."""
        orchestrator = AIOrchestrator()
        assert isinstance(orchestrator.config_manager, ConfigManager)
        assert orchestrator.transcription_engine is None
        assert orchestrator.resolve_bridge is None
        assert orchestrator.ai_engine is None
    
    @patch('src.core.orchestrator.logging.basicConfig')
    def test_setup_logging(self, mock_basic_config, orchestrator):
        """Test logging setup."""
        orchestrator._setup_logging()
        mock_basic_config.assert_called_once()
        
        # Verify logging level is set correctly
        call_args = mock_basic_config.call_args
        assert call_args[1]['level'] == 20  # INFO level
    
    @pytest.mark.asyncio
    @patch.object(AIOrchestrator, '_get_transcription_config')
    @patch('src.core.orchestrator.TranscriptionEngine')
    @patch('src.core.orchestrator.DaVinciResolveBridge')
    @patch('src.core.orchestrator.AIEngine')
    async def test_initialize_success(self, mock_ai_engine_class, mock_resolve_bridge_class, mock_transcription_engine_class, mock_get_config, orchestrator):
        """Test successful initialization of all components."""
        # Setup mocks
        mock_get_config.return_value = Mock(spec=TranscriptionConfig)
        mock_transcription_engine = Mock(spec=TranscriptionEngine)
        mock_transcription_engine_class.return_value = mock_transcription_engine
        
        mock_resolve_bridge = Mock(spec=DaVinciResolveBridge)
        mock_resolve_bridge.connect = AsyncMock(return_value=True)
        mock_resolve_bridge_class.return_value = mock_resolve_bridge
        
        mock_ai_engine = Mock(spec=AIEngine)
        mock_ai_engine_class.return_value = mock_ai_engine
        
        # Execute initialization
        await orchestrator.initialize()
        
        # Verify components are initialized
        assert orchestrator.transcription_engine == mock_transcription_engine
        assert orchestrator.resolve_bridge == mock_resolve_bridge
        assert orchestrator.ai_engine == mock_ai_engine
        
        # Verify component initialization calls
        mock_get_config.assert_called_once()
        mock_transcription_engine_class.assert_called_once_with(mock_get_config.return_value)
        mock_resolve_bridge_class.assert_called_once()
        mock_resolve_bridge.connect.assert_awaited_once()
        mock_ai_engine_class.assert_called_once_with({'engine_type': 'rule_based'})
    
    def test_get_transcription_config_with_apple_silicon(self, orchestrator):
        """Test transcription config with Apple Silicon optimization."""
        config = orchestrator._get_transcription_config()
        
        assert isinstance(config, TranscriptionConfig)
        assert config.optimize_for_apple_silicon is True
        assert config.device == 'auto'
        assert config.compute_type == 'float16'
    
    def test_get_transcription_config_without_apple_silicon(self, mock_config_manager):
        """Test transcription config without Apple Silicon optimization."""
        # Modify config manager to return False for Apple Silicon optimization
        mock_config_manager.get.side_effect = lambda key, default=None: {
            'logging.level': 'INFO',
            'transcription': {
                'model_size': 'base',
                'language': 'en',
                'device': 'cpu',
                'compute_type': 'int8',
                'optimize_for_apple_silicon': False
            },
            'ai_engine': {'engine_type': 'rule_based'},
            'transcription.optimize_for_apple_silicon': False
        }.get(key, default)
        
        orchestrator = AIOrchestrator(mock_config_manager)
        config = orchestrator._get_transcription_config()
        
        assert isinstance(config, TranscriptionConfig)
        assert config.optimize_for_apple_silicon is True  # Should be forced to True
        assert config.device == 'auto'
        assert config.compute_type == 'float16'
    
    @pytest.mark.asyncio
    @patch.object(AIOrchestrator, 'transcribe_audio')
    async def test_process_timeline_success(self, mock_transcribe_audio, orchestrator, mock_transcription_result):
        """Test successful timeline processing."""
        # Setup mocks
        mock_resolve_bridge = Mock(spec=DaVinciResolveBridge)
        mock_resolve_bridge.get_current_timeline = AsyncMock(return_value="/path/to/timeline.drt")
        mock_resolve_bridge.extract_audio = AsyncMock(return_value="/path/to/audio.wav")
        orchestrator.resolve_bridge = mock_resolve_bridge
        
        mock_transcribe_audio.return_value = mock_transcription_result
        
        # Mock Path.exists and Path.unlink
        with patch.object(Path, 'exists', return_value=True):
            with patch.object(Path, 'unlink', return_value=None):
                result = await orchestrator.process_timeline()
        
        # Verify result
        assert result['timeline_path'] == "/path/to/timeline.drt"
        assert result['transcription'] == mock_transcription_result
        assert result['success'] is True
        
        # Verify method calls
        mock_resolve_bridge.get_current_timeline.assert_awaited_once()
        mock_resolve_bridge.extract_audio.assert_awaited_once_with("/path/to/timeline.drt")
        mock_transcribe_audio.assert_awaited_once_with("/path/to/audio.wav")
    
    @pytest.mark.asyncio
    async def test_process_timeline_with_specified_path(self, orchestrator):
        """Test timeline processing with specified path."""
        # Setup mocks
        mock_resolve_bridge = Mock(spec=DaVinciResolveBridge)
        mock_resolve_bridge.extract_audio = AsyncMock(return_value="/path/to/audio.wav")
        orchestrator.resolve_bridge = mock_resolve_bridge
        
        with patch.object(AIOrchestrator, 'transcribe_audio', new_callable=AsyncMock) as mock_transcribe:
            with patch.object(Path, 'exists', return_value=True):
                with patch.object(Path, 'unlink', return_value=None):
                    result = await orchestrator.process_timeline("/custom/timeline.drt")
        
        assert result['timeline_path'] == "/custom/timeline.drt"
        mock_resolve_bridge.extract_audio.assert_awaited_once_with("/custom/timeline.drt")
        # Should not call get_current_timeline when path is specified
        mock_resolve_bridge.get_current_timeline.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_process_timeline_no_timeline_available(self, orchestrator):
        """Test timeline processing when no timeline is available."""
        # Setup mocks
        mock_resolve_bridge = Mock(spec=DaVinciResolveBridge)
        mock_resolve_bridge.get_current_timeline = AsyncMock(return_value=None)
        orchestrator.resolve_bridge = mock_resolve_bridge
        
        result = await orchestrator.process_timeline()
        
        assert result['success'] is False
        assert 'error' in result
        assert "No timeline specified" in result['error']
    
    @pytest.mark.asyncio
    async def test_process_timeline_extraction_failure(self, orchestrator):
        """Test timeline processing when audio extraction fails."""
        # Setup mocks
        mock_resolve_bridge = Mock(spec=DaVinciResolveBridge)
        mock_resolve_bridge.get_current_timeline = AsyncMock(return_value="/path/to/timeline.drt")
        mock_resolve_bridge.extract_audio = AsyncMock(side_effect=Exception("Extraction failed"))
        orchestrator.resolve_bridge = mock_resolve_bridge
        
        result = await orchestrator.process_timeline()
        
        assert result['success'] is False
        assert 'error' in result
        assert "Extraction failed" in result['error']
    
    @pytest.mark.asyncio
    async def test_transcribe_audio_success(self, orchestrator, mock_transcription_result):
        """Test successful audio transcription."""
        # Setup mocks
        mock_transcription_engine = Mock(spec=TranscriptionEngine)
        mock_transcription_engine.transcribe_audio.return_value = mock_transcription_result
        orchestrator.transcription_engine = mock_transcription_engine
        
        result = await orchestrator.transcribe_audio("/path/to/audio.wav")
        
        assert result == mock_transcription_result
        mock_transcription_engine.transcribe_audio.assert_called_once_with("/path/to/audio.wav")
    
    @pytest.mark.asyncio
    async def test_transcribe_audio_no_result(self, orchestrator):
        """Test audio transcription when no result is returned."""
        # Setup mocks
        mock_transcription_engine = Mock(spec=TranscriptionEngine)
        mock_transcription_engine.transcribe_audio.return_value = None
        orchestrator.transcription_engine = mock_transcription_engine
        
        with pytest.raises(RuntimeError, match="Transcription failed"):
            await orchestrator.transcribe_audio("/path/to/audio.wav")
    
    @pytest.mark.asyncio
    async def test_transcribe_audio_exception(self, orchestrator):
        """Test audio transcription when an exception occurs."""
        # Setup mocks
        mock_transcription_engine = Mock(spec=TranscriptionEngine)
        mock_transcription_engine.transcribe_audio.side_effect = Exception("Transcription error")
        orchestrator.transcription_engine = mock_transcription_engine
        
        with pytest.raises(RuntimeError, match="Failed to transcribe audio"):
            await orchestrator.transcribe_audio("/path/to/audio.wav")
    
    @pytest.mark.asyncio
    async def test_analyze_content_success(self, orchestrator):
        """Test successful content analysis."""
        # Setup mocks
        mock_ai_engine = Mock(spec=AIEngine)
        mock_analysis_result = Mock()
        mock_analysis_result.key_topics = ['video', 'editing']
        mock_analysis_result.sentiment = 'positive'
        mock_analysis_result.speakers = ['Speaker 1']
        mock_analysis_result.cuts = [Mock(time=5.0, reason="Test cut")]
        mock_analysis_result.transitions = [Mock(time=0.0, type="fade")]
        mock_analysis_result.effects = [Mock(time=2.0, type="text_overlay")]
        
        mock_ai_engine.analyze_content.return_value = mock_analysis_result
        mock_ai_engine.generate_editing_suggestions.return_value = mock_analysis_result
        orchestrator.ai_engine = mock_ai_engine
        
        result = await orchestrator.analyze_content("Test transcription", {"duration": 60})
        
        assert result['key_topics'] == ['video', 'editing']
        assert result['sentiment'] == 'positive'
        assert result['speakers'] == ['Speaker 1']
        assert 'editing_suggestions' in result
        assert 'cuts' in result['editing_suggestions']
        assert 'transitions' in result['editing_suggestions']
        assert 'effects' in result['editing_suggestions']
        
        mock_ai_engine.analyze_content.assert_called_once_with("Test transcription")
        mock_ai_engine.generate_editing_suggestions.assert_called_once_with(mock_analysis_result, {"duration": 60})
    
    @pytest.mark.asyncio
    async def test_analyze_content_no_ai_engine(self, orchestrator):
        """Test content analysis when AI engine is not initialized."""
        orchestrator.ai_engine = None
        
        with pytest.raises(RuntimeError, match="AI engine not initialized"):
            await orchestrator.analyze_content("Test transcription", {})
    
    @pytest.mark.asyncio
    async def test_analyze_content_failure(self, orchestrator):
        """Test content analysis when analysis fails."""
        # Setup mocks
        mock_ai_engine = Mock(spec=AIEngine)
        mock_ai_engine.analyze_content.side_effect = Exception("Analysis failed")
        orchestrator.ai_engine = mock_ai_engine
        
        with pytest.raises(RuntimeError, match="Content analysis failed"):
            await orchestrator.analyze_content("Test transcription", {})
    
    @pytest.mark.asyncio
    async def test_get_timeline_info(self, orchestrator):
        """Test getting timeline information."""
        # Setup mocks
        mock_resolve_bridge = Mock(spec=DaVinciResolveBridge)
        mock_timeline_info = {"name": "Test Timeline", "duration": 120}
        mock_resolve_bridge.get_timeline_info = AsyncMock(return_value=mock_timeline_info)
        orchestrator.resolve_bridge = mock_resolve_bridge
        
        result = await orchestrator.get_timeline_info()
        
        assert result == mock_timeline_info
        mock_resolve_bridge.get_timeline_info.assert_awaited_once()
    
    @pytest.mark.asyncio
    async def test_cleanup(self, orchestrator):
        """Test cleanup of resources."""
        # Setup mocks
        mock_transcription_engine = Mock(spec=TranscriptionEngine)
        mock_resolve_bridge = Mock(spec=DaVinciResolveBridge)
        mock_resolve_bridge.disconnect = AsyncMock()
        mock_ai_engine = Mock(spec=AIEngine)
        mock_ai_engine.cleanup = Mock()
        
        orchestrator.transcription_engine = mock_transcription_engine
        orchestrator.resolve_bridge = mock_resolve_bridge
        orchestrator.ai_engine = mock_ai_engine
        
        await orchestrator.cleanup()
        
        mock_transcription_engine.cleanup.assert_called_once()
        mock_resolve_bridge.disconnect.assert_awaited_once()
        mock_ai_engine.cleanup.assert_called_once()
    
    def test_get_status_all_initialized(self, orchestrator):
        """Test getting status when all components are initialized."""
        # Setup mocks
        mock_resolve_bridge = Mock(spec=DaVinciResolveBridge)
        mock_resolve_bridge.connected = True
        
        orchestrator.transcription_engine = Mock(spec=TranscriptionEngine)
        orchestrator.resolve_bridge = mock_resolve_bridge
        orchestrator.ai_engine = Mock(spec=AIEngine)
        
        status = orchestrator.get_status()
        
        assert status['transcription_engine'] is True
        assert status['resolve_bridge'] is True
        assert status['resolve_connected'] is True
        assert status['ai_engine'] is True
        assert status['config_manager'] is True
        assert status['apple_silicon_optimized'] is True
    
    def test_get_status_not_initialized(self, orchestrator):
        """Test getting status when components are not initialized."""
        status = orchestrator.get_status()
        
        assert status['transcription_engine'] is False
        assert status['resolve_bridge'] is False
        assert status['resolve_connected'] is False
        assert status['ai_engine'] is False
        assert status['config_manager'] is True
        assert status['apple_silicon_optimized'] is True
    
    @pytest.mark.asyncio
    async def test_integration_full_workflow(self, orchestrator, mock_transcription_result):
        """Test complete integration workflow."""
        # Setup all mocks for a complete workflow
        mock_resolve_bridge = Mock(spec=DaVinciResolveBridge)
        mock_resolve_bridge.get_current_timeline = AsyncMock(return_value="/path/to/timeline.drt")
        mock_resolve_bridge.extract_audio = AsyncMock(return_value="/path/to/audio.wav")
        mock_resolve_bridge.get_timeline_info = AsyncMock(return_value={"duration": 60, "name": "Test"})
        
        mock_transcription_engine = Mock(spec=TranscriptionEngine)
        mock_transcription_engine.transcribe_audio.return_value = mock_transcription_result
        
        mock_ai_engine = Mock(spec=AIEngine)
        mock_analysis_result = Mock()
        mock_analysis_result.key_topics = ['video', 'editing']
        mock_analysis_result.sentiment = 'positive'
        mock_analysis_result.speakers = ['Speaker 1']
        mock_analysis_result.cuts = [Mock(time=5.0, reason="Test cut")]
        mock_analysis_result.transitions = [Mock(time=0.0, type="fade")]
        mock_analysis_result.effects = [Mock(time=2.0, type="text_overlay")]
        
        mock_ai_engine.analyze_content.return_value = mock_analysis_result
        mock_ai_engine.generate_editing_suggestions.return_value = mock_analysis_result
        
        orchestrator.resolve_bridge = mock_resolve_bridge
        orchestrator.transcription_engine = mock_transcription_engine
        orchestrator.ai_engine = mock_ai_engine
        
        # Execute complete workflow
        with patch.object(Path, 'exists', return_value=True):
            with patch.object(Path, 'unlink', return_value=None):
                # Process timeline
                timeline_result = await orchestrator.process_timeline()
                assert timeline_result['success'] is True
                
                # Analyze content
                analysis_result = await orchestrator.analyze_content(
                    timeline_result['transcription'].text,
                    await orchestrator.get_timeline_info()
                )
                assert 'key_topics' in analysis_result
                assert 'editing_suggestions' in analysis_result
        
        # Verify all components were used
        mock_resolve_bridge.get_current_timeline.assert_awaited()
        mock_resolve_bridge.extract_audio.assert_awaited()
        mock_transcription_engine.transcribe_audio.assert_called_once()
        mock_ai_engine.analyze_content.assert_called_once()
        mock_ai_engine.generate_editing_suggestions.assert_called_once()