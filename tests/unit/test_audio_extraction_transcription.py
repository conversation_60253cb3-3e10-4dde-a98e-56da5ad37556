import pytest
import asyncio
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from pathlib import Path

# Add the src directory to the path for imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Import the classes to test
from src.core.workflow_manager import WorkflowManager, WorkflowStep, WorkflowStatus, WorkflowResult
from src.core.orchestrator import AIOrchestrator
from src.resolve_bridge.resolve_bridge import DaVinciResolveBridge
from src.transcription.transcription_engine import TranscriptionEngine, TranscriptionResult


class TestAudioExtractionTranscription:
    """Test suite for audio extraction and transcription functionality."""
    
    @pytest.fixture
    def mock_orchestrator(self):
        """Create a mock orchestrator."""
        orchestrator = Mock(spec=AIOrchestrator)
        orchestrator.extract_audio = AsyncMock()
        orchestrator.transcribe_audio = AsyncMock()
        orchestrator.analyze_content = AsyncMock()
        orchestrator.get_timeline_info = AsyncMock()
        
        # Mock the resolve_bridge with proper async methods
        orchestrator.resolve_bridge = Mock()
        orchestrator.resolve_bridge.extract_audio = AsyncMock()
        orchestrator.resolve_bridge.get_current_timeline = AsyncMock()
        
        return orchestrator

    @pytest.fixture
    def mock_resolve_bridge(self):
        """Create a mock resolve bridge."""
        bridge = Mock(spec=DaVinciResolveBridge)
        bridge.extract_audio = AsyncMock()
        bridge.is_connected = Mock(return_value=True)
        return bridge
    
    @pytest.fixture
    def mock_config_manager(self):
        """Create a mock config manager."""
        config_manager = Mock()
        config_manager.get = Mock(return_value="INFO")
        return config_manager
    
    @pytest.fixture
    def workflow_manager(self, mock_orchestrator, mock_config_manager):
        """Create a workflow manager with mock orchestrator and config manager."""
        return WorkflowManager(mock_orchestrator, mock_config_manager)

    @pytest.fixture
    def sample_transcription_result(self):
        """Create a sample transcription result."""
        mock_result = Mock()
        mock_result.text = "This is a sample transcription of the audio content."
        mock_result.confidence = 0.95
        mock_result.language = "en"
        mock_result.segments = [
            {"start": 0.0, "end": 5.0, "text": "This is a sample"},
            {"start": 5.0, "end": 10.0, "text": "transcription of the audio content."}
        ]
        return mock_result

    @pytest.fixture
    def sample_timeline_info(self):
        """Create sample timeline information."""
        return {
            "name": "Test Timeline",
            "duration": 120.5,
            "fps": 24,
            "resolution": "1920x1080"
        }

    @pytest.mark.asyncio
    async def test_create_timeline_analysis_workflow(self, workflow_manager):
        """Test creation of timeline analysis workflow."""
        workflow = await workflow_manager.create_timeline_analysis_workflow()
        
        assert isinstance(workflow, list)
        assert len(workflow) > 0
        
        # Check that all steps are WorkflowStep instances
        for step in workflow:
            assert isinstance(step, WorkflowStep)
            assert hasattr(step, 'name')
            assert hasattr(step, 'description')
            assert hasattr(step, 'function')
            
        # Check for expected step names
        step_names = [step.name for step in workflow]
        assert "extract_audio" in step_names
        assert "transcribe_audio" in step_names

    @pytest.mark.asyncio
    async def test_execute_timeline_analysis_success(self, workflow_manager, mock_orchestrator, sample_transcription_result):
        """Test successful execution of timeline analysis."""
        # Mock the orchestrator methods
        mock_orchestrator.resolve_bridge.get_current_timeline.return_value = "/path/to/timeline"
        mock_orchestrator.resolve_bridge.extract_audio.return_value = "/tmp/test_audio.wav"
        mock_orchestrator.transcribe_audio.return_value = sample_transcription_result
        mock_orchestrator.analyze_content.return_value = {
            "success": True,
            "analysis": {"sentiment": "positive", "topics": ["technology", "AI"]}
        }
        mock_orchestrator.get_timeline_info.return_value = {
            "name": "Test Timeline",
            "duration": 120.5
        }
        
        result = await workflow_manager.execute_timeline_analysis()
        
        assert isinstance(result, WorkflowResult)
        assert result.success
        assert result.status == WorkflowStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_execute_timeline_analysis_audio_extraction_failure(self, workflow_manager, mock_orchestrator):
        """Test timeline analysis when audio extraction fails."""
        # Mock audio extraction failure
        mock_orchestrator.resolve_bridge.extract_audio.side_effect = Exception("Audio extraction failed")
        
        result = await workflow_manager.execute_timeline_analysis()
        
        assert isinstance(result, WorkflowResult)
        assert not result.success
        assert result.status == WorkflowStatus.FAILED
        assert len(result.errors) > 0

    @pytest.mark.asyncio
    async def test_extract_timeline_audio_success(self, workflow_manager, mock_orchestrator):
        """Test successful audio extraction from timeline."""
        # Mock successful audio extraction
        mock_orchestrator.resolve_bridge.get_current_timeline.return_value = "/path/to/timeline"
        mock_orchestrator.resolve_bridge.extract_audio.return_value = "/tmp/test_audio.wav"
        
        result = await workflow_manager._extract_timeline_audio()
        
        assert result["audio_path"] == "/tmp/test_audio.wav"
        assert result["timeline_path"] == "/path/to/timeline"

    @pytest.mark.asyncio
    async def test_transcribe_timeline_audio_success(self, workflow_manager, mock_orchestrator, sample_transcription_result):
        """Test successful audio transcription."""
        # Mock successful transcription
        mock_orchestrator.transcribe_audio.return_value = sample_transcription_result
        
        step_data = {"extract_audio": {"audio_path": "/tmp/test_audio.wav"}}
        result = await workflow_manager._transcribe_timeline_audio(step_data)
        
        assert "transcribe_audio" in result
        assert result["transcribe_audio"]["text"] == "This is a sample transcription of the audio content."

    @pytest.mark.asyncio
    async def test_transcribe_timeline_audio_no_audio_path(self, workflow_manager):
        """Test transcription when no audio path is provided."""
        step_data = {}
        
        with pytest.raises(ValueError, match="No audio file path provided from previous step"):
            await workflow_manager._transcribe_timeline_audio(step_data)

    @pytest.mark.asyncio
    async def test_analyze_transcription_success(self, workflow_manager, mock_orchestrator, sample_timeline_info):
        """Test successful content analysis."""
        # Mock successful analysis
        mock_orchestrator.analyze_content.return_value = {
            "success": True,
            "analysis": {"sentiment": "positive", "topics": ["technology", "AI"]}
        }
        mock_orchestrator.get_timeline_info.return_value = sample_timeline_info
        
        step_data = {"transcribe_audio": Mock(text="Test transcription")}
        result = await workflow_manager._analyze_transcription(step_data)
        
        assert result["success"] is True
        assert "analysis" in result
        assert result["analysis"]["sentiment"] == "positive"

    @pytest.mark.asyncio
    async def test_analyze_transcription_no_transcription(self, workflow_manager):
        """Test analysis when no transcription is provided."""
        step_data = {}
        
        with pytest.raises(ValueError, match="No transcription text available"):
            await workflow_manager._analyze_transcription(step_data)

    @pytest.mark.asyncio
    async def test_workflow_step_creation(self, workflow_manager):
        """Test creating individual workflow steps."""
        async def dummy_function():
            return {"success": True}
        
        step = WorkflowStep(
            name="test_step",
            description="Test step",
            function=dummy_function,
            timeout=30.0
        )
        
        assert step.name == "test_step"
        assert step.description == "Test step"
        assert step.function == dummy_function
        assert step.timeout == 30.0

    @pytest.mark.asyncio
    async def test_workflow_execution_with_failure(self, workflow_manager):
        """Test workflow execution when a step fails."""
        async def failing_function():
            raise Exception("Step failed")
        
        steps = [
            WorkflowStep(
                name="failing_step",
                description="This step will fail",
                function=failing_function
            )
        ]
        
        result = await workflow_manager.execute_workflow(steps, "test_workflow")
        
        assert isinstance(result, WorkflowResult)
        assert not result.success
        assert result.status == WorkflowStatus.FAILED
        assert len(result.errors) > 0

    def test_workflow_manager_initialization(self, mock_orchestrator, mock_config_manager):
        """Test workflow manager initialization."""
        wm = WorkflowManager(mock_orchestrator, mock_config_manager)
        
        assert wm.orchestrator == mock_orchestrator
        assert wm.config_manager == mock_config_manager
        assert wm.active_workflows == {}
        assert wm.workflow_history == []

    @pytest.mark.asyncio
    async def test_workflow_status_tracking(self, workflow_manager):
        """Test that workflow status is properly tracked."""
        # Create a workflow
        workflow = await workflow_manager.create_timeline_analysis_workflow()
        
        # Check that workflow has proper structure
        assert len(workflow) > 0
        assert all(isinstance(step, WorkflowStep) for step in workflow)
        
        # Check that workflow manager has tracking attributes
        assert hasattr(workflow_manager, 'active_workflows')
        assert hasattr(workflow_manager, 'workflow_history')

    @pytest.mark.asyncio
    async def test_end_to_end_workflow_integration(self, workflow_manager, mock_orchestrator, sample_transcription_result, sample_timeline_info):
        """Test end-to-end workflow integration with all components."""
        # Setup mocks for complete workflow
        mock_orchestrator.resolve_bridge.extract_audio.return_value = {
            "success": True,
            "audio_path": "/tmp/test_audio.wav"
        }
        mock_orchestrator.transcribe_audio.return_value = sample_transcription_result
        mock_orchestrator.analyze_content.return_value = {
            "success": True,
            "analysis": {"sentiment": "positive", "topics": ["technology", "AI"]}
        }
        mock_orchestrator.get_timeline_info.return_value = sample_timeline_info
        
        # Execute the complete workflow
        result = await workflow_manager.execute_timeline_analysis("/path/to/timeline")
        
        # Verify the workflow completed successfully
        assert result.success
        assert result.status == WorkflowStatus.COMPLETED
        assert result.steps_completed > 0
        
        # Verify all orchestrator methods were called
        mock_orchestrator.resolve_bridge.extract_audio.assert_called()
        mock_orchestrator.transcribe_audio.assert_called()
        mock_orchestrator.analyze_content.assert_called()

    @pytest.mark.asyncio
    async def test_workflow_history(self, workflow_manager, mock_orchestrator, sample_transcription_result):
        """Test workflow history tracking."""
        # Mock the orchestrator methods for a successful workflow
        mock_orchestrator.resolve_bridge.get_current_timeline.return_value = "/path/to/timeline"
        mock_orchestrator.resolve_bridge.extract_audio.return_value = "/tmp/test_audio.wav"
        mock_orchestrator.transcribe_audio.return_value = sample_transcription_result
        mock_orchestrator.analyze_content.return_value = {
            "success": True,
            "analysis": {"sentiment": "positive"}
        }
        mock_orchestrator.get_timeline_info.return_value = {
            "name": "Test Timeline",
            "duration": 120.5
        }
        
        # Execute timeline analysis
        result = await workflow_manager.execute_timeline_analysis()
        
        # Check that workflow completed
        assert result.success
        assert result.status == WorkflowStatus.COMPLETED