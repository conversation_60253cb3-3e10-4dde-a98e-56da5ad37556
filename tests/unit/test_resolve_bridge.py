"""
Unit tests for the DaVinci Resolve Bridge module.
"""

import asyncio
import os
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, MagicMock
import pytest

# Add the src directory to the path for imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.resolve_bridge.resolve_bridge import (
    DaVinciResolveBridge,
    TimelineInfo,
    ProjectInfo,
    MockResolveApp,
    MockProjectManager,
    MockProject,
    MockTimeline,
    MockMediaPool,
    MockMediaPoolFolder,
    MockClip
)


class TestDaVinciResolveBridge:
    """Test cases for DaVinciResolveBridge class."""
    
    @pytest.fixture
    def bridge(self):
        """Create a bridge instance for testing."""
        return DaVinciResolveBridge()
    
    @pytest.fixture
    def mock_subprocess_run(self):
        """Mock subprocess.run for testing."""
        with patch('subprocess.run') as mock_run:
            yield mock_run
    
    @pytest.fixture
    def mock_tempfile(self):
        """Mock tempfile module."""
        with patch('tempfile.NamedTemporaryFile') as mock_tempfile:
            mock_file = MagicMock()
            mock_file.name = '/tmp/test.wav'
            mock_file.__enter__.return_value = mock_file
            mock_tempfile.return_value = mock_file
            yield mock_tempfile
    
    @pytest.mark.asyncio
    async def test_bridge_initialization(self, bridge):
        """Test bridge initialization."""
        assert bridge.connected is False
        assert bridge.resolve_app is None
        assert bridge.project_manager is None
        assert bridge.current_project is None
        assert bridge.current_timeline is None
        assert bridge.fusion_script_path is None
    
    @pytest.mark.asyncio
    async def test_connect_with_mock_mode(self, bridge):
        """Test connection in mock mode when DaVinci Resolve API is not available."""
        with patch('builtins.__import__', side_effect=ImportError("No module named 'DaVinciResolveScript'")):
            result = await bridge.connect()
            
            assert result is True
            assert bridge.connected is True
            assert bridge.resolve_app is not None
            assert isinstance(bridge.resolve_app, MockResolveApp)
            assert bridge.project_manager is not None
            assert isinstance(bridge.project_manager, MockProjectManager)
    
    @pytest.mark.asyncio
    async def test_connect_with_real_resolve_api(self, bridge):
        """Test connection with real DaVinci Resolve API."""
        mock_dvr = Mock()
        mock_resolve_app = Mock()
        mock_project_manager = Mock()
        
        mock_dvr.scriptapp.return_value = mock_resolve_app
        mock_resolve_app.GetProjectManager.return_value = mock_project_manager
        
        with patch.dict('sys.modules', {'DaVinciResolveScript': mock_dvr}):
            result = await bridge.connect()
            
            assert result is True
            assert bridge.connected is True
            assert bridge.resolve_app == mock_resolve_app
            assert bridge.project_manager == mock_project_manager
    
    @pytest.mark.asyncio
    async def test_connect_failure(self, bridge):
        """Test connection failure handling."""
        with patch('builtins.__import__', side_effect=Exception("Connection failed")):
            result = await bridge.connect()
            
            assert result is False
            assert bridge.connected is False
    
    @pytest.mark.asyncio
    async def test_disconnect(self, bridge):
        """Test disconnection."""
        bridge.connected = True
        bridge.resolve_app = Mock()
        bridge.project_manager = Mock()
        bridge.current_project = Mock()
        bridge.current_timeline = Mock()
        
        await bridge.disconnect()
        
        assert bridge.connected is False
        assert bridge.resolve_app is None
        assert bridge.project_manager is None
        assert bridge.current_project is None
        assert bridge.current_timeline is None
    
    @pytest.mark.asyncio
    async def test_get_current_project_not_connected(self, bridge):
        """Test getting current project when not connected."""
        result = await bridge.get_current_project()
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_current_project_success(self, bridge):
        """Test successful project information retrieval."""
        await bridge.connect()  # Use mock mode
        
        result = await bridge.get_current_project()
        
        assert result is not None
        assert isinstance(result, ProjectInfo)
        assert result.name == "Test Project"
        assert result.timeline_count == 2
        assert result.current_timeline == "Current Timeline"
        assert len(result.media_pool_items) == 3
    
    @pytest.mark.asyncio
    async def test_get_current_timeline_not_connected(self, bridge):
        """Test getting current timeline when not connected."""
        result = await bridge.get_current_timeline()
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_current_timeline_success(self, bridge):
        """Test successful timeline information retrieval."""
        await bridge.connect()  # Use mock mode
        
        # First get current project to set it
        await bridge.get_current_project()
        
        result = await bridge.get_current_timeline()
        
        assert result is not None
        assert isinstance(result, TimelineInfo)
        assert result.name == "Current Timeline"
        assert result.duration == 120.0
        assert result.frame_rate == 24.0
        assert result.resolution == "1920x1080"
        assert result.audio_tracks == 0
        assert result.video_tracks == 0
    
    @pytest.mark.asyncio
    async def test_extract_audio_not_connected(self, bridge):
        """Test audio extraction when not connected."""
        result = await bridge.extract_audio()
        assert result is None
    
    @pytest.mark.asyncio
    async def test_extract_audio_with_resolve_api(self, bridge, mock_tempfile):
        """Test audio extraction using DaVinci Resolve API."""
        await bridge.connect()
        await bridge.get_current_project()  # Set current project/timeline
        
        # Mock the resolve app's ExtractAudio method
        bridge.resolve_app.ExtractAudio = Mock(return_value=True)
        
        result = await bridge.extract_audio()
        
        assert result == '/tmp/test.wav'
        bridge.resolve_app.ExtractAudio.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_extract_audio_with_ffmpeg_fallback(self, bridge, mock_subprocess_run):
        """Test audio extraction using FFmpeg fallback."""
        await bridge.connect()
        await bridge.get_current_project()  # Set current project/timeline
        
        # Mock FFmpeg commands to succeed for both version check and extraction
        mock_subprocess_run.side_effect = [
            Mock(returncode=0, stderr=""),  # First call: ffmpeg -version
            Mock(returncode=0, stderr="")   # Second call: ffmpeg extraction
        ]
        
        # Mock tempfile to return a specific path
        with patch('tempfile.NamedTemporaryFile') as mock_tempfile, \
             patch.object(bridge, 'get_timeline_media', return_value=['/path/to/video.mp4']), \
             patch.object(bridge.resolve_app, 'ExtractAudio', return_value=False):
            
            mock_file = MagicMock()
            mock_file.name = '/tmp/test.wav'
            mock_file.__enter__.return_value = mock_file
            mock_tempfile.return_value = mock_file
            
            result = await bridge.extract_audio()
            
            assert result == '/tmp/test.wav'
            assert mock_subprocess_run.call_count == 2
    
    @pytest.mark.asyncio
    async def test_extract_audio_ffmpeg_failure(self, bridge, mock_subprocess_run):
        """Test audio extraction when FFmpeg fails."""
        await bridge.connect()
        await bridge.get_current_project()  # Set current project/timeline
        
        # Mock FFmpeg version check to succeed, but extraction to fail
        mock_subprocess_run.side_effect = [
            Mock(returncode=0, stderr=""),  # ffmpeg -version succeeds
            Mock(returncode=1, stderr="FFmpeg error")  # ffmpeg extraction fails
        ]
        
        # Mock tempfile to return our test path
        with patch('tempfile.NamedTemporaryFile') as mock_tempfile, \
             patch.object(bridge.resolve_app, 'ExtractAudio', return_value=False):  # Force FFmpeg fallback
            
            mock_file = MagicMock()
            mock_file.name = '/tmp/test.wav'
            mock_file.__enter__.return_value = mock_file
            mock_tempfile.return_value = mock_file
            
            # Mock timeline media
            with patch.object(bridge, 'get_timeline_media', return_value=['/path/to/video.mp4']):
                result = await bridge.extract_audio()
                
                assert result is None
                assert mock_subprocess_run.call_count == 2
    
    @pytest.mark.asyncio
    async def test_extract_audio_ffmpeg_not_found(self, bridge, mock_subprocess_run):
        """Test audio extraction when FFmpeg is not available."""
        await bridge.connect()
        await bridge.get_current_project()  # Set current project/timeline
        
        # Mock FFmpeg version check to fail with FileNotFoundError
        mock_subprocess_run.side_effect = FileNotFoundError("ffmpeg not found")
        
        # Mock tempfile to return our test path
        with patch('tempfile.NamedTemporaryFile') as mock_tempfile, \
             patch.object(bridge.resolve_app, 'ExtractAudio', return_value=False):  # Force FFmpeg fallback
            
            mock_file = MagicMock()
            mock_file.name = '/tmp/test.wav'
            mock_file.__enter__.return_value = mock_file
            mock_tempfile.return_value = mock_file
            
            # Mock timeline media
            with patch.object(bridge, 'get_timeline_media', return_value=['/path/to/video.mp4']):
                result = await bridge.extract_audio()
                
                assert result is None
                assert mock_subprocess_run.call_count == 1
    
    @pytest.mark.asyncio
    async def test_get_timeline_media_not_connected(self, bridge):
        """Test getting timeline media when not connected."""
        result = await bridge.get_timeline_media()
        assert result == []
    
    @pytest.mark.asyncio
    async def test_get_timeline_media_success(self, bridge):
        """Test successful timeline media retrieval."""
        await bridge.connect()
        await bridge.get_current_project()  # Set current project/timeline
        
        result = await bridge.get_timeline_media()
        
        assert result == []
        # Note: Mock timeline returns empty list for GetItemListInTrack
    
    @pytest.mark.asyncio
    async def test_create_fusion_script(self, bridge):
        """Test Fusion script creation."""
        script_content = "Fusion {\n  Tools = {\n    Loader1 = Loader {\n      Clip = 'test.mp4'\n    }\n  }\n}"
        script_name = "test_script"
        
        with patch('tempfile.NamedTemporaryFile') as mock_tempfile:
            mock_file = MagicMock()
            mock_file.name = '/tmp/test_script.comp'
            mock_file.__enter__.return_value = mock_file
            mock_tempfile.return_value = mock_file
            
            result = await bridge.create_fusion_script(script_content, script_name)
            
            assert result == '/tmp/test_script.comp'
            mock_file.write.assert_called_once_with(script_content)
            assert bridge.fusion_script_path == '/tmp/test_script.comp'
    
    @pytest.mark.asyncio
    async def test_create_fusion_script_failure(self, bridge):
        """Test Fusion script creation failure."""
        script_content = "Fusion { Tools = {} }"
        script_name = "test_script"
        
        with patch('tempfile.NamedTemporaryFile', side_effect=Exception("IO error")):
            result = await bridge.create_fusion_script(script_content, script_name)
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_execute_fusion_script_success(self, bridge):
        """Test successful Fusion script execution."""
        await bridge.connect()
        bridge.resolve_app.ExecuteFusionScript = Mock(return_value=True)
        
        result = await bridge.execute_fusion_script('/tmp/test_script.comp')
        
        assert result is True
        bridge.resolve_app.ExecuteFusionScript.assert_called_once_with('/tmp/test_script.comp')
    
    @pytest.mark.asyncio
    async def test_execute_fusion_script_not_available(self, bridge):
        """Test Fusion script execution when not available."""
        await bridge.connect()
        # Don't add ExecuteFusionScript method to resolve_app
        
        result = await bridge.execute_fusion_script('/tmp/test_script.comp')
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_execute_fusion_script_failure(self, bridge):
        """Test Fusion script execution failure."""
        await bridge.connect()
        bridge.resolve_app.ExecuteFusionScript = Mock(side_effect=Exception("Execution failed"))
        
        result = await bridge.execute_fusion_script('/tmp/test_script.comp')
        
        assert result is False


class TestMockClasses:
    """Test cases for mock classes."""
    
    def test_mock_resolve_app(self):
        """Test MockResolveApp class."""
        mock_app = MockResolveApp()
        
        assert mock_app.name == "Mock DaVinci Resolve"
        assert mock_app.version == "18.0"
        
        project_manager = mock_app.GetProjectManager()
        assert isinstance(project_manager, MockProjectManager)
        
        project = mock_app.GetCurrentProject()
        assert isinstance(project, MockProject)
        
        # Test ExtractAudio
        with patch('builtins.open', create=True) as mock_open:
            mock_file = MagicMock()
            mock_open.return_value.__enter__.return_value = mock_file
            result = mock_app.ExtractAudio(None, '/tmp/test.wav')
            assert result is True
            mock_file.write.assert_called()
    
    def test_mock_project_manager(self):
        """Test MockProjectManager class."""
        project_manager = MockProjectManager()
        
        assert project_manager.projects == ["Project 1", "Project 2", "Test Project"]
        
        project = project_manager.GetCurrentProject()
        assert isinstance(project, MockProject)
    
    def test_mock_project(self):
        """Test MockProject class."""
        project = MockProject()
        
        assert project.GetName() == "Test Project"
        
        timelines = project.GetTimelineList()
        assert len(timelines) == 2
        assert all(isinstance(tl, MockTimeline) for tl in timelines)
        
        current_timeline = project.GetCurrentTimeline()
        assert isinstance(current_timeline, MockTimeline)
        assert current_timeline.name == "Current Timeline"
        
        media_pool = project.GetMediaPool()
        assert isinstance(media_pool, MockMediaPool)
    
    def test_mock_timeline(self):
        """Test MockTimeline class."""
        timeline = MockTimeline("Test Timeline")
        
        assert timeline.GetName() == "Test Timeline"
        assert timeline.GetDuration() == 120.0
        assert timeline.GetSetting('timelineFrameRate') == 24.0
        assert timeline.GetSetting('timelineResolutionWidth') == 1920
        assert timeline.GetSetting('timelineResolutionHeight') == 1080
        assert timeline.GetSetting('unknown_setting') == 0  # Default case
        
        items = timeline.GetItemListInTrack('video', 1)
        assert items == []  # Empty list for mock
    
    def test_mock_media_pool(self):
        """Test MockMediaPool class."""
        media_pool = MockMediaPool()
        
        root_folder = media_pool.GetRootFolder()
        assert isinstance(root_folder, MockMediaPoolFolder)
    
    def test_mock_media_pool_folder(self):
        """Test MockMediaPoolFolder class."""
        folder = MockMediaPoolFolder()
        
        clips = folder.GetClipList()
        assert len(clips) == 3
        assert all(isinstance(clip, MockClip) for clip in clips)
        assert all(clip.GetName() == f"Clip {i}" for i, clip in enumerate(clips))
    
    def test_mock_clip(self):
        """Test MockClip class."""
        clip = MockClip("Test Clip")
        
        assert clip.GetName() == "Test Clip"


class TestDataclasses:
    """Test cases for dataclasses."""
    
    def test_timeline_info_dataclass(self):
        """Test TimelineInfo dataclass."""
        timeline_info = TimelineInfo(
            name="Test Timeline",
            duration=120.5,
            frame_rate=24.0,
            resolution="1920x1080",
            audio_tracks=2,
            video_tracks=1,
            media_pool_items=["clip1.mp4", "clip2.mp4"]
        )
        
        assert timeline_info.name == "Test Timeline"
        assert timeline_info.duration == 120.5
        assert timeline_info.frame_rate == 24.0
        assert timeline_info.resolution == "1920x1080"
        assert timeline_info.audio_tracks == 2
        assert timeline_info.video_tracks == 1
        assert timeline_info.media_pool_items == ["clip1.mp4", "clip2.mp4"]
    
    def test_project_info_dataclass(self):
        """Test ProjectInfo dataclass."""
        project_info = ProjectInfo(
            name="Test Project",
            timeline_count=3,
            current_timeline="Main Timeline",
            media_pool_items=["clip1.mp4", "clip2.mp4", "clip3.mp4"]
        )
        
        assert project_info.name == "Test Project"
        assert project_info.timeline_count == 3
        assert project_info.current_timeline == "Main Timeline"
        assert project_info.media_pool_items == ["clip1.mp4", "clip2.mp4", "clip3.mp4"]


if __name__ == "__main__":
    pytest.main([__file__])