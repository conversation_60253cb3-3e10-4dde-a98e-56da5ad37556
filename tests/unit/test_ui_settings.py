"""Unit tests for UI settings functionality."""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from src.core.config_manager import ConfigManager
    from src.ai_engine.model_manager import ModelManager
    from src.ai_engine.model_provider_factory import ModelProviderFactory, ModelProviderType
    from src.ai_engine.utils import create_model_config, get_provider_display_name
except ImportError:
    # Mock imports if not available
    ConfigManager = Mock
    ModelManager = Mock
    ModelProviderFactory = Mock
    ModelProviderType = Mock
    create_model_config = Mock
    get_provider_display_name = Mock

# Mock DOM elements for testing
class MockElement:
    """Mock DOM element for testing."""
    
    def __init__(self, value="", checked=False, selected_index=0):
        self.value = value
        self.checked = checked
        self.selectedIndex = selected_index
        self.options = []
        self.style = Mock()
        self.classList = Mock()
    
    def getElementById(self, element_id):
        """Mock getElementById."""
        return MockElement()
    
    def querySelector(self, selector):
        """Mock querySelector."""
        return MockElement()
    
    def querySelectorAll(self, selector):
        """Mock querySelectorAll."""
        return [MockElement()]


class TestUISettingsIntegration:
    """Test UI settings integration with model providers."""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Mock configuration manager for UI testing."""
        mock_manager = Mock()
        mock_manager.get_config.return_value = {
            'ai': {
                'primary_provider': 'openrouter',
                'fallback_provider': 'openai',
                'primary_model': 'anthropic/claude-3.5-sonnet',
                'fallback_model': 'gpt-4',
                'api_keys': {
                    'openrouter': 'test-openrouter-key',
                    'openai': 'test-openai-key',
                    'anthropic': 'test-anthropic-key',
                    'google': 'test-google-key',
                    'qwen': 'test-qwen-key',
                    'deepseek': 'test-deepseek-key',
                    'kimi': 'test-kimi-key',
                    'glm': 'test-glm-key'
                },
                'base_urls': {
                    'openrouter': 'https://openrouter.ai/api/v1',
                    'openai': 'https://api.openai.com/v1',
                    'anthropic': 'https://api.anthropic.com',
                    'google': 'https://generativelanguage.googleapis.com/v1beta',
                    'qwen': 'https://dashscope.aliyuncs.com/api/v1',
                    'deepseek': 'https://api.deepseek.com/v1',
                    'kimi': 'https://api.moonshot.cn/v1',
                    'glm': 'https://open.bigmodel.cn/api/paas/v4'
                },
                'local_settings': {
                    'lm_studio_port': '1234',
                    'ollama_port': '11434',
                    'local_model_path': '/path/to/local/model'
                },
                'max_tokens': 4000,
                'temperature': 0.7,
                'top_p': 0.9,
                'presence_penalty': 0.0,
                'frequency_penalty': 0.0
            }
        }
        mock_manager.save_config.return_value = True
        return mock_manager
    
    def test_settings_structure_validation(self, mock_config_manager):
        """Test that settings structure matches expected format."""
        config = mock_config_manager.get_config()
        ai_config = config['ai']
        
        # Test required provider fields
        assert 'primary_provider' in ai_config
        assert 'fallback_provider' in ai_config
        assert 'primary_model' in ai_config
        assert 'fallback_model' in ai_config
        
        # Test API configuration
        assert 'api_keys' in ai_config
        assert 'base_urls' in ai_config
        assert 'local_settings' in ai_config
        
        # Test model parameters
        assert 'max_tokens' in ai_config
        assert 'temperature' in ai_config
        assert 'top_p' in ai_config
        assert 'presence_penalty' in ai_config
        assert 'frequency_penalty' in ai_config
    
    def test_provider_api_keys_validation(self, mock_config_manager):
        """Test API key validation for all providers."""
        config = mock_config_manager.get_config()
        api_keys = config['ai']['api_keys']
        
        expected_providers = [
            'openrouter', 'openai', 'anthropic', 'google',
            'qwen', 'deepseek', 'kimi', 'glm'
        ]
        
        for provider in expected_providers:
            assert provider in api_keys
            assert api_keys[provider] is not None
            assert len(api_keys[provider]) > 0
    
    def test_provider_base_urls_validation(self, mock_config_manager):
        """Test base URL validation for all providers."""
        config = mock_config_manager.get_config()
        base_urls = config['ai']['base_urls']
        
        expected_providers = [
            'openrouter', 'openai', 'anthropic', 'google',
            'qwen', 'deepseek', 'kimi', 'glm'
        ]
        
        for provider in expected_providers:
            assert provider in base_urls
            assert base_urls[provider] is not None
            assert base_urls[provider].startswith('https://')
    
    def test_local_settings_validation(self, mock_config_manager):
        """Test local model settings validation."""
        config = mock_config_manager.get_config()
        local_settings = config['ai']['local_settings']
        
        # Test required local settings
        assert 'lm_studio_port' in local_settings
        assert 'ollama_port' in local_settings
        assert 'local_model_path' in local_settings
        
        # Test port validation
        assert local_settings['lm_studio_port'].isdigit()
        assert local_settings['ollama_port'].isdigit()
        assert int(local_settings['lm_studio_port']) > 0
        assert int(local_settings['ollama_port']) > 0
    
    def test_model_parameters_validation(self, mock_config_manager):
        """Test model parameter validation."""
        config = mock_config_manager.get_config()
        ai_config = config['ai']
        
        # Test parameter ranges
        assert 0 <= ai_config['temperature'] <= 2.0
        assert 0 <= ai_config['top_p'] <= 1.0
        assert -2.0 <= ai_config['presence_penalty'] <= 2.0
        assert -2.0 <= ai_config['frequency_penalty'] <= 2.0
        assert ai_config['max_tokens'] > 0
        assert ai_config['max_tokens'] <= 32000  # Reasonable upper limit


class TestProviderSpecificModels:
    """Test provider-specific model configurations."""
    
    def test_openrouter_models(self):
        """Test OpenRouter model configuration."""
        openrouter_models = [
            'anthropic/claude-3.5-sonnet',
            'openai/gpt-4',
            'meta-llama/llama-3.1-70b-instruct',
            'google/gemini-pro-1.5',
            'mistralai/mixtral-8x7b-instruct'
        ]
        
        for model in openrouter_models:
            assert '/' in model  # OpenRouter models have provider/model format
            assert len(model.split('/')) == 2
    
    def test_openai_models(self):
        """Test OpenAI model configuration."""
        openai_models = [
            'gpt-4',
            'gpt-4-turbo',
            'gpt-3.5-turbo',
            'gpt-4o',
            'gpt-4o-mini'
        ]
        
        for model in openai_models:
            assert model.startswith('gpt-')
    
    def test_anthropic_models(self):
        """Test Anthropic model configuration."""
        anthropic_models = [
            'claude-3-5-sonnet-20241022',
            'claude-3-opus-20240229',
            'claude-3-haiku-20240307'
        ]
        
        for model in anthropic_models:
            assert model.startswith('claude-')
    
    def test_google_models(self):
        """Test Google model configuration."""
        google_models = [
            'gemini-1.5-pro',
            'gemini-1.5-flash',
            'gemini-pro'
        ]
        
        for model in google_models:
            assert model.startswith('gemini')
    
    def test_local_models(self):
        """Test local model configuration."""
        local_models = [
            'llama-3.1-8b-instruct',
            'mistral-7b-instruct',
            'codellama-13b-instruct'
        ]
        
        # Local models should not have provider prefixes
        for model in local_models:
            assert '/' not in model


class TestConnectionTesting:
    """Test connection testing functionality."""
    
    @pytest.fixture
    def mock_connection_test(self):
        """Mock connection test responses."""
        return {
            'success': True,
            'latency': 150,
            'model_info': {
                'name': 'anthropic/claude-3.5-sonnet',
                'provider': 'openrouter',
                'context_length': 200000
            }
        }
    
    def test_successful_connection(self, mock_connection_test):
        """Test successful provider connection."""
        assert mock_connection_test['success'] is True
        assert 'latency' in mock_connection_test
        assert mock_connection_test['latency'] > 0
        assert 'model_info' in mock_connection_test
    
    def test_connection_failure(self):
        """Test connection failure handling."""
        failed_connection = {
            'success': False,
            'error': 'Invalid API key',
            'error_code': 401
        }
        
        assert failed_connection['success'] is False
        assert 'error' in failed_connection
        assert 'error_code' in failed_connection
    
    def test_connection_timeout(self):
        """Test connection timeout handling."""
        timeout_connection = {
            'success': False,
            'error': 'Connection timeout',
            'error_code': 408,
            'timeout': True
        }
        
        assert timeout_connection['success'] is False
        assert timeout_connection['timeout'] is True


class TestSettingsFormValidation:
    """Test settings form validation and data handling."""
    
    def test_form_data_collection(self):
        """Test settings form data collection structure."""
        form_data = {
            'ai': {
                'primary_provider': 'openrouter',
                'fallback_provider': 'openai',
                'primary_model': 'anthropic/claude-3.5-sonnet',
                'fallback_model': 'gpt-4',
                'api_keys': {
                    'openrouter': 'test-key-1',
                    'openai': 'test-key-2'
                },
                'base_urls': {
                    'openrouter': 'https://openrouter.ai/api/v1',
                    'openai': 'https://api.openai.com/v1'
                },
                'local_settings': {
                    'lm_studio_port': '1234',
                    'ollama_port': '11434',
                    'local_model_path': '/path/to/model'
                },
                'max_tokens': 4000,
                'temperature': 0.7,
                'top_p': 0.9,
                'presence_penalty': 0.0,
                'frequency_penalty': 0.0
            }
        }
        
        # Validate structure
        assert 'ai' in form_data
        ai_data = form_data['ai']
        
        # Validate required fields
        required_fields = [
            'primary_provider', 'fallback_provider',
            'primary_model', 'fallback_model',
            'api_keys', 'base_urls', 'local_settings'
        ]
        
        for field in required_fields:
            assert field in ai_data
    
    def test_form_validation_errors(self):
        """Test form validation error handling."""
        invalid_data = {
            'ai': {
                'temperature': 3.0,  # Invalid: > 2.0
                'top_p': 1.5,       # Invalid: > 1.0
                'max_tokens': -100   # Invalid: < 0
            }
        }
        
        ai_data = invalid_data['ai']
        
        # These should fail validation
        assert ai_data['temperature'] > 2.0
        assert ai_data['top_p'] > 1.0
        assert ai_data['max_tokens'] < 0
    
    def test_empty_api_key_handling(self):
        """Test handling of empty API keys."""
        data_with_empty_keys = {
            'ai': {
                'api_keys': {
                    'openrouter': '',
                    'openai': 'valid-key',
                    'anthropic': None
                }
            }
        }
        
        api_keys = data_with_empty_keys['ai']['api_keys']
        
        # Test empty key detection
        assert api_keys['openrouter'] == ''
        assert api_keys['openai'] != ''
        assert api_keys['anthropic'] is None


class TestProviderSwitching:
    """Test dynamic provider switching functionality."""
    
    def test_provider_switch_data_structure(self):
        """Test provider switching data structure."""
        switch_data = {
            'from_provider': 'openrouter',
            'to_provider': 'openai',
            'from_model': 'anthropic/claude-3.5-sonnet',
            'to_model': 'gpt-4',
            'preserve_settings': True
        }
        
        assert 'from_provider' in switch_data
        assert 'to_provider' in switch_data
        assert 'from_model' in switch_data
        assert 'to_model' in switch_data
        assert switch_data['preserve_settings'] is True
    
    def test_model_compatibility_check(self):
        """Test model compatibility checking."""
        # OpenRouter models have provider/model format
        openrouter_model = 'anthropic/claude-3.5-sonnet'
        assert '/' in openrouter_model
        
        # OpenAI models are direct
        openai_model = 'gpt-4'
        assert '/' not in openai_model
        
        # Local models are direct
        local_model = 'llama-3.1-8b-instruct'
        assert '/' not in local_model
    
    def test_settings_preservation(self):
        """Test settings preservation during provider switch."""
        original_settings = {
            'max_tokens': 4000,
            'temperature': 0.7,
            'top_p': 0.9,
            'presence_penalty': 0.0,
            'frequency_penalty': 0.0
        }
        
        # Settings should be preserved when switching providers
        preserved_settings = original_settings.copy()
        
        assert preserved_settings == original_settings
        assert preserved_settings is not original_settings  # Different objects