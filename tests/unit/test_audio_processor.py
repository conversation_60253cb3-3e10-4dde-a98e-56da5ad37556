import pytest
import numpy as np
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock, call
from tempfile import NamedTemporaryFile
import tempfile
import subprocess

# Add the src directory to the path for imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Import the config first (no dependency issues)
from src.transcription.config import TranscriptionConfig

# Now import the audio processor - it will handle its own dependency checking
from src.transcription.audio_processor import AudioProcessor


class TestAudioProcessor:
    """Unit tests for AudioProcessor class."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock transcription config."""
        config = Mock(spec=TranscriptionConfig)
        config.sample_rate = 16000
        config.audio_format = "wav"
        config.enable_preprocessing = True
        config.noise_reduction = True
        config.normalize_audio = True
        return config
    
    @pytest.fixture
    def audio_processor(self, mock_config):
        """Create AudioProcessor instance with mock config."""
        return AudioProcessor(mock_config)
    
    @pytest.fixture
    def mock_audio_file(self):
        """Create a mock audio file path."""
        with NamedTemporaryFile(suffix=".wav", delete=False) as f:
            f.write(b"mock audio data")
            return Path(f.name)
    
    def test_initialization(self, mock_config):
        """Test AudioProcessor initialization."""
        processor = AudioProcessor(mock_config)
        assert processor.config == mock_config
        # Check actual availability - these should match what's really available
        # The test should pass regardless of what's installed on the system
    
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', True)
    @patch('src.transcription.audio_processor.PYDUB_AVAILABLE', False)
    def test_initialization_librosa_available(self, mock_config):
        """Test initialization when librosa is available."""
        processor = AudioProcessor(mock_config)
        assert processor.librosa_available == True
        assert processor.pydub_available == False
    
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', False)
    @patch('src.transcription.audio_processor.PYDUB_AVAILABLE', True)
    def test_initialization_pydub_available(self, mock_config):
        """Test initialization when pydub is available."""
        processor = AudioProcessor(mock_config)
        assert processor.librosa_available == False
        assert processor.pydub_available == True
    
    @patch('src.transcription.audio_processor.Path.exists')
    def test_process_audio_file_not_found(self, mock_exists, audio_processor):
        """Test process_audio with non-existent file."""
        mock_exists.return_value = False
        
        result = audio_processor.process_audio("nonexistent.wav")
        
        assert result is None
    
    @patch('src.transcription.audio_processor.AudioProcessor._process_with_librosa')
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', True)
    @patch('src.transcription.audio_processor.Path.exists')
    def test_process_audio_with_librosa(self, mock_exists, mock_process_librosa, audio_processor, mock_audio_file):
        """Test process_audio using librosa."""
        mock_exists.return_value = True
        
        # Mock the _process_with_librosa method to return a path
        mock_process_librosa.return_value = mock_audio_file
        
        result = audio_processor.process_audio(str(mock_audio_file))
        
        assert result is not None
        assert result == mock_audio_file
        mock_process_librosa.assert_called_once_with(mock_audio_file)
    
    @patch('src.transcription.audio_processor.PYDUB_AVAILABLE', True)
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', False)
    @patch('src.transcription.audio_processor.Path.exists')
    @patch('src.transcription.audio_processor.AudioSegment')
    def test_process_audio_with_pydub(self, mock_audio_segment, mock_exists, audio_processor, mock_audio_file):
        """Test process_audio using pydub."""
        mock_exists.return_value = True
        
        # Mock AudioSegment
        mock_audio = MagicMock()
        mock_audio.set_channels.return_value = mock_audio
        mock_audio.set_frame_rate.return_value = mock_audio
        mock_audio.normalize.return_value = mock_audio
        mock_audio.get_array_of_samples.return_value = np.array([100, 200, 300, 400, 500])
        mock_audio.__len__.return_value = 1000  # 1000ms
        mock_audio.export.return_value = None  # Ensure export method is properly mocked
        mock_audio_segment.from_file.return_value = mock_audio
        
        # Mock the slicing operation (audio[start_ms:end_ms])
        mock_audio.__getitem__.return_value = mock_audio
        
        with patch('tempfile.NamedTemporaryFile') as mock_temp:
            mock_temp_file = MagicMock()
            mock_temp_file.name = str(mock_audio_file)
            mock_temp.return_value.__enter__.return_value = mock_temp_file
            
            result = audio_processor.process_audio(str(mock_audio_file))
            
            assert result is not None
            mock_audio_segment.from_file.assert_called_once()
            mock_audio.set_channels.assert_called_once_with(1)
            mock_audio.set_frame_rate.assert_called_once_with(16000)
            mock_audio.normalize.assert_called_once()
            mock_audio.export.assert_called_once()
    
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', True)
    @patch('src.transcription.audio_processor.Path.exists')
    @patch('src.transcription.audio_processor.librosa')
    def test_process_audio_librosa_error_handling(self, mock_librosa, mock_exists, audio_processor, mock_audio_file):
        """Test error handling in process_audio with librosa."""
        mock_exists.return_value = True
        mock_librosa.load.side_effect = Exception("Librosa error")
        
        result = audio_processor.process_audio(str(mock_audio_file))
        
        assert result is None
    
    @patch('src.transcription.audio_processor.PYDUB_AVAILABLE', True)
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', False)
    @patch('src.transcription.audio_processor.Path.exists')
    @patch('src.transcription.audio_processor.AudioSegment')
    def test_process_audio_pydub_error_handling(self, mock_audio_segment, mock_exists, audio_processor, mock_audio_file):
        """Test error handling in process_audio with pydub."""
        mock_exists.return_value = True
        mock_audio_segment.from_file.side_effect = Exception("Pydub error")
        
        result = audio_processor.process_audio(str(mock_audio_file))
        
        assert result is None
    
    @patch('src.transcription.audio_processor.shutil.which')
    @patch('src.transcription.audio_processor.Path.exists')
    def test_convert_audio_format_ffmpeg_not_found(self, mock_exists, mock_which, audio_processor, mock_audio_file):
        """Test _convert_audio_format when ffmpeg is not available."""
        mock_exists.return_value = True
        mock_which.return_value = None
        
        result = audio_processor._convert_audio_format(mock_audio_file)
        
        assert result is None
    
    @patch('src.transcription.audio_processor.subprocess.run')
    @patch('src.transcription.audio_processor.shutil.which')
    @patch('src.transcription.audio_processor.Path.exists')
    def test_convert_audio_format_success(self, mock_exists, mock_which, mock_run, audio_processor, mock_audio_file):
        """Test successful audio format conversion."""
        mock_exists.return_value = True
        mock_which.return_value = "/usr/bin/ffmpeg"
        
        # Mock successful subprocess call
        mock_result = Mock()
        mock_result.returncode = 0
        mock_run.return_value = mock_result
        
        with patch('tempfile.NamedTemporaryFile') as mock_temp:
            mock_temp_file = MagicMock()
            mock_temp_file.name = str(mock_audio_file)
            mock_temp.return_value.__enter__.return_value = mock_temp_file
            
            result = audio_processor._convert_audio_format(mock_audio_file)
            
            assert result is not None
            mock_run.assert_called_once()
            
            # Verify ffmpeg command structure
            call_args = mock_run.call_args[0][0]
            assert "ffmpeg" in call_args
            assert "-i" in call_args
            assert "-ar" in call_args
            assert "16000" in call_args
    
    @patch('src.transcription.audio_processor.subprocess.run')
    @patch('src.transcription.audio_processor.shutil.which')
    @patch('src.transcription.audio_processor.Path.exists')
    def test_convert_audio_format_ffmpeg_failure(self, mock_exists, mock_which, mock_run, audio_processor, mock_audio_file):
        """Test _convert_audio_format when ffmpeg fails."""
        mock_exists.return_value = True
        mock_which.return_value = "/usr/bin/ffmpeg"
        
        # Mock failed subprocess call
        mock_run.side_effect = subprocess.CalledProcessError(1, "ffmpeg", stderr="Error converting")
        
        result = audio_processor._convert_audio_format(mock_audio_file)
        
        assert result is None
    
    @patch('src.transcription.audio_processor.shutil.which')
    @patch('src.transcription.audio_processor.Path.exists')
    def test_extract_audio_from_video_ffmpeg_not_found(self, mock_exists, mock_which, audio_processor, mock_audio_file):
        """Test extract_audio_from_video when ffmpeg is not available."""
        mock_exists.return_value = True
        mock_which.return_value = None
        
        result = audio_processor.extract_audio_from_video(mock_audio_file)
        
        assert result is None
    
    @patch('src.transcription.audio_processor.subprocess.run')
    @patch('src.transcription.audio_processor.shutil.which')
    @patch('src.transcription.audio_processor.Path.exists')
    def test_extract_audio_from_video_success(self, mock_exists, mock_which, mock_run, audio_processor, mock_audio_file):
        """Test successful audio extraction from video."""
        mock_exists.return_value = True
        mock_which.return_value = "/usr/bin/ffmpeg"
        
        # Mock successful subprocess call
        mock_result = Mock()
        mock_result.returncode = 0
        mock_run.return_value = mock_result
        
        result = audio_processor.extract_audio_from_video(mock_audio_file)
        
        assert result is not None
        assert result.suffix == ".wav"
        mock_run.assert_called_once()
        
        # Verify ffmpeg command structure
        call_args = mock_run.call_args[0][0]
        assert "ffmpeg" in call_args
        assert "-i" in call_args
        assert "-vn" in call_args
        assert "-acodec" in call_args
    
    @patch('src.transcription.audio_processor.subprocess.run')
    @patch('src.transcription.audio_processor.shutil.which')
    @patch('src.transcription.audio_processor.Path.exists')
    def test_extract_audio_from_video_with_output_path(self, mock_exists, mock_which, mock_run, audio_processor, mock_audio_file):
        """Test extract_audio_from_video with custom output path."""
        mock_exists.return_value = True
        mock_which.return_value = "/usr/bin/ffmpeg"
        
        custom_output = mock_audio_file.with_name(mock_audio_file.stem + "_extracted.wav")
        
        # Mock successful subprocess call
        mock_result = Mock()
        mock_result.returncode = 0
        mock_run.return_value = mock_result
        
        result = audio_processor.extract_audio_from_video(mock_audio_file, custom_output)
        
        assert result == custom_output
        mock_run.assert_called_once()
    
    @patch('src.transcription.audio_processor.subprocess.run')
    @patch('src.transcription.audio_processor.shutil.which')
    @patch('src.transcription.audio_processor.Path.exists')
    def test_extract_audio_from_video_ffmpeg_failure(self, mock_exists, mock_which, mock_run, audio_processor, mock_audio_file):
        """Test extract_audio_from_video when ffmpeg fails."""
        mock_exists.return_value = True
        mock_which.return_value = "/usr/bin/ffmpeg"
        
        # Mock failed subprocess call
        mock_run.side_effect = subprocess.CalledProcessError(1, "ffmpeg", stderr="Error extracting")
        
        result = audio_processor.extract_audio_from_video(mock_audio_file)
        
        assert result is None
    
    @patch('src.transcription.audio_processor.Path.exists')
    def test_get_audio_info_file_not_found(self, mock_exists, audio_processor):
        """Test get_audio_info with non-existent file."""
        mock_exists.return_value = False
        
        result = audio_processor.get_audio_info("nonexistent.wav")
        
        assert result is None
    
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', True)
    @patch('src.transcription.audio_processor.Path.exists')
    @patch('src.transcription.audio_processor.librosa')
    def test_get_audio_info_with_librosa(self, mock_librosa, mock_exists, audio_processor, mock_audio_file):
        """Test get_audio_info using librosa."""
        mock_exists.return_value = True
        
        # Mock librosa functions
        mock_audio = np.array([0.1, 0.2, 0.3, 0.4])
        mock_librosa.load.return_value = (mock_audio, 16000)
        
        result = audio_processor.get_audio_info(mock_audio_file)
        
        assert result is not None
        assert result["duration"] == 4.0 / 16000  # 4 samples at 16kHz
        assert result["sample_rate"] == 16000
        assert result["channels"] == 1
        assert result["format"] == ".wav"
        assert result["file_size"] == mock_audio_file.stat().st_size
    
    @patch('src.transcription.audio_processor.PYDUB_AVAILABLE', True)
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', False)
    @patch('src.transcription.audio_processor.Path.exists')
    @patch('src.transcription.audio_processor.AudioSegment')
    def test_get_audio_info_with_pydub(self, mock_audio_segment, mock_exists, audio_processor, mock_audio_file):
        """Test get_audio_info using pydub."""
        mock_exists.return_value = True
        
        # Mock AudioSegment
        mock_audio = MagicMock()
        mock_audio.frame_rate = 16000
        mock_audio.channels = 1
        mock_audio.__len__.return_value = 1000  # 1000ms
        mock_audio_segment.from_file.return_value = mock_audio
        
        result = audio_processor.get_audio_info(mock_audio_file)
        
        assert result is not None
        assert result["duration"] == 1.0  # 1000ms = 1 second
        assert result["sample_rate"] == 16000
        assert result["channels"] == 1
        assert result["format"] == ".wav"
        assert result["file_size"] == mock_audio_file.stat().st_size
    
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', False)
    @patch('src.transcription.audio_processor.PYDUB_AVAILABLE', False)
    @patch('src.transcription.audio_processor.Path.exists')
    def test_get_audio_info_no_libraries_available(self, mock_exists, audio_processor, mock_audio_file):
        """Test get_audio_info when no audio libraries are available."""
        mock_exists.return_value = True
        
        result = audio_processor.get_audio_info(mock_audio_file)
        
        assert result is None
    
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', True)
    @patch('src.transcription.audio_processor.Path.exists')
    @patch('src.transcription.audio_processor.librosa')
    def test_get_audio_info_librosa_error(self, mock_librosa, mock_exists, audio_processor, mock_audio_file):
        """Test error handling in get_audio_info with librosa."""
        mock_exists.return_value = True
        mock_librosa.load.side_effect = Exception("Librosa error")
        
        result = audio_processor.get_audio_info(mock_audio_file)
        
        assert result is None
    
    @patch('src.transcription.audio_processor.PYDUB_AVAILABLE', True)
    @patch('src.transcription.audio_processor.LIBROSA_AVAILABLE', False)
    @patch('src.transcription.audio_processor.Path.exists')
    @patch('src.transcription.audio_processor.AudioSegment')
    def test_get_audio_info_pydub_error(self, mock_audio_segment, mock_exists, audio_processor, mock_audio_file):
        """Test error handling in get_audio_info with pydub."""
        mock_exists.return_value = True
        mock_audio_segment.from_file.side_effect = Exception("Pydub error")
        
        result = audio_processor.get_audio_info(mock_audio_file)
        
        assert result is None
    
    def test_supported_formats(self, audio_processor):
        """Test that AudioProcessor handles different audio formats."""
        # This test verifies the class can be instantiated with different configs
        # The actual format handling is tested in the processing methods
        
        formats = ["wav", "mp3", "m4a", "flac", "ogg"]
        
        for fmt in formats:
            config = Mock(spec=TranscriptionConfig)
            config.sample_rate = 16000
            config.audio_format = fmt
            config.enable_preprocessing = True
            
            processor = AudioProcessor(config)
            assert processor.config.audio_format == fmt
            assert processor.config.sample_rate == 16000