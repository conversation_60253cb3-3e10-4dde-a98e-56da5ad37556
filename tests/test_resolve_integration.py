#!/usr/bin/env python3
"""
Test Suite for DaVinci Resolve Integration

This test suite validates the integration between AI transcription
and DaVinci Resolve caption functionality.
"""

import unittest
import tempfile
import os
import json
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
import sys
sys.path.insert(0, str(project_root))

from utils.srt_generator import SRTGenerator

# Mock DaVinciResolveScript before importing examples
sys.modules['DaVinciResolveScript'] = Mock()
sys.modules['DaVinciResolveScript'].scriptapp = Mock()

from examples.caption_workflow import ResolveCaptionManager
from examples.ai_to_resolve_captions import AIResolveCaptionWorkflow


class TestSRTGenerator(unittest.TestCase):
    """Test SRT generation functionality"""
    
    def setUp(self):
        self.generator = SRTGenerator()
        self.sample_transcription = {
            "segments": [
                {
                    "start": 0.0,
                    "end": 2.5,
                    "text": "Hello world, this is a test transcription."
                },
                {
                    "start": 3.0,
                    "end": 5.2,
                    "text": "This is the second segment with multiple words."
                }
            ],
            "language": "en",
            "duration": 10.0
        }
    
    def test_load_from_transcription_data(self):
        """Test loading transcription data"""
        success = self.generator.load_from_transcription_data(self.sample_transcription)
        self.assertTrue(success)
        self.assertEqual(len(self.generator.segments), 2)
    
    def test_load_from_whisper_json(self):
        """Test loading Whisper JSON format"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.sample_transcription, f)
            temp_file = f.name
        
        try:
            success = self.generator.load_from_whisper_json(temp_file)
            self.assertTrue(success)
            self.assertEqual(len(self.generator.segments), 2)
        finally:
            os.unlink(temp_file)
    
    def test_validate_segments(self):
        """Test segment validation"""
        self.generator.load_from_transcription_data(self.sample_transcription)
        is_valid = self.generator.validate_segments()
        self.assertTrue(is_valid)
    
    def test_validate_invalid_segments(self):
        """Test validation with invalid segments"""
        invalid_data = {
            "segments": [
                {
                    "start": 5.0,
                    "end": 3.0,  # End before start
                    "text": "Invalid segment"
                },
                {
                    "start": -1.0,  # Negative start time
                    "end": 2.0,
                    "text": "Negative start time"
                }
            ]
        }
        
        self.generator.load_from_transcription_data(invalid_data)
        is_valid = self.generator.validate_segments()
        self.assertFalse(is_valid)  # Should have validation warnings
    
    def test_format_timestamp(self):
        """Test timestamp formatting"""
        result = self.generator.format_timestamp(3661.998)
        expected = "01:01:01,998"
        self.assertEqual(result, expected)
    
    def test_wrap_text(self):
        """Test text wrapping functionality"""
        long_text = "This is a very long line that should be wrapped at the specified maximum length to ensure proper subtitle formatting."
        
        # Add a test segment with long text
        self.generator.add_segment(0, 5, long_text)
        
        # Generate SRT with max line length to test wrapping
        srt_content = self.generator.generate_srt(max_line_length=40)
        self.assertIn("\n", srt_content)
        lines = srt_content.split("\n")
        # Find the text line (should be after sequence and timestamp)
        text_lines = [line for line in lines if line and not line.isdigit() and "-->" not in line]
        if text_lines:
            self.assertLessEqual(len(text_lines[0]), 40)
    
    def test_generate_srt_content(self):
        """Test SRT content generation"""
        self.generator.load_from_transcription_data(self.sample_transcription)
        content = self.generator.generate_srt()
        
        # Check SRT format
        lines = content.strip().split('\n')
        
        # Should have proper SRT format for each segment
        self.assertIn("1", lines)  # First segment number
        self.assertIn("00:00:00,000 --> 00:00:02,500", lines)  # First timestamp
        self.assertIn("Hello world, this is a test transcription.", lines)  # First text
        
        self.assertIn("2", lines)  # Second segment number
        self.assertIn("00:00:03,000 --> 00:00:05,200", lines)  # Second timestamp
    
    def test_save_srt(self):
        """Test saving SRT to file"""
        self.generator.load_from_transcription_data(self.sample_transcription)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False) as f:
            temp_file = f.name
        
        try:
            success = self.generator.save_srt(temp_file)
            self.assertTrue(success)
            
            # Verify file was created and has content
            self.assertTrue(os.path.exists(temp_file))
            with open(temp_file, 'r') as f:
                content = f.read()
            
            self.assertIn("Hello world, this is a test transcription.", content)
            
        finally:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    def test_srt_with_speaker_labels(self):
        """Test SRT generation with speaker labels"""
        speaker_data = {
            "segments": [
                {
                    "start": 0.0,
                    "end": 2.0,
                    "text": "Hello, this is speaker one.",
                    "speaker": "SPEAKER_1"
                },
                {
                    "start": 2.5,
                    "end": 4.0,
                    "text": "And this is speaker two responding.",
                    "speaker": "SPEAKER_2"
                }
            ]
        }
        
        self.generator.load_from_transcription_data(speaker_data)
        content = self.generator.generate_srt(include_speaker_labels=True)
        
        # Check that speaker labels are included
        self.assertIn("[SPEAKER_1]", content)
        self.assertIn("[SPEAKER_2]", content)


class TestResolveCaptionManager(unittest.TestCase):
    """Test Resolve Caption Manager"""
    
    def setUp(self):
        # Mock the connection to avoid trying to connect to real Resolve
        with patch('examples.caption_workflow.dvr_script.scriptapp'):
            self.manager = ResolveCaptionManager()
        
        # Mock Resolve objects
        self.mock_resolve = Mock()
        self.mock_project_manager = Mock()
        self.mock_project = Mock()
        self.mock_timeline = Mock()
        
        self.mock_resolve.GetProjectManager.return_value = self.mock_project_manager
        self.mock_project_manager.GetCurrentProject.return_value = self.mock_project
        self.mock_project.GetCurrentTimeline.return_value = self.mock_timeline
        
        self.manager.resolve = self.mock_resolve
        self.manager.project_manager = self.mock_project_manager
        self.manager.project = self.mock_project
        self.manager.timeline = self.mock_timeline
    
    def test_get_caption_info(self):
        """Test getting caption information"""
        # Mock timeline caption info
        self.mock_timeline.GetCaptionsCount.return_value = 2
        self.mock_timeline.GetCaptions.return_value = ['caption1', 'caption2']  # Mock captions list
        self.mock_timeline.GetCurrentCaption.return_value = 'caption1'  # Mock current caption
        
        result = self.manager.get_caption_info()
        
        # The method returns the caption count from timeline.GetCaptionsCount()
        self.assertEqual(result, 2)
        self.mock_timeline.GetCaptionsCount.assert_called_once()
    
    def test_import_captions_success(self):
        """Test successful caption import"""
        test_file = '/tmp/test.srt'
        with patch('os.path.exists', return_value=True):
            with patch('os.path.getsize', return_value=1024):
                self.mock_timeline.ImportCaptions.return_value = True
                
                result = self.manager.import_captions(test_file, offset=0)
                
                self.assertTrue(result)
                self.mock_timeline.ImportCaptions.assert_called_with(test_file, 0)
    
    def test_import_captions_failure(self):
        """Test failed caption import"""
        test_file = '/tmp/test.srt'
        with patch('os.path.exists', return_value=True):
            with patch('os.path.getsize', return_value=1024):
                self.mock_timeline.ImportCaptions.return_value = False
                
                result = self.manager.import_captions(test_file, offset=0)
                
                self.assertFalse(result)
                self.mock_timeline.ImportCaptions.assert_called_with(test_file, 0)
    
    def test_export_captions(self):
        """Test caption export"""
        export_path = '/tmp/export.srt'
        self.mock_timeline.GetCaptionsCount.return_value = 1
        self.mock_timeline.ExportCaptions.return_value = True
        
        with patch('os.path.exists', return_value=True):
            with patch('os.path.getsize', return_value=2048):
                result = self.manager.export_captions(export_path, format_type="srt", caption_index=1)
                
                self.assertTrue(result)
                self.mock_timeline.ExportCaptions.assert_called_with(export_path, "srt", 1)
    
    def test_batch_process_captions(self):
        """Test batch caption processing"""
        test_folder = '/tmp/captions'
        with patch('os.path.exists', return_value=True):
            with patch('os.listdir', return_value=['test1.srt', 'test2.srt']):
                with patch('os.path.join', side_effect=lambda x, y: f"{x}/{y}"):
                    with patch('os.path.getsize', return_value=1024):  # Mock file size
                        self.mock_timeline.ImportCaptions.return_value = True
                        
                        result = self.manager.batch_import_captions(test_folder)
                        
                        self.assertTrue(result)
                        # Should be called twice for two files
                        self.assertEqual(self.mock_timeline.ImportCaptions.call_count, 2)
    
    def test_format_timestamp_for_resolve(self):
        """Test timestamp formatting for Resolve"""
        # This method doesn't exist in the actual implementation, so skip this test
        self.skipTest("format_timestamp_for_resolve method not implemented")


class TestAIResolveCaptionWorkflow(unittest.TestCase):
    """Test AI to Resolve Caption Workflow Integration"""
    
    def setUp(self):
        # Mock Resolve and AI components
        with patch('examples.ai_to_resolve_captions.AIResolveCaptionWorkflow._initialize_resolve'):
            with patch('examples.ai_to_resolve_captions.AIResolveCaptionWorkflow._initialize_ai_components'):
                self.workflow = AIResolveCaptionWorkflow()
        
        # Setup mock objects
        self.workflow.resolve = Mock()
        self.workflow.project_manager = Mock()
        self.workflow.project = Mock()
        self.workflow.timeline = Mock()
        self.workflow.transcription_engine = Mock()
        self.workflow.audio_processor = Mock()
        self.workflow.srt_generator = Mock()
        
        # Mock timeline info
        self.workflow.timeline.GetName.return_value = "Test Timeline"
        self.workflow.timeline.GetDuration.return_value = 1000
        self.workflow.timeline.GetSetting.return_value = "25"
    
    def test_extract_audio_from_timeline(self):
        """Test audio extraction from timeline"""
        self.workflow.timeline.GetName.return_value = "Test Timeline"
        self.workflow.timeline.GetDuration.return_value = 1000
        self.workflow.timeline.GetSetting.return_value = "25"  # 25fps
        
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
            temp_audio_path = temp_audio.name
        
        try:
            result = self.workflow.extract_audio_from_timeline(temp_audio_path)
            
            self.assertEqual(result, temp_audio_path)
            self.assertTrue(os.path.exists(temp_audio_path))
            self.workflow.timeline.GetName.assert_called_once()
            self.workflow.timeline.GetDuration.assert_called_once()
            self.workflow.timeline.GetSetting.assert_called_with("timelineFrameRate")
        finally:
            if os.path.exists(temp_audio_path):
                os.unlink(temp_audio_path)
    
    def test_generate_ai_transcription(self):
        """Test AI transcription generation"""
        mock_transcription = {
            "segments": [
                {"start": 0.0, "end": 5.0, "text": "Hello world"},
                {"start": 5.0, "end": 10.0, "text": "This is a test"}
            ],
            "language": "en"
        }
        
        with patch.object(self.workflow.transcription_engine, 'transcribe', return_value=mock_transcription):
            with patch.object(self.workflow.audio_processor, 'process_audio', return_value='processed_audio.wav'):
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                    temp_audio_path = temp_audio.name
                    temp_audio.write(b'fake_audio_data')
                
                try:
                    result = self.workflow.generate_ai_transcription(temp_audio_path)
                    
                    self.assertEqual(result, mock_transcription)
                    self.assertEqual(len(result['segments']), 2)
                    self.workflow.audio_processor.process_audio.assert_called_once_with(temp_audio_path)
                    self.workflow.transcription_engine.transcribe.assert_called_once_with('processed_audio.wav')
                finally:
                    if os.path.exists(temp_audio_path):
                        os.unlink(temp_audio_path)
    
    def test_create_srt_from_transcription(self):
        """Test SRT creation from transcription data"""
        transcription_data = {
            "text": "This is a test transcription",
            "segments": [
                {"start": 0.0, "end": 1.0, "text": "This is a test"},
                {"start": 1.0, "end": 2.0, "text": "transcription"}
            ]
        }
        
        with patch.object(self.workflow.srt_generator, 'load_from_transcription_data', return_value=True):
            with patch.object(self.workflow.srt_generator, 'validate_segments', return_value=True):
                with patch.object(self.workflow.srt_generator, 'save_srt', return_value=True) as mock_save:
                    with tempfile.NamedTemporaryFile(suffix=".srt", delete=False) as temp_srt:
                        temp_srt_path = temp_srt.name
                    
                    try:
                        result = self.workflow.create_srt_from_transcription(transcription_data, temp_srt_path)
                        
                        self.assertEqual(result, temp_srt_path)
                        self.workflow.srt_generator.load_from_transcription_data.assert_called_once_with(transcription_data)
                        self.workflow.srt_generator.validate_segments.assert_called_once()
                        mock_save.assert_called_once_with(temp_srt_path, include_speaker_labels=False, max_line_length=80)
                    finally:
                        if os.path.exists(temp_srt_path):
                            os.unlink(temp_srt_path)
    
    def test_import_captions_to_resolve(self):
        """Test importing captions to Resolve"""
        mock_srt_content = """1
00:00:00,000 --> 00:00:05,000
Hello world

2
00:00:05,000 --> 00:00:10,000
This is a test
"""
        
        with tempfile.NamedTemporaryFile(suffix=".srt", delete=False) as temp_srt:
            temp_srt.write(mock_srt_content.encode('utf-8'))
            temp_srt_path = temp_srt.name
        
        try:
            # Mock the timeline's ImportCaptions method
            self.workflow.timeline.ImportCaptions.return_value = True
            self.workflow.timeline.GetCaptionsCount.return_value = 1
            
            result = self.workflow.import_captions_to_resolve(temp_srt_path)
            
            self.assertTrue(result)
            self.workflow.timeline.ImportCaptions.assert_called_once_with(temp_srt_path, 0)
        finally:
            if os.path.exists(temp_srt_path):
                os.unlink(temp_srt_path)
    
    def test_run_complete_workflow_with_audio_file(self):
        """Test complete workflow with audio file"""
        # Create a mock audio file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
            temp_audio_path = temp_audio.name
            # Write minimal WAV header for testing
            temp_audio.write(b'RIFF\x26\x00\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x44\xac\x00\x00\x88X\x01\x00\x02\x00\x10\x00data\x02\x00\x00\x00\x00\x00')
        
        try:
            # Mock the transcription engine and audio processor
            mock_transcription_data = {
                "text": "This is a test transcription",
                "segments": [
                    {"start": 0.0, "end": 1.0, "text": "This is a test"},
                    {"start": 1.0, "end": 2.0, "text": "transcription"}
                ]
            }
            
            with patch.object(self.workflow.audio_processor, 'process_audio', return_value=temp_audio_path):
                with patch.object(self.workflow.transcription_engine, 'transcribe', return_value=mock_transcription_data):
                    with patch.object(self.workflow, 'timeline') as mock_timeline:
                        # Mock timeline methods
                        mock_timeline.GetName.return_value = 'Test Timeline'
                        mock_timeline.ImportCaptions.return_value = True
                        mock_timeline.GetCaptionsCount.return_value = 1
                        
                        result = self.workflow.run_complete_workflow(
                            audio_file=temp_audio_path,
                            offset=0,
                            include_speakers=True
                        )
                        
                        self.assertTrue(result)
                        self.workflow.audio_processor.process_audio.assert_called_once_with(temp_audio_path)
                        self.workflow.transcription_engine.transcribe.assert_called_once_with(temp_audio_path)
                        mock_timeline.ImportCaptions.assert_called_once()
        finally:
            if os.path.exists(temp_audio_path):
                os.unlink(temp_audio_path)


class TestIntegrationErrorHandling(unittest.TestCase):
    """Test error handling in integration components"""
    
    def setUp(self):
        """Set up test fixtures with proper mocking"""
        # Create a mock workflow for error handling tests
        with patch('examples.ai_to_resolve_captions.dvr_script.scriptapp'):
            self.workflow = AIResolveCaptionWorkflow()
    
    def test_srt_generator_error_handling(self):
        """Test SRT generator error handling"""
        generator = SRTGenerator()
        
        # Test with invalid data
        result = generator.load_from_transcription_data(None)
        self.assertFalse(result)
        
        result = generator.load_from_transcription_data({})
        self.assertFalse(result)
        
        result = generator.load_from_transcription_data({"invalid": "data"})
        self.assertFalse(result)
    
    def test_resolve_connection_error(self):
        """Test handling of Resolve connection errors"""
        with patch('builtins.print') as mock_print:
            # Create a new manager with connection error - should raise exception
            with patch('examples.caption_workflow.dvr_script.scriptapp', side_effect=Exception("Connection failed")):
                with self.assertRaises(Exception):
                    manager = ResolveCaptionManager()
                
                # Check that error was printed
                mock_print.assert_called()
    
    def test_ai_transcription_error_handling(self):
        """Test AI transcription error handling"""
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as f:
            audio_file = f.name
            # Write minimal WAV header
            f.write(b'RIFF\x26\x00\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x44\xac\x00\x00\x88X\x01\x00\x02\x00\x10\x00data\x02\x00\x00\x00\x00\x00')
        
        try:
            with patch.object(self.workflow.audio_processor, 'process_audio', side_effect=Exception("Processing failed")):
                with self.assertRaises(Exception):
                    self.workflow.generate_ai_transcription(audio_file)
        finally:
            if os.path.exists(audio_file):
                os.unlink(audio_file)
    
    def test_srt_generation_error_handling(self):
        """Test SRT generation error handling"""
        # Test with invalid transcription data
        invalid_data = {"invalid": "data"}
        
        with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as f:
            srt_file = f.name
        
        try:
            with self.assertRaises(Exception):
                self.workflow.create_srt_from_transcription(invalid_data, srt_file)
        finally:
            if os.path.exists(srt_file):
                os.unlink(srt_file)
    
    def test_resolve_caption_import_error(self):
        """Test Resolve caption import error handling"""
        with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as f:
            srt_file = f.name
            f.write(b'1\n00:00:00,000 --> 00:00:01,000\nTest\n\n')
        
        try:
            # Mock timeline ImportCaptions to raise an exception
            with patch.object(self.workflow, 'timeline') as mock_timeline:
                mock_timeline.GetName.return_value = 'Test Timeline'
                mock_timeline.ImportCaptions.side_effect = Exception("Import failed")
                
                # Should return False when import fails
                result = self.workflow.import_captions_to_resolve(srt_file)
                self.assertFalse(result)
        finally:
            if os.path.exists(srt_file):
                os.unlink(srt_file)
    
    def test_workflow_error_recovery(self):
        """Test workflow error recovery"""
        # Create a mock audio file
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as f:
            audio_file = f.name
            f.write(b'RIFF\x26\x00\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x44\xac\x00\x00\x88X\x01\x00\x02\x00\x10\x00data\x02\x00\x00\x00\x00\x00')
        
        with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as f:
            srt_file = f.name
        
        try:
            with patch.object(self.workflow.audio_processor, 'process_audio', side_effect=Exception("Processing failed")):
                with self.assertRaises(Exception):
                    self.workflow.run_complete_workflow(audio_file=audio_file, srt_file=srt_file)
        finally:
            if os.path.exists(audio_file):
                os.unlink(audio_file)
            if os.path.exists(srt_file):
                os.unlink(srt_file)


if __name__ == '__main__':
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestSRTGenerator))
    suite.addTests(loader.loadTestsFromTestCase(TestResolveCaptionManager))
    suite.addTests(loader.loadTestsFromTestCase(TestAIResolveCaptionWorkflow))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegrationErrorHandling))
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)