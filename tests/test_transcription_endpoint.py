"""
Unit tests for the transcription endpoint.
"""

import pytest
import requests
import tempfile
import numpy as np
import soundfile as sf
import os
import json
from pathlib import Path


class TestTranscriptionEndpoint:
    """Test suite for the transcription endpoint."""
    
    BASE_URL = "http://127.0.0.1:5000"
    TRANSCRIBE_URL = f"{BASE_URL}/api/transcribe"
    
    @classmethod
    def setup_class(cls):
        """Check if server is running before running tests."""
        try:
            response = requests.get(f"{cls.BASE_URL}/api/status", timeout=5)
            if response.status_code != 200:
                pytest.skip("Server not running or not responding")
        except requests.exceptions.ConnectionError:
            pytest.skip("Cannot connect to server. Please start the server with: python src/ui/app.py")
    
    def create_test_audio(self, duration=1.0, sample_rate=16000, frequency=440):
        """Create a test audio file with a sine wave."""
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio = np.sin(frequency * 2 * np.pi * t) * 0.3
        return audio, sample_rate
    
    def create_silent_audio(self, duration=1.0, sample_rate=16000):
        """Create a silent audio file."""
        audio = np.zeros(int(sample_rate * duration))
        return audio, sample_rate
    
    def create_very_short_audio(self, sample_rate=16000):
        """Create a very short audio file (less than 1024 samples)."""
        audio = np.random.random(512) * 0.1  # 512 samples = ~0.032 seconds at 16kHz
        return audio, sample_rate
    
    def upload_audio_file(self, audio_data, sample_rate, additional_data=None):
        """Helper method to upload audio file to the transcription endpoint."""
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
            temp_path = temp_file.name
            sf.write(temp_path, audio_data, sample_rate)
        
        try:
            with open(temp_path, 'rb') as audio_file:
                files = {'audio': audio_file}
                data = {
                    'model': 'faster-whisper',
                    'language': 'auto',
                    'speaker_diarization': 'false',
                    'profanity_filter': 'false'
                }
                if additional_data:
                    data.update(additional_data)
                
                response = requests.post(self.TRANSCRIBE_URL, files=files, data=data)
            return response
        finally:
            try:
                os.unlink(temp_path)
            except:
                pass
    
    def test_server_status(self):
        """Test that the server is running and responding."""
        response = requests.get(f"{self.BASE_URL}/api/status")
        assert response.status_code == 200
    
    def test_transcribe_normal_audio(self):
        """Test transcription with normal audio (sine wave)."""
        audio, sr = self.create_test_audio(duration=2.0, frequency=440)
        response = self.upload_audio_file(audio, sr)
        
        assert response.status_code == 200
        result = response.json()
        
        # Validate response structure
        assert 'success' in result
        assert result['success'] is True
        assert 'transcription' in result
        assert 'transcription_id' in result
        assert 'message' in result
        
        # Validate transcription structure
        transcription = result['transcription']
        assert 'text' in transcription
        assert 'segments' in transcription
        assert 'language' in transcription
        assert 'confidence' in transcription
        assert 'processing_time' in transcription
        
        # Validate data types
        assert isinstance(transcription['text'], str)
        assert isinstance(transcription['segments'], list)
        assert isinstance(transcription['language'], str)
        assert isinstance(transcription['confidence'], (int, float))
        assert isinstance(transcription['processing_time'], (int, float))
        
        # Validate reasonable values
        assert transcription['confidence'] >= 0.0
        assert transcription['processing_time'] >= 0.0
    
    def test_transcribe_silent_audio(self):
        """Test transcription with silent audio."""
        audio, sr = self.create_silent_audio(duration=1.5)
        response = self.upload_audio_file(audio, sr)
        
        assert response.status_code == 200
        result = response.json()
        assert result['success'] is True
        
        # Silent audio should return empty or minimal transcription
        transcription = result['transcription']
        assert isinstance(transcription['text'], str)
        assert isinstance(transcription['segments'], list)
    
    def test_transcribe_very_short_audio(self):
        """Test transcription with very short audio (edge case)."""
        audio, sr = self.create_very_short_audio()
        response = self.upload_audio_file(audio, sr)
        
        assert response.status_code == 200
        result = response.json()
        assert result['success'] is True
        
        # Very short audio should still be processed
        transcription = result['transcription']
        assert 'text' in transcription
        assert 'processing_time' in transcription
    
    def test_transcribe_longer_audio(self):
        """Test transcription with longer audio."""
        audio, sr = self.create_test_audio(duration=5.0, frequency=880)
        response = self.upload_audio_file(audio, sr)
        
        assert response.status_code == 200
        result = response.json()
        assert result['success'] is True
        
        transcription = result['transcription']
        # Longer audio might take more time to process
        assert transcription['processing_time'] >= 0.0
    
    def test_no_audio_file(self):
        """Test request without audio file."""
        data = {'model': 'faster-whisper'}
        response = requests.post(self.TRANSCRIBE_URL, data=data)
        
        assert response.status_code == 400
        result = response.json()
        assert 'error' in result
        assert 'No audio file provided' in result['error']
    
    def test_empty_filename(self):
        """Test request with empty filename."""
        files = {'audio': ('', b'', 'audio/wav')}
        data = {'model': 'faster-whisper'}
        response = requests.post(self.TRANSCRIBE_URL, files=files, data=data)
        
        assert response.status_code == 400
        result = response.json()
        assert 'error' in result
        assert 'No file selected' in result['error']
    
    def test_invalid_file_type(self):
        """Test request with invalid file type."""
        files = {'audio': ('test.txt', b'not audio data', 'text/plain')}
        data = {'model': 'faster-whisper'}
        response = requests.post(self.TRANSCRIBE_URL, files=files, data=data)
        
        assert response.status_code == 400
        result = response.json()
        assert 'error' in result
        assert 'Invalid file type' in result['error']
    
    def test_transcribe_with_different_languages(self):
        """Test transcription with different language settings."""
        audio, sr = self.create_test_audio(duration=1.0)
        
        # Test with specific language
        response = self.upload_audio_file(audio, sr, {'language': 'en'})
        assert response.status_code == 200
        result = response.json()
        assert result['success'] is True
        
        # Test with auto language detection
        response = self.upload_audio_file(audio, sr, {'language': 'auto'})
        assert response.status_code == 200
        result = response.json()
        assert result['success'] is True
    
    def test_transcribe_with_speaker_diarization(self):
        """Test transcription with speaker diarization enabled."""
        audio, sr = self.create_test_audio(duration=2.0)
        
        response = self.upload_audio_file(audio, sr, {'speaker_diarization': 'true'})
        assert response.status_code == 200
        result = response.json()
        assert result['success'] is True
    
    def test_transcribe_with_profanity_filter(self):
        """Test transcription with profanity filter enabled."""
        audio, sr = self.create_test_audio(duration=1.0)
        
        response = self.upload_audio_file(audio, sr, {'profanity_filter': 'true'})
        assert response.status_code == 200
        result = response.json()
        assert result['success'] is True
    
    def test_response_time_reasonable(self):
        """Test that response times are reasonable."""
        import time
        
        audio, sr = self.create_test_audio(duration=1.0)
        
        start_time = time.time()
        response = self.upload_audio_file(audio, sr)
        end_time = time.time()
        
        assert response.status_code == 200
        
        # Response should be reasonably fast (less than 30 seconds for 1 second audio)
        response_time = end_time - start_time
        assert response_time < 30.0, f"Response took too long: {response_time:.2f}s"
        
        # Processing time should be reported in the response
        result = response.json()
        processing_time = result['transcription']['processing_time']
        assert processing_time > 0.0
        assert processing_time < response_time  # Processing time should be less than total response time


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])