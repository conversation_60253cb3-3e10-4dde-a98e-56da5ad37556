"""
Test suite for UI connection functionality.
Tests the Flask app's ability to handle DaVinci Resolve connections properly.
"""

import pytest
import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ui.app import app, initialize_app
from src.core.orchestrator import AIOrchestrator
from src.core.config_manager import ConfigManager


class TestUIConnection:
    """Test class for UI connection functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        app.config['TESTING'] = True
        self.client = app.test_client()
        
    def test_app_initialization(self):
        """Test that the app initializes properly with async orchestrator."""
        # Test that initialization completes without errors
        result = initialize_app()
        assert result is True, "App initialization should succeed"
        
    def test_resolve_status_endpoint(self):
        """Test the resolve status endpoint."""
        response = self.client.get('/api/resolve-status')
        assert response.status_code == 200
        
        data = response.get_json()
        assert 'connected' in data
        assert 'current_project' in data
        assert 'current_timeline' in data
        
    def test_resolve_connect_endpoint(self):
        """Test the resolve connect endpoint."""
        # Ensure app is initialized first
        initialize_app()
        
        response = self.client.post('/api/resolve-connect')
        assert response.status_code in [200, 500]  # 500 is expected if DaVinci Resolve isn't running
        
        data = response.get_json()
        assert 'message' in data or 'error' in data
        
    def test_orchestrator_async_initialization(self):
        """Test that the orchestrator initializes properly with async methods."""
        config_manager = ConfigManager()
        orchestrator = AIOrchestrator(config_manager)
        
        # Test async initialization
        async def test_init():
            await orchestrator.initialize()
            assert orchestrator.resolve_bridge is not None
            assert orchestrator.transcription_engine is not None
            assert orchestrator.ai_engine is not None
            
        # Run the async test
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        loop.run_until_complete(test_init())
        
    def test_resolve_bridge_connection(self):
        """Test that the resolve bridge can handle connection attempts."""
        config_manager = ConfigManager()
        orchestrator = AIOrchestrator(config_manager)
        
        async def test_connection():
            await orchestrator.initialize()
            
            # Test connection (will be in mock mode if DaVinci Resolve isn't running)
            result = await orchestrator.resolve_bridge.connect()
            assert isinstance(result, bool)
            
            # Test status check
            status = orchestrator.resolve_bridge.connected
            assert isinstance(status, bool)
            
        # Run the async test
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        loop.run_until_complete(test_connection())


if __name__ == '__main__':
    # Run the tests
    test_instance = TestUIConnection()
    test_instance.setup_method()
    
    print("Running UI connection tests...")
    
    try:
        test_instance.test_app_initialization()
        print("✅ App initialization test passed")
        
        test_instance.test_resolve_status_endpoint()
        print("✅ Resolve status endpoint test passed")
        
        test_instance.test_resolve_connect_endpoint()
        print("✅ Resolve connect endpoint test passed")
        
        test_instance.test_orchestrator_async_initialization()
        print("✅ Orchestrator async initialization test passed")
        
        test_instance.test_resolve_bridge_connection()
        print("✅ Resolve bridge connection test passed")
        
        print("\n🎉 All tests passed! The DaVinci Resolve connection functionality is working properly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise