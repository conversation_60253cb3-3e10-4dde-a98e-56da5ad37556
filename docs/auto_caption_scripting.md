# Auto-Caption Settings Scripting Guide

This guide covers scripting for auto-caption settings in DaVinci Resolve, based on the official DaVinci Resolve Scripting API documentation.

## Overview

Auto-caption functionality in DaVinci Resolve can be controlled through scripting, allowing for automated caption generation and management. This is particularly useful for batch processing and workflow automation.

## Key Objects and Methods

### Timeline Object Methods

The Timeline object provides methods for working with captions:

#### GetCaptionsCount()
Returns the total number of caption tracks in the timeline.
```python
count = timeline.GetCaptionsCount()
```

#### GetCaptions()
Returns a list of all caption tracks in the timeline.
```python
captions = timeline.GetCaptions()
```

#### GetCurrentCaption()
Returns the currently selected caption track.
```python
current_caption = timeline.GetCurrentCaption()
```

#### SetCurrentCaption(captionIndex)
Sets the current caption track by index (1-based).
```python
timeline.SetCurrentCaption(1)  # Select first caption track
```

#### ImportCaptions(filePath, offset)
Imports captions from a file with optional time offset.
```python
# Import SRT file with 0 frame offset
timeline.ImportCaptions("/path/to/captions.srt", 0)
```

#### ExportCaptions(filePath, exportType, captionIndex)
Exports captions to a file.
```python
# Export first caption track as SRT
timeline.ExportCaptions("/path/to/export.srt", "srt", 1)
```

## Auto-Caption Settings

### Caption Format Support
DaVinci Resolve supports various caption formats:
- **SRT**: SubRip Subtitle format
- **SCC**: Scenarist Closed Caption format
- **VTT**: WebVTT format
- **TTML**: Timed Text Markup Language

### Import Settings
When importing captions, consider:
- **Time Offset**: Number of frames to offset the captions
- **Frame Rate**: Ensure caption timing matches timeline frame rate
- **Encoding**: UTF-8 encoding is recommended for compatibility

### Export Settings
When exporting captions:
- **File Path**: Full path to the export location
- **Export Type**: Format specification ("srt", "scc", "vtt", "ttml")
- **Caption Index**: 1-based index of the caption track to export

## Practical Examples

### Basic Caption Operations
```python
#!/usr/bin/env python
import DaVinciResolveScript as dvr_script

# Get Resolve instance
resolve = dvr_script.scriptapp("Resolve")
projectManager = resolve.GetProjectManager()
project = projectManager.GetCurrentProject()
timeline = project.GetCurrentTimeline()

# Get caption information
caption_count = timeline.GetCaptionsCount()
print(f"Number of caption tracks: {caption_count}")

# Get all caption tracks
captions = timeline.GetCaptions()
for i, caption in enumerate(captions):
    print(f"Caption track {i+1}: {caption}")
```

### Import and Export Workflow
```python
def import_captions_with_validation(timeline, caption_file, offset=0):
    """Import captions with error checking"""
    try:
        # Check if file exists
        import os
        if not os.path.exists(caption_file):
            print(f"Error: Caption file not found: {caption_file}")
            return False
        
        # Import captions
        result = timeline.ImportCaptions(caption_file, offset)
        if result:
            print(f"Successfully imported captions from {caption_file}")
            return True
        else:
            print(f"Failed to import captions from {caption_file}")
            return False
    except Exception as e:
        print(f"Error importing captions: {e}")
        return False

def export_captions_with_validation(timeline, export_path, format_type="srt", caption_index=1):
    """Export captions with error checking"""
    try:
        # Validate caption index
        caption_count = timeline.GetCaptionsCount()
        if caption_index > caption_count:
            print(f"Error: Caption index {caption_index} exceeds available tracks ({caption_count})")
            return False
        
        # Export captions
        result = timeline.ExportCaptions(export_path, format_type, caption_index)
        if result:
            print(f"Successfully exported captions to {export_path}")
            return True
        else:
            print(f"Failed to export captions to {export_path}")
            return False
    except Exception as e:
        print(f"Error exporting captions: {e}")
        return False
```

### Batch Caption Processing
```python
def batch_process_captions(project_folder, import_format="srt", export_format="scc"):
    """Batch process captions for multiple timelines"""
    resolve = dvr_script.scriptapp("Resolve")
    projectManager = resolve.GetProjectManager()
    project = projectManager.GetCurrentProject()
    
    # Get all timelines
    timeline_count = project.GetTimelineCount()
    
    for i in range(1, timeline_count + 1):
        timeline = project.GetTimelineByIndex(i)
        timeline_name = timeline.GetName()
        
        print(f"Processing timeline: {timeline_name}")
        
        # Look for caption files with matching names
        import_file = f"{project_folder}/{timeline_name}.{import_format}"
        export_file = f"{project_folder}/{timeline_name}_exported.{export_format}"
        
        # Import captions if file exists
        if os.path.exists(import_file):
            if timeline.ImportCaptions(import_file, 0):
                print(f"  ✓ Imported captions from {import_file}")
                
                # Export in different format
                if timeline.ExportCaptions(export_file, export_format, 1):
                    print(f"  ✓ Exported captions to {export_file}")
                else:
                    print(f"  ✗ Failed to export captions")
            else:
                print(f"  ✗ Failed to import captions from {import_file}")
        else:
            print(f"  - No caption file found for {timeline_name}")
```

## Auto-Caption Generation Integration

While DaVinci Resolve doesn't have built-in auto-caption generation through scripting, you can integrate external transcription services:

### External Transcription Workflow
```python
def create_captions_from_transcription(timeline, transcription_data, output_file):
    """Create caption file from transcription data"""
    import json
    
    # Assuming transcription_data contains time-coded segments
    captions = []
    
    for segment in transcription_data.get('segments', []):
        start_time = segment['start']
        end_time = segment['end']
        text = segment['text'].strip()
        
        # Convert to SRT format
        caption = {
            'start': start_time,
            'end': end_time,
            'text': text
        }
        captions.append(caption)
    
    # Write SRT file
    with open(output_file, 'w', encoding='utf-8') as f:
        for i, caption in enumerate(captions, 1):
            f.write(f"{i}\n")
            f.write(f"{format_time(caption['start'])} --> {format_time(caption['end'])}\n")
            f.write(f"{caption['text']}\n\n")
    
    return output_file

def format_time(seconds):
    """Convert seconds to SRT time format"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"
```

## Error Handling Best Practices

### Validation Functions
```python
def validate_caption_operation(timeline, operation="import"):
    """Validate caption operation prerequisites"""
    errors = []
    
    # Check if timeline exists
    if not timeline:
        errors.append("No active timeline")
        return False, errors
    
    # Check timeline properties
    try:
        timeline_name = timeline.GetName()
        duration = timeline.GetDuration()
        frame_rate = timeline.GetSetting("timelineFrameRate")
        
        if duration == 0:
            errors.append("Timeline has zero duration")
        
        if not frame_rate:
            errors.append("Unable to determine timeline frame rate")
            
    except Exception as e:
        errors.append(f"Error accessing timeline properties: {e}")
    
    return len(errors) == 0, errors

def safe_caption_operation(timeline, operation_func, *args, **kwargs):
    """Safely execute caption operations with error handling"""
    try:
        # Validate before operation
        is_valid, errors = validate_caption_operation(timeline)
        if not is_valid:
            return False, f"Validation failed: {'; '.join(errors)}"
        
        # Execute operation
        result = operation_func(*args, **kwargs)
        
        if result:
            return True, "Operation completed successfully"
        else:
            return False, "Operation returned False (check Resolve logs for details)"
            
    except Exception as e:
        return False, f"Exception during operation: {e}"
```

## Performance Considerations

### Optimization Tips
1. **Batch Operations**: Group multiple caption operations to reduce API calls
2. **File Validation**: Validate files before import to avoid Resolve errors
3. **Memory Management**: Large caption files may impact performance
4. **Timeline Updates**: Consider timeline updates when modifying captions

### Monitoring Progress
```python
def import_captions_with_progress(timeline, caption_file, offset=0):
    """Import captions with progress monitoring"""
    import time
    
    start_time = time.time()
    print(f"Starting caption import from {caption_file}")
    
    # Get file size for progress estimation
    file_size = os.path.getsize(caption_file)
    print(f"File size: {file_size} bytes")
    
    # Perform import
    result = timeline.ImportCaptions(caption_file, offset)
    
    end_time = time.time()
    duration = end_time - start_time
    
    if result:
        print(f"✓ Import completed in {duration:.2f} seconds")
        
        # Verify import
        new_count = timeline.GetCaptionsCount()
        print(f"Current caption track count: {new_count}")
        
        return True
    else:
        print(f"✗ Import failed after {duration:.2f} seconds")
        return False
```

## Integration with AI Transcription

This auto-caption scripting can be integrated with AI transcription services like the one in this project:

### Workflow Integration
```python
def integrate_ai_transcription(timeline, audio_file, transcription_service):
    """Integrate AI transcription with caption creation"""
    
    # Step 1: Generate transcription using AI service
    print("Generating AI transcription...")
    transcription_result = transcription_service.transcribe(audio_file)
    
    if not transcription_result:
        print("Failed to generate transcription")
        return False
    
    # Step 2: Create caption file from transcription
    caption_file = f"{audio_file}.srt"
    print(f"Creating caption file: {caption_file}")
    
    caption_file_path = create_captions_from_transcription(
        timeline, 
        transcription_result, 
        caption_file
    )
    
    # Step 3: Import captions into timeline
    print("Importing captions into timeline...")
    success = timeline.ImportCaptions(caption_file_path, 0)
    
    if success:
        print("✓ AI transcription successfully converted to captions")
        return True
    else:
        print("✗ Failed to import AI-generated captions")
        return False
```

## Troubleshooting

### Common Issues

1. **Import Failures**
   - Check file format compatibility
   - Verify file encoding (UTF-8 recommended)
   - Ensure timeline frame rate matches caption timing

2. **Export Failures**
   - Verify caption track exists
   - Check export path permissions
   - Ensure sufficient disk space

3. **Timing Issues**
   - Caption timing may need adjustment based on timeline frame rate
   - Consider timecode offset when importing
   - Verify timeline start timecode

### Debug Functions
```python
def debug_caption_info(timeline):
    """Debug function to display caption information"""
    try:
        print("=== Caption Debug Information ===")
        print(f"Timeline: {timeline.GetName()}")
        print(f"Duration: {timeline.GetDuration()} frames")
        
        caption_count = timeline.GetCaptionsCount()
        print(f"Caption tracks: {caption_count}")
        
        if caption_count > 0:
            captions = timeline.GetCaptions()
            for i, caption in enumerate(captions):
                print(f"Track {i+1}: {caption}")
        
        current_caption = timeline.GetCurrentCaption()
        print(f"Current caption: {current_caption}")
        
        return True
    except Exception as e:
        print(f"Debug error: {e}")
        return False
```

## Resources

- [Official DaVinci Resolve Scripting Documentation](https://extremraym.com/cloud/resolve-scripting-doc/#auto-caption-settings)
- DaVinci Resolve User Manual for detailed caption workflow information
- Blackmagic Design Forum for community support and examples

## Version Compatibility

This guide is based on DaVinci Resolve scripting API documentation. Caption-related APIs may vary between Resolve versions. Always test scripts with your specific Resolve version.