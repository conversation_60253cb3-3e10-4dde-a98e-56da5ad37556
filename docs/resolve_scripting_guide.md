# DaVinci Resolve Scripting Guide

This guide provides comprehensive information about scripting in DaVinci Resolve, based on the official DaVinci Resolve Scripting API documentation.

## Overview

DaVinci Resolve supports scripting in both Lua and Python programming languages. Scripts can be invoked from:
- The Console window in the Fusion page
- Command line interface
- Workspace application menu under Scripts
- External applications (with proper configuration)

## Prerequisites

DaVinci Resolve scripting requires one of the following to be installed (for all users):
- Lua 5.1
- Python >= 3.6 64-bit
- Python 2.7 64-bit

## Environment Setup

### Environment Variables

Set these environment variables to allow Python to pick up the appropriate dependencies:

#### macOS
```bash
export RESOLVE_SCRIPT_API="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Developer/Scripting"
export RESOLVE_SCRIPT_LIB="/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Libraries/Fusion/fusionscript.so"
export PYTHONPATH="$PYTHONPATH:$RESOLVE_SCRIPT_API/Modules/"
```

#### Windows
```cmd
set RESOLVE_SCRIPT_API=%PROGRAMDATA%\Blackmagic Design\DaVinci Resolve\Support\Developer\Scripting
set RESOLVE_SCRIPT_LIB=C:\Program Files\Blackmagic Design\DaVinci Resolve\fusionscript.dll
set PYTHONPATH=%PYTHONPATH%;%RESOLVE_SCRIPT_API%\Modules\
```

#### Linux
```bash
export RESOLVE_SCRIPT_API="/opt/resolve/Developer/Scripting"
export RESOLVE_SCRIPT_LIB="/opt/resolve/libs/Fusion/fusionscript.so"
export PYTHONPATH="$PYTHONPATH:$RESOLVE_SCRIPT_API/Modules/"
```

Note: For standard ISO Linux installations, paths may need to be modified to refer to `/home/<USER>/opt/resolve`.

## Basic Script Structure

### Python Script Example
```python
#!/usr/bin/env python
import DaVinciResolveScript as dvr_script

# Get the Resolve application object
resolve = dvr_script.scriptapp("Resolve")

# Access Fusion for additional scripting functionality
fusion = resolve.Fusion()

# Get project manager
projectManager = resolve.GetProjectManager()

# Create a new project
projectManager.CreateProject("Hello World")
```

### Key Objects

1. **resolve** - The fundamental starting point for scripting via Resolve
2. **fusion** - Provides access to all existing Fusion scripting functionality
3. **projectManager** - Manages projects and provides project-level operations

## Script Locations

DaVinci Resolve scans these directories for scripts and enumerates them in the Workspace application menu:

### macOS
- All users: `/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts`
- Specific user: `/Users/<USER>/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts`

### Windows
- All users: `%PROGRAMDATA%\Blackmagic Design\DaVinci Resolve\Fusion\Scripts`
- Specific user: `%APPDATA%\Roaming\Blackmagic Design\DaVinci Resolve\Support\Fusion\Scripts`

### Linux
- All users: `/opt/resolve/Fusion/Scripts` (or `/home/<USER>/Fusion/Scripts/` depending on installation)
- Specific user: `$HOME/.local/share/DaVinci Resolve/Fusion/Scripts`

### Script Folder Organization
- **Utility**: Scripts listed in all pages
- **Comp** or **Tool**: Scripts available in the Fusion page
- **Edit**, **Color**, or **Deliver**: Scripts for individual pages
- **Deliver**: Scripts are additionally listed under render jobs

## Running Scripts

### From Console
The interactive Console window allows executing simple scripting commands to query or modify properties and test scripts. The console accepts commands in Python 2.7, Python 3.6, and Lua.

### Headless Mode
DaVinci Resolve can be launched in headless mode without the user interface using the `-nogui` command line option. When launched this way, the user interface is disabled but scripting APIs continue to work.

## Security Considerations

Script execution permissions can be configured in Resolve Preferences:
- Console only
- Console and command line
- Console, command line, and network access

Be aware of security implications when allowing scripting access from outside the Resolve application.

## Important API Changes

From v16.2.0 onwards, the `nodeIndex` parameters accepted by `SetLUT()` and `SetCDL()` are 1-based instead of 0-based (i.e., 1 <= nodeIndex <= total number of nodes).

## Common Scripting Tasks

### Project Management
```python
# Get current project manager
projectManager = resolve.GetProjectManager()

# Create new project
projectManager.CreateProject("Project Name")

# Load existing project
projectManager.LoadProject("Project Name")

# Get current project
currentProject = projectManager.GetCurrentProject()
```

### Timeline Operations
```python
# Get current timeline
currentTimeline = currentProject.GetCurrentTimeline()

# Get timeline name
timelineName = currentTimeline.GetName()

# Get timeline duration
duration = currentTimeline.GetDuration()
```

### Media Pool Operations
```python
# Get media pool
mediaPool = currentProject.GetMediaPool()

# Get root folder
rootFolder = mediaPool.GetRootFolder()

# Add media to media pool
mediaPool.AddItemsToMediaPool("/path/to/media/file.mov")
```

## Troubleshooting

### Common Issues

1. **Script API not found**: Ensure environment variables are set correctly
2. **Permission denied**: Check Resolve preferences for scripting permissions
3. **Module import errors**: Verify Python/Lua installation and PATH settings
4. **Resolve not responding**: Ensure Resolve is running before executing scripts

### Debugging Tips

1. Use the Console window for testing individual commands
2. Check Resolve logs for error messages
3. Use `print()` statements for debugging (visible in Console)
4. Test scripts in small sections before full execution

## Advanced Topics

### Fusion Integration
Access Fusion-specific functionality through the fusion object:
```python
fusion = resolve.Fusion()
comp = fusion.GetCurrentComp()
```

### Custom UI Creation
Create custom user interfaces using Fusion's UI manager (available in Studio version):
```python
ui = fusion.UIManager
```

### Batch Processing
Scripts can be used for batch processing multiple files or projects:
```python
import os

# Process all video files in a directory
for filename in os.listdir("/path/to/videos"):
    if filename.endswith(".mov"):
        # Process each file
        pass
```

## Resources

- [Official DaVinci Resolve Scripting Documentation](https://extremraym.com/cloud/resolve-scripting-doc/)
- DaVinci Resolve User Manual for Console usage
- Blackmagic Design Forum for community support

## Version Compatibility

This guide is based on DaVinci Resolve scripting API documentation. Always check your Resolve version for specific API changes and compatibility.