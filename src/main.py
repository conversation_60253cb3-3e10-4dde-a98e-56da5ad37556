"""
DaVinci Resolve AI Assistant - Main Application

This is the main entry point for the DaVinci Resolve AI Assistant.
It coordinates all components and provides the user interface.
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Optional

# Add src directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.orchestrator import AIOrchestrator
from src.core.config_manager import ConfigManager
from src.core.workflow_manager import WorkflowManager

# Optional UI import - only load if needed
try:
    import tkinter as tk
    from ui.app import DaVinciAIAssistantApp
    UI_AVAILABLE = True
except ImportError:
    UI_AVAILABLE = False
    DaVinciAIAssistantApp = None

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('davinci_ai_assistant.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class DaVinciAIAssistant:
    """
    Main application class for DaVinci Resolve AI Assistant.
    """
    
    def __init__(self):
        """Initialize the AI Assistant."""
        self.config_manager = None
        self.orchestrator = None
        self.workflow_manager = None
        self.ui_app = None
        self.running = False
        
    async def initialize(self):
        """
        Initialize all components of the AI Assistant.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            logger.info("Initializing DaVinci Resolve AI Assistant...")
            
            # Load configuration
            self.config_manager = ConfigManager()
            self.config_manager.load_config()
            
            # Initialize AI orchestrator with faster-whisper
            self.orchestrator = AIOrchestrator(self.config_manager)
            await self.orchestrator.initialize()
            
            # Initialize workflow manager
            self.workflow_manager = WorkflowManager(self.orchestrator, self.config_manager)
            
            logger.info("AI Assistant initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize AI Assistant: {e}")
            return False
            
    async def start_ui(self):
        """
        Start the user interface.
        
        Returns:
            True if UI started successfully, False otherwise
        """
        try:
            logger.info("Starting AI Assistant UI...")
            
            # Check if UI is available
            if not UI_AVAILABLE:
                logger.error("UI not available - tkinter not installed")
                logger.info("Running in CLI mode instead...")
                await self.run_cli_mode()
                return True
            
            # Create UI application
            self.ui_app = DaVinciAIAssistantApp(
                orchestrator=self.orchestrator,
                workflow_manager=self.workflow_manager,
                config_manager=self.config_manager
            )
            
            # Start UI (this will block until UI is closed)
            await self.ui_app.run()
            
            logger.info("AI Assistant UI started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start AI Assistant UI: {e}")
            return False
            
    async def run_cli_mode(self, timeline_path: Optional[str] = None):
        """
        Run the AI Assistant in CLI mode.
        
        Args:
            timeline_path: Optional path to timeline for analysis
        """
        try:
            logger.info("Running AI Assistant in CLI mode...")
            
            if timeline_path:
                # Analyze specific timeline
                logger.info(f"Analyzing timeline: {timeline_path}")
                result = await self.workflow_manager.execute_timeline_analysis(timeline_path)
                
                if result.success:
                    logger.info("Timeline analysis completed successfully")
                    logger.info(f"Processing time: {result.execution_time:.2f}s")
                    logger.info(f"Steps completed: {result.steps_completed}/{result.total_steps}")
                    
                    # Print transcription results
                    if 'transcribe_audio' in result.results:
                        transcription_data = result.results['transcribe_audio']
                        logger.info(f"Transcription: {transcription_data['text'][:200]}...")
                        logger.info(f"Language: {transcription_data['language']}")
                        
                else:
                    logger.error("Timeline analysis failed")
                    for error in result.errors:
                        logger.error(f"Error: {error}")
                        
            else:
                # Interactive CLI mode
                logger.info("Entering interactive CLI mode...")
                await self._interactive_cli()
                
        except Exception as e:
            logger.error(f"CLI mode execution failed: {e}")
            
    async def _interactive_cli(self):
        """Run interactive CLI mode."""
        print("\nDaVinci Resolve AI Assistant - Interactive Mode")
        print("=" * 50)
        print("Commands:")
        print("  analyze - Analyze current timeline")
        print("  connect - Connect to DaVinci Resolve")
        print("  status  - Show connection status")
        print("  config  - Show configuration")
        print("  quit    - Exit application")
        print("=" * 50)
        
        while True:
            try:
                command = input("\nAI Assistant> ").strip().lower()
                
                if command == 'quit':
                    break
                elif command == 'analyze':
                    result = await self.workflow_manager.execute_timeline_analysis()
                    if result.success:
                        print(f"Analysis completed in {result.execution_time:.2f}s")
                        print(f"Transcription: {result.results.get('transcribe_audio', {}).get('text', 'N/A')[:100]}...")
                    else:
                        print("Analysis failed:")
                        for error in result.errors:
                            print(f"  - {error}")
                            
                elif command == 'connect':
                    success = await self.orchestrator.resolve_bridge.connect()
                    print(f"Connection {'successful' if success else 'failed'}")
                    
                elif command == 'status':
                    connected = self.orchestrator.resolve_bridge.connected
                    print(f"DaVinci Resolve: {'Connected' if connected else 'Disconnected'}")
                    if connected:
                        project_info = await self.orchestrator.resolve_bridge.get_current_project()
                        if project_info:
                            print(f"Project: {project_info.name}")
                            print(f"Timelines: {project_info.timeline_count}")
                            
                elif command == 'config':
                    config = self.config_manager.get_config()
                    print(f"Transcription Engine: {config.get('transcription', {}).get('engine', 'N/A')}")
                    print(f"Model Size: {config.get('transcription', {}).get('model_size', 'N/A')}")
                    print(f"Apple Silicon: {config.get('transcription', {}).get('use_metal', False)}")
                    
                else:
                    print(f"Unknown command: {command}")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"CLI error: {e}")
                
    async def cleanup(self):
        """Clean up resources."""
        try:
            logger.info("Cleaning up AI Assistant resources...")
            
            if self.orchestrator:
                await self.orchestrator.cleanup()
                
            if self.ui_app:
                await self.ui_app.cleanup()
                
            logger.info("AI Assistant cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            
    async def run(self, cli_mode: bool = False, timeline_path: Optional[str] = None):
        """
        Run the AI Assistant.
        
        Args:
            cli_mode: Run in CLI mode instead of GUI
            timeline_path: Optional timeline path for CLI analysis
        """
        try:
            self.running = True
            
            # Initialize
            if not await self.initialize():
                logger.error("Failed to initialize AI Assistant")
                return
                
            # Run in selected mode
            if cli_mode:
                await self.run_cli_mode(timeline_path)
            else:
                await self.start_ui()
                
        except KeyboardInterrupt:
            logger.info("AI Assistant interrupted by user")
        except Exception as e:
            logger.error(f"AI Assistant runtime error: {e}")
        finally:
            await self.cleanup()
            self.running = False


async def main():
    """Main entry point."""
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='DaVinci Resolve AI Assistant')
    parser.add_argument('--cli', action='store_true', help='Run in CLI mode')
    parser.add_argument('--timeline', type=str, help='Timeline path for analysis (CLI mode)')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    
    args = parser.parse_args()
    
    # Set debug level if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        
    # Create and run AI Assistant
    assistant = DaVinciAIAssistant()
    
    try:
        await assistant.run(cli_mode=args.cli, timeline_path=args.timeline)
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the application
    asyncio.run(main())