"""
AI Model Utilities

This module provides utilities for working with AI model providers,
including default model configurations and helper functions.
"""

from typing import Dict, List, Any
from .model_providers import ModelProviderType, ModelConfig

# Default models for each provider
DEFAULT_MODELS = {
    ModelProviderType.OPENROUTER: [
        "anthropic/claude-3.5-sonnet",
        "anthropic/claude-3-haiku",
        "openai/gpt-4o",
        "openai/gpt-4o-mini",
        "google/gemini-pro-1.5",
        "meta-llama/llama-3.1-405b-instruct",
        "meta-llama/llama-3.1-70b-instruct",
        "meta-llama/llama-3.1-8b-instruct",
        "qwen/qwen-2.5-72b-instruct",
        "deepseek/deepseek-chat"
    ],
    
    ModelProviderType.QWEN: [
        "qwen-turbo",
        "qwen-plus", 
        "qwen-max",
        "qwen-max-1201",
        "qwen-max-longcontext",
        "qwen2.5-72b-instruct",
        "qwen2.5-32b-instruct",
        "qwen2.5-14b-instruct",
        "qwen2.5-7b-instruct",
        "qwen2.5-3b-instruct",
        "qwen2.5-1.5b-instruct",
        "qwen2.5-0.5b-instruct",
        "qwen2.5-coder-32b-instruct",
        "qwen2.5-coder-14b-instruct",
        "qwen2.5-coder-7b-instruct"
    ],
    
    ModelProviderType.DEEPSEEK: [
        "deepseek-chat",
        "deepseek-coder",
        "deepseek-reasoner"
    ],
    
    ModelProviderType.KIMI: [
        "moonshot-v1-8k",
        "moonshot-v1-32k",
        "moonshot-v1-128k"
    ],
    
    ModelProviderType.GLM: [
        "glm-4",
        "glm-4-0520",
        "glm-4-air",
        "glm-4-airx",
        "glm-4-flash",
        "glm-4-plus",
        "glm-4v",
        "glm-4v-plus"
    ],
    
    ModelProviderType.LOCAL_LMSTUDIO: [
        # These will be dynamically populated from LM Studio
        "local-model"
    ],
    
    ModelProviderType.LOCAL_OLLAMA: [
        # Common Ollama models
        "llama3.2:latest",
        "llama3.1:latest", 
        "llama3:latest",
        "mistral:latest",
        "codellama:latest",
        "phi3:latest",
        "gemma2:latest",
        "qwen2.5:latest",
        "deepseek-coder:latest"
    ]
}

# Default base URLs for each provider
DEFAULT_BASE_URLS = {
    ModelProviderType.OPENROUTER: "https://openrouter.ai/api/v1",
    ModelProviderType.QWEN: "https://dashscope.aliyuncs.com/api/v1",
    ModelProviderType.DEEPSEEK: "https://api.deepseek.com",
    ModelProviderType.KIMI: "https://api.moonshot.cn",
    ModelProviderType.GLM: "https://open.bigmodel.cn/api/paas",
    ModelProviderType.LOCAL_LMSTUDIO: "http://localhost:1234",
    ModelProviderType.LOCAL_OLLAMA: "http://localhost:11434"
}

# Model context windows (approximate)
MODEL_CONTEXT_WINDOWS = {
    # OpenRouter models
    "anthropic/claude-3.5-sonnet": 200000,
    "anthropic/claude-3-haiku": 200000,
    "openai/gpt-4o": 128000,
    "openai/gpt-4o-mini": 128000,
    "google/gemini-pro-1.5": 1000000,
    "meta-llama/llama-3.1-405b-instruct": 128000,
    "meta-llama/llama-3.1-70b-instruct": 128000,
    "meta-llama/llama-3.1-8b-instruct": 128000,
    
    # Qwen models
    "qwen-turbo": 8192,
    "qwen-plus": 32768,
    "qwen-max": 8192,
    "qwen-max-1201": 32768,
    "qwen-max-longcontext": 1000000,
    "qwen2.5-72b-instruct": 32768,
    "qwen2.5-32b-instruct": 32768,
    "qwen2.5-14b-instruct": 32768,
    "qwen2.5-7b-instruct": 32768,
    
    # DeepSeek models
    "deepseek-chat": 32768,
    "deepseek-coder": 16384,
    "deepseek-reasoner": 32768,
    
    # Kimi models
    "moonshot-v1-8k": 8192,
    "moonshot-v1-32k": 32768,
    "moonshot-v1-128k": 131072,
    
    # GLM models
    "glm-4": 128000,
    "glm-4-0520": 128000,
    "glm-4-air": 128000,
    "glm-4-airx": 128000,
    "glm-4-flash": 128000,
    "glm-4-plus": 128000,
    "glm-4v": 128000,
    "glm-4v-plus": 128000
}

def get_default_models(provider_type: ModelProviderType) -> List[str]:
    """
    Get default models for a provider type.
    
    Args:
        provider_type: The provider type
        
    Returns:
        List of default model names
    """
    return DEFAULT_MODELS.get(provider_type, [])

def get_default_base_url(provider_type: ModelProviderType) -> str:
    """
    Get default base URL for a provider type.
    
    Args:
        provider_type: The provider type
        
    Returns:
        Default base URL
    """
    return DEFAULT_BASE_URLS.get(provider_type, "")

def get_model_context_window(model_name: str) -> int:
    """
    Get context window size for a model.
    
    Args:
        model_name: The model name
        
    Returns:
        Context window size in tokens
    """
    return MODEL_CONTEXT_WINDOWS.get(model_name, 4096)  # Default fallback

def create_model_config(
    provider_type: ModelProviderType,
    model_name: str,
    api_key: str | None = None,
    base_url: str | None = None,
    **kwargs
) -> ModelConfig:
    """
    Create a model configuration with sensible defaults.
    
    Args:
        provider_type: The provider type
        model_name: The model name
        api_key: API key (if required)
        base_url: Base URL (uses default if not provided)
        **kwargs: Additional configuration parameters
        
    Returns:
        ModelConfig object
    """
    if base_url is None:
        base_url = get_default_base_url(provider_type)
    
    return ModelConfig(
        provider_type=provider_type,
        model_name=model_name,
        api_key=api_key,
        base_url=base_url,
        **kwargs
    )

def is_local_provider(provider_type: ModelProviderType) -> bool:
    """
    Check if a provider type is for local models.
    
    Args:
        provider_type: The provider type
        
    Returns:
        True if local provider, False otherwise
    """
    return provider_type in [
        ModelProviderType.LOCAL_LMSTUDIO,
        ModelProviderType.LOCAL_OLLAMA
    ]

def requires_api_key(provider_type: ModelProviderType) -> bool:
    """
    Check if a provider type requires an API key.
    
    Args:
        provider_type: The provider type
        
    Returns:
        True if API key required, False otherwise
    """
    return provider_type in [
        ModelProviderType.OPENROUTER,
        ModelProviderType.QWEN,
        ModelProviderType.DEEPSEEK,
        ModelProviderType.KIMI,
        ModelProviderType.GLM
    ]

def get_provider_display_name(provider_type: ModelProviderType) -> str:
    """
    Get human-readable display name for a provider.
    
    Args:
        provider_type: The provider type
        
    Returns:
        Display name
    """
    display_names = {
        ModelProviderType.OPENROUTER: "OpenRouter",
        ModelProviderType.QWEN: "Qwen (Alibaba Cloud)",
        ModelProviderType.DEEPSEEK: "DeepSeek",
        ModelProviderType.KIMI: "Kimi (Moonshot AI)",
        ModelProviderType.GLM: "GLM (Zhipu AI)",
        ModelProviderType.LOCAL_LMSTUDIO: "LM Studio (Local)",
        ModelProviderType.LOCAL_OLLAMA: "Ollama (Local)"
    }
    return display_names.get(provider_type, provider_type.value)

def get_provider_description(provider_type: ModelProviderType) -> str:
    """
    Get description for a provider.
    
    Args:
        provider_type: The provider type
        
    Returns:
        Provider description
    """
    descriptions = {
        ModelProviderType.OPENROUTER: "Access to multiple AI models through OpenRouter API",
        ModelProviderType.QWEN: "Alibaba Cloud's Qwen models with strong multilingual capabilities",
        ModelProviderType.DEEPSEEK: "DeepSeek's powerful coding and reasoning models",
        ModelProviderType.KIMI: "Moonshot AI's Kimi models with long context support",
        ModelProviderType.GLM: "Zhipu AI's GLM models with vision and text capabilities",
        ModelProviderType.LOCAL_LMSTUDIO: "Local models running through LM Studio",
        ModelProviderType.LOCAL_OLLAMA: "Local models running through Ollama"
    }
    return descriptions.get(provider_type, f"AI models from {provider_type.value}")

def validate_model_config(config: ModelConfig) -> List[str]:
    """
    Validate a model configuration and return any errors.
    
    Args:
        config: The model configuration to validate
        
    Returns:
        List of validation error messages (empty if valid)
    """
    errors = []
    
    # Check if API key is required but missing
    if requires_api_key(config.provider_type) and not config.api_key:
        provider_name = get_provider_display_name(config.provider_type)
        errors.append(f"API key is required for {provider_name}")
    
    # Check if base URL is provided
    if not config.base_url:
        errors.append("Base URL is required")
    
    # Check if model name is provided
    if not config.model_name:
        errors.append("Model name is required")
    
    # Validate temperature range
    if not (0.0 <= config.temperature <= 2.0):
        errors.append("Temperature must be between 0.0 and 2.0")
    
    # Validate top_p range
    if not (0.0 <= config.top_p <= 1.0):
        errors.append("Top-p must be between 0.0 and 1.0")
    
    # Validate max_tokens
    if config.max_tokens <= 0:
        errors.append("Max tokens must be greater than 0")
    
    # Validate timeout
    if config.timeout <= 0:
        errors.append("Timeout must be greater than 0")
    
    return errors