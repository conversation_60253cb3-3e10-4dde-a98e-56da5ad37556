"""
Editing Suggestions Generator for AI-powered video editing recommendations
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from .content_analyzer import ContentAnalysis

logger = logging.getLogger(__name__)

@dataclass
class CutSuggestion:
    """Represents a suggested cut point."""
    time: float
    reason: str
    confidence: float
    type: str = 'cut'
    
@dataclass
class TransitionSuggestion:
    """Represents a suggested transition."""
    type: str
    from_time: float
    to_time: float
    reason: str
    confidence: float
    
@dataclass
class EffectSuggestion:
    """Represents a suggested effect."""
    type: str
    time: float
    parameters: Dict[str, Any]
    reason: str
    confidence: float

@dataclass
class EditingSuggestions:
    """Complete set of editing suggestions."""
    cuts: List[CutSuggestion]
    transitions: List[TransitionSuggestion]
    effects: List[EffectSuggestion]
    analysis: ContentAnalysis
    
class EditingSuggestionsGenerator:
    """Generates editing suggestions based on content analysis."""
    
    def __init__(self):
        """Initialize the suggestions generator."""
        self.min_segment_duration = 2.0  # Minimum segment duration in seconds
        self.max_segment_duration = 10.0  # Maximum segment duration in seconds
        
    def generate_suggestions(self, content_analysis: ContentAnalysis) -> EditingSuggestions:
        """
        Generate editing suggestions based on content analysis.
        
        Args:
            content_analysis: Content analysis results
            
        Returns:
            EditingSuggestions object with recommendations
        """
        logger.info("Generating editing suggestions")
        
        # Generate different types of suggestions
        cuts = self._suggest_cuts(content_analysis)
        transitions = self._suggest_transitions(content_analysis)
        effects = self._suggest_effects(content_analysis)
        
        suggestions = EditingSuggestions(
            cuts=cuts,
            transitions=transitions,
            effects=effects,
            analysis=content_analysis
        )
        
        logger.info(f"Generated {len(cuts)} cuts, {len(transitions)} transitions, {len(effects)} effects")
        return suggestions
        
    def _suggest_cuts(self, analysis: ContentAnalysis) -> List[CutSuggestion]:
        """Suggest cut points based on content analysis."""
        cuts = []
        
        # Analyze segments for natural cut points
        for i, segment in enumerate(analysis.segments):
            if i == 0:
                continue  # Skip first segment
                
            start_time = segment.get('start', 0)
            end_time = segment.get('end', 0)
            text = segment.get('text', '')
            duration = end_time - start_time
            
            # Suggest cuts at natural pauses
            if duration > self.max_segment_duration:
                # Suggest cut in the middle of long segments
                cut_time = start_time + (duration / 2)
                cuts.append(CutSuggestion(
                    time=cut_time,
                    reason="Long segment - natural break point",
                    confidence=0.7
                ))
            
            # Suggest cuts at sentence endings
            if text.strip().endswith(('.', '!', '?')):
                cuts.append(CutSuggestion(
                    time=end_time,
                    reason="Natural sentence ending",
                    confidence=0.8
                ))
            
            # Suggest cuts at topic changes
            if i > 0 and self._detect_topic_change(analysis.segments[i-1], segment):
                cuts.append(CutSuggestion(
                    time=start_time,
                    reason="Topic change detected",
                    confidence=0.6
                ))
        
        return cuts
        
    def _suggest_transitions(self, analysis: ContentAnalysis) -> List[TransitionSuggestion]:
        """Suggest transitions based on content analysis."""
        transitions = []
        
        # Add fade in at the beginning
        if analysis.duration > 0:
            transitions.append(TransitionSuggestion(
                type="fade",
                from_time=0.0,
                to_time=1.0,
                reason="Smooth introduction",
                confidence=0.9
            ))
        
        # Add fade out at the end
        if analysis.duration > 2.0:
            transitions.append(TransitionSuggestion(
                type="fade",
                from_time=analysis.duration - 1.0,
                to_time=analysis.duration,
                reason="Smooth ending",
                confidence=0.9
            ))
        
        # Suggest transitions based on sentiment changes
        for i in range(len(analysis.segments) - 1):
            current_sentiment = self._estimate_segment_sentiment(analysis.segments[i])
            next_sentiment = self._estimate_segment_sentiment(analysis.segments[i + 1])
            
            if current_sentiment != next_sentiment:
                start_time = analysis.segments[i + 1].get('start', 0)
                transitions.append(TransitionSuggestion(
                    type="cross_dissolve",
                    from_time=start_time - 0.5,
                    to_time=start_time + 0.5,
                    reason=f"Sentiment change from {current_sentiment} to {next_sentiment}",
                    confidence=0.7
                ))
        
        return transitions
        
    def _suggest_effects(self, analysis: ContentAnalysis) -> List[EffectSuggestion]:
        """Suggest effects based on content analysis."""
        effects = []
        
        # Add text overlay for key topics
        for i, topic in enumerate(analysis.key_topics[:3]):  # Top 3 topics
            if analysis.segments:
                segment_index = min(i, len(analysis.segments) - 1)
                segment = analysis.segments[segment_index]
                time = segment.get('start', 0)
                
                effects.append(EffectSuggestion(
                    type="text_overlay",
                    time=time,
                    parameters={
                        "text": topic.capitalize(),
                        "position": "bottom_center",
                        "duration": 3.0,
                        "font_size": 24
                    },
                    reason=f"Highlight key topic: {topic}",
                    confidence=0.8
                ))
        
        # Add audio enhancement for low confidence segments
        for segment in analysis.segments:
            if segment.get('confidence', 1.0) < 0.7:
                effects.append(EffectSuggestion(
                    type="audio_enhance",
                    time=segment.get('start', 0),
                    parameters={
                        "enhancement_type": "noise_reduction",
                        "duration": segment.get('end', 0) - segment.get('start', 0)
                    },
                    reason="Low confidence transcription - enhance audio",
                    confidence=0.6
                ))
        
        return effects
        
    def _detect_topic_change(self, prev_segment: Dict[str, Any], current_segment: Dict[str, Any]) -> bool:
        """Detect if there's a topic change between segments."""
        prev_text = prev_segment.get('text', '').lower()
        current_text = current_segment.get('text', '').lower()
        
        # Simple heuristic: look for transition words
        transition_words = ['however', 'but', 'meanwhile', 'alternatively', 'on the other hand', 'in contrast']
        
        return any(word in current_text for word in transition_words)
        
    def _estimate_segment_sentiment(self, segment: Dict[str, Any]) -> str:
        """Estimate sentiment of a segment."""
        text = segment.get('text', '').lower()
        
        positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'perfect']
        negative_words = ['bad', 'terrible', 'awful', 'horrible', 'hate', 'worst', 'disappointing']
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'