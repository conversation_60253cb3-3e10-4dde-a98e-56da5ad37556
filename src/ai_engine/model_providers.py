"""
AI Model Provider Abstraction Layer

This module provides a unified interface for different AI model providers,
including OpenRouter, local models (LM Studio, Ollama), and open-source APIs.
"""

import logging
import requests
import json
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ModelProviderType(Enum):
    """Enumeration of supported model provider types."""
    OPENROUTER = "openrouter"
    LOCAL_LMSTUDIO = "local_lmstudio"
    LOCAL_OLLAMA = "local_ollama"
    QWEN = "qwen"
    DEEPSEEK = "deepseek"
    KIMI = "kimi"
    GLM = "glm"

@dataclass
class ModelResponse:
    """Standardized response from AI model providers."""
    content: str
    model: str
    provider: str
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ModelConfig:
    """Configuration for AI model providers."""
    provider_type: ModelProviderType
    model_name: str
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    max_tokens: int = 4000
    temperature: float = 0.7
    top_p: float = 0.9
    timeout: int = 60
    extra_params: Optional[Dict[str, Any]] = None

class BaseModelProvider(ABC):
    """Abstract base class for AI model providers."""
    
    def __init__(self, config: ModelConfig):
        """Initialize the model provider with configuration."""
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> ModelResponse:
        """Generate a response from the AI model."""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the model provider is available."""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the model."""
        pass

class OpenRouterProvider(BaseModelProvider):
    """OpenRouter API provider for various AI models."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        if not config.api_key:
            raise ValueError("OpenRouter API key is required")
        
        self.base_url = config.base_url or "https://openrouter.ai/api/v1"
        self.headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/your-repo",  # Optional
            "X-Title": "DaVinci Resolve AI Assistant"  # Optional
        }
    
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> ModelResponse:
        """Generate response using OpenRouter API."""
        try:
            payload = {
                "model": self.config.model_name,
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "top_p": kwargs.get("top_p", self.config.top_p),
            }
            
            # Add any extra parameters
            if self.config.extra_params:
                payload.update(self.config.extra_params)
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            return ModelResponse(
                content=data["choices"][0]["message"]["content"],
                model=data.get("model", self.config.model_name),
                provider="openrouter",
                usage=data.get("usage"),
                metadata={"response_id": data.get("id")}
            )
            
        except Exception as e:
            self.logger.error(f"OpenRouter API error: {e}")
            raise

    def is_available(self) -> bool:
        """Check if OpenRouter API is available."""
        try:
            response = requests.get(
                f"{self.base_url}/models",
                headers=self.headers,
                timeout=10
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get OpenRouter model information."""
        return {
            "provider": "openrouter",
            "model": self.config.model_name,
            "base_url": self.base_url,
            "supports_streaming": True,
            "context_window": "varies by model"
        }

class LocalLMStudioProvider(BaseModelProvider):
    """Local LM Studio provider for local AI models."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        self.base_url = config.base_url or "http://localhost:1234"
    
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> ModelResponse:
        """Generate response using local LM Studio."""
        try:
            payload = {
                "model": self.config.model_name,
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "top_p": kwargs.get("top_p", self.config.top_p),
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            return ModelResponse(
                content=data["choices"][0]["message"]["content"],
                model=data.get("model", self.config.model_name),
                provider="local_lmstudio",
                usage=data.get("usage"),
                metadata={"local_server": self.base_url}
            )
            
        except Exception as e:
            self.logger.error(f"LM Studio API error: {e}")
            raise

    def is_available(self) -> bool:
        """Check if LM Studio server is available."""
        try:
            response = requests.get(
                f"{self.base_url}/v1/models",
                timeout=5
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get LM Studio model information."""
        return {
            "provider": "local_lmstudio",
            "model": self.config.model_name,
            "base_url": self.base_url,
            "supports_streaming": True,
            "context_window": "depends on loaded model"
        }

class LocalOllamaProvider(BaseModelProvider):
    """Local Ollama provider for local AI models."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        self.base_url = config.base_url or "http://localhost:11434"
    
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> ModelResponse:
        """Generate response using local Ollama."""
        try:
            # Convert messages to Ollama format
            prompt = self._convert_messages_to_prompt(messages)
            
            payload = {
                "model": self.config.model_name,
                "prompt": prompt,
                "options": {
                    "num_predict": kwargs.get("max_tokens", self.config.max_tokens),
                    "temperature": kwargs.get("temperature", self.config.temperature),
                    "top_p": kwargs.get("top_p", self.config.top_p),
                }
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            return ModelResponse(
                content=data.get("response", ""),
                model=data.get("model", self.config.model_name),
                provider="local_ollama",
                usage={"total_duration": data.get("total_duration")},
                metadata={"local_server": self.base_url}
            )
            
        except Exception as e:
            self.logger.error(f"Ollama API error: {e}")
            raise
    
    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Convert OpenAI-style messages to a single prompt for Ollama."""
        prompt_parts = []
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        return "\n\n".join(prompt_parts) + "\n\nAssistant:"

    def is_available(self) -> bool:
        """Check if Ollama server is available."""
        try:
            response = requests.get(
                f"{self.base_url}/api/tags",
                timeout=5
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get Ollama model information."""
        return {
            "provider": "local_ollama",
            "model": self.config.model_name,
            "base_url": self.base_url,
            "supports_streaming": True,
            "context_window": "depends on loaded model"
        }

class QwenProvider(BaseModelProvider):
    """Qwen (Alibaba Cloud) API provider."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        if not config.api_key:
            raise ValueError("Qwen API key is required")
        
        self.base_url = config.base_url or "https://dashscope.aliyuncs.com/api/v1"
        self.headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
    
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> ModelResponse:
        """Generate response using Qwen API."""
        try:
            payload = {
                "model": self.config.model_name,
                "input": {
                    "messages": messages
                },
                "parameters": {
                    "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                    "temperature": kwargs.get("temperature", self.config.temperature),
                    "top_p": kwargs.get("top_p", self.config.top_p),
                }
            }
            
            response = requests.post(
                f"{self.base_url}/services/aigc/text-generation/generation",
                headers=self.headers,
                json=payload,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            return ModelResponse(
                content=data["output"]["text"],
                model=data.get("model", self.config.model_name),
                provider="qwen",
                usage=data.get("usage"),
                metadata={"request_id": data.get("request_id")}
            )
            
        except Exception as e:
            self.logger.error(f"Qwen API error: {e}")
            raise

    def is_available(self) -> bool:
        """Check if Qwen API is available."""
        try:
            # Simple health check - attempt to get model info
            response = requests.get(
                f"{self.base_url}/models",
                headers=self.headers,
                timeout=10
            )
            return response.status_code in [200, 404]  # 404 might be expected for some endpoints
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get Qwen model information."""
        return {
            "provider": "qwen",
            "model": self.config.model_name,
            "base_url": self.base_url,
            "supports_streaming": True,
            "context_window": "varies by model"
        }

class DeepSeekProvider(BaseModelProvider):
    """DeepSeek API provider."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        if not config.api_key:
            raise ValueError("DeepSeek API key is required")
        
        self.base_url = config.base_url or "https://api.deepseek.com"
        self.headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
    
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> ModelResponse:
        """Generate response using DeepSeek API."""
        try:
            payload = {
                "model": self.config.model_name,
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "top_p": kwargs.get("top_p", self.config.top_p),
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            return ModelResponse(
                content=data["choices"][0]["message"]["content"],
                model=data.get("model", self.config.model_name),
                provider="deepseek",
                usage=data.get("usage"),
                metadata={"response_id": data.get("id")}
            )
            
        except Exception as e:
            self.logger.error(f"DeepSeek API error: {e}")
            raise

    def is_available(self) -> bool:
        """Check if DeepSeek API is available."""
        try:
            response = requests.get(
                f"{self.base_url}/v1/models",
                headers=self.headers,
                timeout=10
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get DeepSeek model information."""
        return {
            "provider": "deepseek",
            "model": self.config.model_name,
            "base_url": self.base_url,
            "supports_streaming": True,
            "context_window": "varies by model"
        }

class KimiProvider(BaseModelProvider):
    """Kimi (Moonshot AI) API provider."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        if not config.api_key:
            raise ValueError("Kimi API key is required")
        
        self.base_url = config.base_url or "https://api.moonshot.cn"
        self.headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
    
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> ModelResponse:
        """Generate response using Kimi API."""
        try:
            payload = {
                "model": self.config.model_name,
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "top_p": kwargs.get("top_p", self.config.top_p),
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            return ModelResponse(
                content=data["choices"][0]["message"]["content"],
                model=data.get("model", self.config.model_name),
                provider="kimi",
                usage=data.get("usage"),
                metadata={"response_id": data.get("id")}
            )
            
        except Exception as e:
            self.logger.error(f"Kimi API error: {e}")
            raise

    def is_available(self) -> bool:
        """Check if Kimi API is available."""
        try:
            response = requests.get(
                f"{self.base_url}/v1/models",
                headers=self.headers,
                timeout=10
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get Kimi model information."""
        return {
            "provider": "kimi",
            "model": self.config.model_name,
            "base_url": self.base_url,
            "supports_streaming": True,
            "context_window": "varies by model"
        }

class GLMProvider(BaseModelProvider):
    """GLM (Zhipu AI) API provider."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        if not config.api_key:
            raise ValueError("GLM API key is required")
        
        self.base_url = config.base_url or "https://open.bigmodel.cn/api/paas"
        self.headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
    
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> ModelResponse:
        """Generate response using GLM API."""
        try:
            payload = {
                "model": self.config.model_name,
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "top_p": kwargs.get("top_p", self.config.top_p),
            }
            
            response = requests.post(
                f"{self.base_url}/v4/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.config.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            
            return ModelResponse(
                content=data["choices"][0]["message"]["content"],
                model=data.get("model", self.config.model_name),
                provider="glm",
                usage=data.get("usage"),
                metadata={"response_id": data.get("id")}
            )
            
        except Exception as e:
            self.logger.error(f"GLM API error: {e}")
            raise

    def is_available(self) -> bool:
        """Check if GLM API is available."""
        try:
            response = requests.get(
                f"{self.base_url}/v4/models",
                headers=self.headers,
                timeout=10
            )
            return response.status_code == 200
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get GLM model information."""
        return {
            "provider": "glm",
            "model": self.config.model_name,
            "base_url": self.base_url,
            "supports_streaming": True,
            "context_window": "varies by model"
        }

class ModelProviderFactory:
    """Factory class for creating model providers."""
    
    _providers = {
        ModelProviderType.OPENROUTER: OpenRouterProvider,
        ModelProviderType.LOCAL_LMSTUDIO: LocalLMStudioProvider,
        ModelProviderType.LOCAL_OLLAMA: LocalOllamaProvider,
        ModelProviderType.QWEN: QwenProvider,
        ModelProviderType.DEEPSEEK: DeepSeekProvider,
        ModelProviderType.KIMI: KimiProvider,
        ModelProviderType.GLM: GLMProvider,
    }
    
    @classmethod
    def create_provider(cls, config: ModelConfig) -> BaseModelProvider:
        """Create a model provider based on configuration."""
        provider_class = cls._providers.get(config.provider_type)
        if not provider_class:
            raise ValueError(f"Unsupported provider type: {config.provider_type}")
        
        return provider_class(config)
    
    @classmethod
    def get_supported_providers(cls) -> List[ModelProviderType]:
        """Get list of supported provider types."""
        return list(cls._providers.keys())

class ModelManager:
    """Manager class for handling multiple model providers with fallback support."""
    
    def __init__(self, primary_config: ModelConfig, fallback_configs: Optional[List[ModelConfig]] = None):
        """Initialize model manager with primary and fallback configurations."""
        self.primary_provider = ModelProviderFactory.create_provider(primary_config)
        self.fallback_providers = []
        
        if fallback_configs:
            for config in fallback_configs:
                try:
                    provider = ModelProviderFactory.create_provider(config)
                    self.fallback_providers.append(provider)
                except Exception as e:
                    logger.warning(f"Failed to create fallback provider {config.provider_type}: {e}")
    
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> ModelResponse:
        """Generate response with automatic fallback support."""
        providers_to_try = [self.primary_provider] + self.fallback_providers
        
        for i, provider in enumerate(providers_to_try):
            try:
                if provider.is_available():
                    response = provider.generate_response(messages, **kwargs)
                    if i > 0:
                        logger.info(f"Used fallback provider: {provider.config.provider_type}")
                    return response
                else:
                    logger.warning(f"Provider {provider.config.provider_type} is not available")
            except Exception as e:
                logger.error(f"Provider {provider.config.provider_type} failed: {e}")
                if i == len(providers_to_try) - 1:  # Last provider
                    raise
                continue
        
        raise RuntimeError("All model providers failed")
    
    def get_available_providers(self) -> List[Dict[str, Any]]:
        """Get information about available providers."""
        available = []
        all_providers = [self.primary_provider] + self.fallback_providers
        
        for provider in all_providers:
            try:
                if provider.is_available():
                    info = provider.get_model_info()
                    info["status"] = "available"
                    available.append(info)
                else:
                    info = provider.get_model_info()
                    info["status"] = "unavailable"
                    available.append(info)
            except Exception as e:
                available.append({
                    "provider": provider.config.provider_type.value,
                    "model": provider.config.model_name,
                    "status": "error",
                    "error": str(e)
                })
        
        return available