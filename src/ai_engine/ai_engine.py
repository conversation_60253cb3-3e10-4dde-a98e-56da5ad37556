"""
Main AI Engine that coordinates content analysis and editing suggestions
"""

import logging
from typing import Dict, Any, Optional
from .content_analyzer import ContentAnalyzer, ContentAnalysis
from .editing_suggestions import EditingSuggestionsGenerator, EditingSuggestions
from .model_providers import Model<PERSON>anager, ModelProviderFactory, ModelProviderType
from .model_utils import create_model_config, get_provider_display_name

# Import with fallback for development
try:
    from ..transcription import TranscriptionResult
except ImportError:
    # Fallback for development/testing
    TranscriptionResult = None

try:
    from ..core.config_manager import ConfigManager
except ImportError:
    # Fallback for development/testing
    ConfigManager = None

logger = logging.getLogger(__name__)

class AIEngine:
    """
    Main AI engine that coordinates content analysis and editing suggestions.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the AI engine.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.content_analyzer = ContentAnalyzer()
        self.suggestions_generator = EditingSuggestionsGenerator()
        
        # Initialize configuration manager
        self.config_manager = ConfigManager()
        
        # Initialize model manager
        self.model_manager = None
        self._initialize_model_manager()
        
        logger.info("AI Engine initialized")
    
    def _initialize_model_manager(self):
        """Initialize the model manager with current configuration."""
        try:
            if ConfigManager is None:
                logger.warning("ConfigManager not available, using default model configuration")
                return
            
            # Get AI configuration
            ai_config = self.config_manager.get_ai_config()
            provider_type = ai_config.get('provider_type', 'openrouter')
            
            # Create model configuration
            model_config = create_model_config(
                provider_type=ModelProviderType(provider_type),
                model_name=ai_config.get('primary_model', 'anthropic/claude-3.5-sonnet'),
                api_key=self.config_manager.get_ai_api_key(provider_type),
                base_url=self.config_manager.get_ai_base_url(provider_type)
            )
            
            # Initialize model manager
            self.model_manager = ModelManager(
                primary_config=model_config,
                fallback_configs=self._get_fallback_configs()
            )
            
            logger.info(f"Model manager initialized with provider: {provider_type}")
            
        except Exception as e:
            logger.error(f"Failed to initialize model manager: {e}")
            self.model_manager = None
    
    def _get_fallback_configs(self) -> list:
        """Get fallback model configurations."""
        fallback_configs = []
        
        try:
            ai_config = self.config_manager.get_ai_config()
            fallback_models = ai_config.get('fallback_models', [])
            
            for model_name in fallback_models:
                # For simplicity, use the same provider for fallbacks
                provider_type = ai_config.get('provider_type', 'openrouter')
                
                config = create_model_config(
                    provider_type=ModelProviderType(provider_type),
                    model_name=model_name,
                    api_key=self.config_manager.get_ai_api_key(provider_type),
                    base_url=self.config_manager.get_ai_base_url(provider_type)
                )
                fallback_configs.append(config)
                
        except Exception as e:
            logger.error(f"Failed to get fallback configurations: {e}")
        
        return fallback_configs
    
    def get_available_providers(self) -> Dict[str, Any]:
        """Get information about available model providers."""
        providers = {}
        
        for provider_type in ModelProviderType:
            providers[provider_type.value] = {
                'name': get_provider_display_name(provider_type),
                'requires_api_key': provider_type not in [
                    ModelProviderType.LOCAL_LMSTUDIO,
                    ModelProviderType.LOCAL_OLLAMA
                ],
                'is_local': provider_type in [
                    ModelProviderType.LOCAL_LMSTUDIO,
                    ModelProviderType.LOCAL_OLLAMA
                ]
            }
        
        return providers
    
    def switch_model_provider(self, provider_type: str, model_name: str | None = None) -> bool:
        """Switch to a different model provider."""
        try:
            if ConfigManager is None:
                logger.error("ConfigManager not available")
                return False
            
            # Update configuration
            self.config_manager.set_ai_provider_type(provider_type)
            
            if model_name:
                self.config_manager.set_ai_primary_model(model_name)
            
            # Reinitialize model manager
            self._initialize_model_manager()
            
            logger.info(f"Switched to provider: {provider_type}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to switch model provider: {e}")
            return False
        
    def analyze_content(self, transcription_result) -> ContentAnalysis:
        """
        Analyze transcribed content for insights.
        
        Args:
            transcription_result: Transcription result to analyze
            
        Returns:
            ContentAnalysis object with insights
        """
        logger.info("Starting content analysis")
        
        try:
            analysis = self.content_analyzer.analyze_transcription(transcription_result)
            logger.info("Content analysis completed successfully")
            return analysis
            
        except Exception as e:
            logger.error(f"Content analysis failed: {e}")
            raise RuntimeError(f"Failed to analyze content: {e}")
            
    def generate_editing_suggestions(self, content_analysis: ContentAnalysis) -> EditingSuggestions:
        """
        Generate editing suggestions based on content analysis.
        
        Args:
            content_analysis: Content analysis results
            
        Returns:
            EditingSuggestions object with recommendations
        """
        logger.info("Generating editing suggestions")
        
        try:
            suggestions = self.suggestions_generator.generate_suggestions(content_analysis)
            logger.info("Editing suggestions generated successfully")
            return suggestions
            
        except Exception as e:
            logger.error(f"Failed to generate editing suggestions: {e}")
            raise RuntimeError(f"Failed to generate editing suggestions: {e}")
            
    def process_transcription(self, transcription_result: TranscriptionResult) -> Dict[str, Any]:
        """
        Complete processing pipeline: analyze content and generate suggestions.
        
        Args:
            transcription_result: Transcription result to process
            
        Returns:
            Dictionary containing analysis and suggestions
        """
        logger.info("Processing transcription through AI pipeline")
        
        try:
            # Analyze content
            content_analysis = self.analyze_content(transcription_result)
            
            # Generate editing suggestions
            editing_suggestions = self.generate_editing_suggestions(content_analysis)
            
            # Compile results
            result = {
                'analysis': content_analysis,
                'suggestions': editing_suggestions,
                'success': True
            }
            
            logger.info("AI processing pipeline completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"AI processing pipeline failed: {e}")
            return {
                'error': str(e),
                'success': False
            }
            
    def get_engine_info(self) -> Dict[str, Any]:
        """
        Get information about the AI engine.
        
        Returns:
            Dictionary containing engine information
        """
        return {
            'engine_type': 'rule_based',
            'version': '1.0.0',
            'capabilities': [
                'content_analysis',
                'sentiment_analysis',
                'topic_extraction',
                'editing_suggestions',
                'cut_detection',
                'transition_suggestions',
                'effect_recommendations'
            ],
            'config': self.config
        }