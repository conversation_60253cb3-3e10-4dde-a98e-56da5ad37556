"""
Content Analyzer for AI-powered video content analysis
"""

import logging
import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from ..transcription import TranscriptionResult

logger = logging.getLogger(__name__)

@dataclass
class ContentAnalysis:
    """Results of content analysis."""
    key_topics: List[str]
    sentiment: str
    speakers: List[str]
    duration: float
    word_count: int
    language: str
    confidence: float
    segments: List[Dict[str, Any]]
    
class ContentAnalyzer:
    """Analyzes transcribed content for editing insights."""
    
    def __init__(self):
        """Initialize the content analyzer."""
        self.sentiment_keywords = {
            'positive': ['great', 'amazing', 'excellent', 'wonderful', 'fantastic', 'love', 'perfect', 'awesome'],
            'negative': ['terrible', 'awful', 'bad', 'horrible', 'hate', 'worst', 'disappointing', 'frustrated'],
            'neutral': ['okay', 'fine', 'normal', 'standard', 'basic', 'regular']
        }
        
    def analyze_transcription(self, transcription_result: TranscriptionResult) -> ContentAnalysis:
        """
        Analyze transcribed content for key insights.
        
        Args:
            transcription_result: Transcription result to analyze
            
        Returns:
            ContentAnalysis object with insights
        """
        logger.info("Starting content analysis")
        
        text = transcription_result.text
        segments = transcription_result.segments or []
        
        # Extract key topics
        key_topics = self._extract_key_topics(text)
        
        # Analyze sentiment
        sentiment = self._analyze_sentiment(text)
        
        # Identify speakers (if available)
        speakers = self._identify_speakers(segments)
        
        # Calculate metrics
        word_count = len(text.split())
        duration = segments[-1]['end'] if segments else 0.0
        language = transcription_result.language or 'unknown'
        confidence = transcription_result.confidence or 0.8
        
        analysis = ContentAnalysis(
            key_topics=key_topics,
            sentiment=sentiment,
            speakers=speakers,
            duration=duration,
            word_count=word_count,
            language=language,
            confidence=confidence,
            segments=segments
        )
        
        logger.info(f"Content analysis completed: {word_count} words, {len(key_topics)} topics, sentiment: {sentiment}")
        return analysis
        
    def _extract_key_topics(self, text: str) -> List[str]:
        """Extract key topics from text."""
        # Simple keyword extraction - can be enhanced with NLP libraries
        words = re.findall(r'\b[a-zA-Z]{4,}\b', text.lower())
        
        # Filter out common stop words
        stop_words = {'this', 'that', 'with', 'from', 'they', 'have', 'will', 'been', 'were', 'said', 'each', 'which', 'their', 'would', 'there', 'could', 'should'}
        
        # Count word frequency
        word_freq = {}
        for word in words:
            if word not in stop_words and len(word) > 3:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Return top 5 most frequent words as topics
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:5]]
        
    def _analyze_sentiment(self, text: str) -> str:
        """Analyze sentiment of the text."""
        text_lower = text.lower()
        
        sentiment_scores = {}
        for sentiment, keywords in self.sentiment_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            sentiment_scores[sentiment] = score
        
        # Return sentiment with highest score, default to neutral
        if max(sentiment_scores.values()) == 0:
            return 'neutral'
            
        return max(sentiment_scores, key=sentiment_scores.get)
        
    def _identify_speakers(self, segments: List[Dict[str, Any]]) -> List[str]:
        """Identify speakers from transcription segments."""
        speakers = set()
        
        for segment in segments:
            if 'speaker' in segment:
                speakers.add(segment['speaker'])
                
        return list(speakers) if speakers else ['Speaker 1']