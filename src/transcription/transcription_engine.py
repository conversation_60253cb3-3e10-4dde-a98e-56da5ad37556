"""
Transcription engine using faster-whisper for optimal performance on Apple Silicon.
"""

import logging
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any, List, Union
import torch
from loguru import logger

try:
    from faster_whisper import WhisperModel
    FASTER_WHISPER_AVAILABLE = True
except ImportError:
    logger.warning("faster-whisper not available, transcription will be disabled")
    FASTER_WHISPER_AVAILABLE = False

from .config import TranscriptionConfig, TranscriptionModel, TranscriptionLanguage
from .audio_processor import AudioProcessor


class TranscriptionResult:
    """Container for transcription results."""
    
    def __init__(self, text: str, segments: List[Dict[str, Any]], language: str, 
                 confidence: float = 1.0, processing_time: float = 0.0):
        self.text = text
        self.segments = segments
        self.language = language
        self.confidence = confidence
        self.processing_time = processing_time
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            "text": self.text,
            "segments": self.segments,
            "language": self.language,
            "confidence": self.confidence,
            "processing_time": self.processing_time
        }


class TranscriptionEngine:
    """Main transcription engine using faster-whisper."""
    
    def __init__(self, config: Optional[TranscriptionConfig] = None):
        """Initialize transcription engine."""
        self.config = config or TranscriptionConfig()
        self.model = None
        self.audio_processor = AudioProcessor(self.config)
        self._setup_logging()
        
        if not FASTER_WHISPER_AVAILABLE:
            raise RuntimeError("faster-whisper is not available. Please install it with: pip install faster-whisper")
    
    def _setup_logging(self):
        """Setup logging configuration."""
        logger.add("logs/transcription.log", rotation="10 MB", level="INFO")
    
    def load_model(self) -> bool:
        """Load the transcription model."""
        try:
            device = self.config.validate_device()
            model_size = self.config.model_size
            logger.info(f"Loading faster-whisper model: {model_size}")
            logger.info(f"Using device: {device}, compute type: {self.config.compute_type}")
            
            # Configure model parameters for Apple Silicon
            model_kwargs = {
                "model_size_or_path": model_size,
                "device": device,
                "compute_type": self.config.compute_type,
                "cpu_threads": 0 if device != "cpu" else 4,  # Optimize CPU threads
            }
            
            # Apple Silicon optimizations
            if device == "mps" and self.config.optimize_for_apple_silicon:
                logger.info("Applying Apple Silicon optimizations")
                model_kwargs["cpu_threads"] = 8  # Use more threads on Apple Silicon
                if self.config.compute_type == "int8":
                    model_kwargs["compute_type"] = "float16"  # Use float16 on MPS
            
            self.model = WhisperModel(**model_kwargs)
            logger.success("Model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            return False
    
    def transcribe_audio(self, audio_path: Union[str, Path]) -> Optional[TranscriptionResult]:
        """
        Transcribe audio file to text.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            TranscriptionResult object or None if failed
        """
        if not self.model and not self.load_model():
            logger.error("Model not loaded and failed to load")
            return None
        
        audio_path = Path(audio_path)
        if not audio_path.exists():
            logger.error(f"Audio file not found: {audio_path}")
            return None
        
        try:
            logger.info(f"Starting transcription of: {audio_path.name}")
            
            # Process audio file
            processed_audio = self.audio_processor.process_audio(audio_path)
            if not processed_audio:
                logger.error("Audio processing failed")
                return None
            
            # Prepare transcription parameters
            language_code = None if self.config.language == TranscriptionLanguage.AUTO else self.config.language.value
            
            # Configure transcription options
            transcription_options = {
                "language": language_code,
                "task": "transcribe",
                "beam_size": self.config.beam_size,
                "best_of": self.config.best_of,
                "temperature": self.config.temperature,
                "condition_on_previous_text": self.config.condition_on_previous_text,
                "word_timestamps": self.config.include_word_timestamps,
            }
            
            # Perform transcription
            import time
            start_time = time.time()
            
            segments, info = self.model.transcribe(
                processed_audio,
                **transcription_options
            )
            
            # Collect segments
            segment_list = []
            full_text = ""
            
            for segment in segments:
                segment_dict = {
                    "start": segment.start,
                    "end": segment.end,
                    "text": segment.text.strip(),
                    "confidence": getattr(segment, "confidence", 1.0)
                }
                
                if hasattr(segment, "words") and segment.words:
                    segment_dict["words"] = [
                        {
                            "start": word.start,
                            "end": word.end,
                            "word": word.word,
                            "confidence": getattr(word, "probability", 1.0)
                        }
                        for word in segment.words
                    ]
                
                segment_list.append(segment_dict)
                full_text += segment.text.strip() + " "
            
            processing_time = time.time() - start_time
            
            # Calculate average confidence
            avg_confidence = sum(s.get("confidence", 1.0) for s in segment_list) / len(segment_list) if segment_list else 1.0
            
            result = TranscriptionResult(
                text=full_text.strip(),
                segments=segment_list,
                language=info.language,
                confidence=avg_confidence,
                processing_time=processing_time
            )
            
            logger.success(f"Transcription completed in {processing_time:.2f}s")
            logger.info(f"Detected language: {info.language}, Confidence: {avg_confidence:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return None
    
    def transcribe_with_fallback(self, audio_path: Union[str, Path]) -> Optional[TranscriptionResult]:
        """
        Transcribe with fallback model if primary fails.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            TranscriptionResult object or None if all attempts failed
        """
        # Try primary model
        result = self.transcribe_audio(audio_path)
        if result:
            return result
        
        # Try fallback model if configured
        if self.config.fallback_model and self.config.fallback_model != self.config.model_size:
            logger.warning(f"Primary model failed, trying fallback: {self.config.fallback_model}")
            
            # Save current config
            original_model = self.config.model_size
            
            try:
                # Switch to fallback model
                self.config.model_size = self.config.fallback_model
                self.model = None  # Force model reload
                
                result = self.transcribe_audio(audio_path)
                if result:
                    logger.success("Fallback model succeeded")
                    return result
                
            finally:
                # Restore original config
                self.config.model_size = original_model
                self.model = None
        
        logger.error("All transcription attempts failed")
        return None
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return [lang.value for lang in TranscriptionLanguage]
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        if not self.model:
            return {"loaded": False}
        
        return {
            "loaded": True,
            "model_size": self.config.model_size,
            "device": self.config.validate_device(),
            "compute_type": self.config.compute_type,
            "language": self.config.language.value,
            "apple_silicon_optimized": self.config.optimize_for_apple_silicon and self.config.validate_device() == "mps"
        }
    
    def cleanup(self):
        """Cleanup resources."""
        if self.model:
            del self.model
            self.model = None
        
        # Clear any CUDA/MPS cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        elif hasattr(torch.backends.mps, 'empty_cache'):
            torch.backends.mps.empty_cache()
        
        logger.info("Transcription engine cleaned up")