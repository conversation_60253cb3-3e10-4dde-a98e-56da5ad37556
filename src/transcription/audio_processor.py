"""
Audio processing utilities for transcription.
Handles audio extraction, format conversion, and preprocessing.
"""

import logging
import tempfile
from pathlib import Path
from typing import Optional, Union, Dict, Any
import subprocess
import shutil
from loguru import logger

import numpy as np
try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    logger.warning("librosa not available, some audio processing features will be limited")

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    logger.warning("pydub not available, some audio formats may not be supported")

from .config import TranscriptionConfig


class AudioProcessor:
    """Handles audio processing for transcription."""
    
    def __init__(self, config: TranscriptionConfig):
        """Initialize audio processor."""
        self.config = config
        self.supported_formats = {".wav", ".mp3", ".m4a", ".flac", ".ogg", ".aac"}
        self.librosa_available = LIBROSA_AVAILABLE
        self.pydub_available = PYDUB_AVAILABLE
        
        if not LIBROSA_AVAILABLE and not PYDUB_AVAILABLE:
            logger.warning("No audio processing libraries available. Install librosa or pydub for full functionality.")
    
    def process_audio(self, audio_path: Union[str, Path]) -> Optional[Path]:
        """
        Process audio file for transcription.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Path to processed audio file or None if failed
        """
        audio_path = Path(audio_path)
        
        if not audio_path.exists():
            logger.error(f"Audio file not found: {audio_path}")
            return None
        
        try:
            logger.info(f"Processing audio file: {audio_path.name}")
            
            # Check if audio format is supported
            if audio_path.suffix.lower() not in self.supported_formats:
                logger.warning(f"Unsupported format: {audio_path.suffix}. Attempting conversion.")
                audio_path = self._convert_audio_format(audio_path)
                if not audio_path:
                    return None
            
            # Get audio info first to check for edge cases
            audio_info = self.get_audio_info(audio_path)
            if not audio_info:
                logger.error("Could not get audio information")
                return None
            
            # Check for edge cases
            if audio_info["duration"] < 0.1:  # Very short audio (< 100ms)
                logger.warning(f"Audio is very short ({audio_info['duration']:.3f}s), may produce hallucinations")
                # For very short audio, we'll process but add a flag
                return self._process_edge_case_audio(audio_path, "very_short")
            
            # Check for silence
            if self._is_mostly_silence(audio_path):
                logger.warning("Audio appears to be mostly silence")
                return self._process_edge_case_audio(audio_path, "silence")
            
            # Load and preprocess audio
            if LIBROSA_AVAILABLE:
                return self._process_with_librosa(audio_path)
            elif PYDUB_AVAILABLE:
                return self._process_with_pydub(audio_path)
            else:
                logger.error("No audio processing library available")
                return None
                
        except Exception as e:
            logger.error(f"Audio processing failed: {e}")
            return None
    
    def _process_with_librosa(self, audio_path: Path) -> Optional[Path]:
        """Process audio using librosa."""
        try:
            # Load audio with target sample rate
            logger.info(f"Loading audio with librosa: {audio_path.name}")
            
            audio, sr = librosa.load(
                audio_path,
                sr=self.config.sample_rate,
                mono=True  # Convert to mono for better transcription
            )
            
            # Check if audio is valid (not empty or too short)
            if len(audio) == 0:
                logger.warning("Audio file is empty, skipping processing")
                return None
            
            if len(audio) < 1024:  # Less than ~0.02 seconds at 44.1kHz
                logger.warning("Audio file is too short for processing")
                # For very short audio, just normalize and save
                audio = librosa.util.normalize(audio)
            else:
                # Normalize audio
                audio = librosa.util.normalize(audio)
                
                # Apply noise reduction if needed (simple high-pass filter)
                try:
                    audio = librosa.effects.preemphasis(audio)
                except Exception as e:
                    logger.warning(f"Preemphasis failed, skipping: {e}")
                
                # Trim silence from beginning and end
                try:
                    audio, _ = librosa.effects.trim(audio, top_db=20)
                except Exception as e:
                    logger.warning(f"Trimming failed, skipping: {e}")
            
            # Save processed audio to temporary file
            temp_file = tempfile.NamedTemporaryFile(
                suffix=".wav", delete=False, dir=tempfile.gettempdir()
            )
            temp_path = Path(temp_file.name)
            temp_file.close()
            
            # Save as WAV file
            import soundfile as sf
            sf.write(temp_path, audio, self.config.sample_rate)
            
            logger.success(f"Audio processed successfully: {temp_path.name}")
            return temp_path
            
        except Exception as e:
            logger.error(f"Librosa processing failed: {e}")
            return None
    
    def _process_with_pydub(self, audio_path: Path) -> Optional[Path]:
        """Process audio using pydub."""
        try:
            logger.info(f"Loading audio with pydub: {audio_path.name}")
            
            # Load audio
            audio = AudioSegment.from_file(audio_path)
            
            # Convert to mono
            audio = audio.set_channels(1)
            
            # Set sample rate
            audio = audio.set_frame_rate(self.config.sample_rate)
            
            # Normalize audio
            audio = audio.normalize()
            
            # Remove silence from beginning and end (simple version)
            # Find first and last samples above threshold
            samples = np.array(audio.get_array_of_samples())
            threshold = np.max(np.abs(samples)) * 0.01  # 1% of max amplitude
            
            # Find start and end of significant audio
            above_threshold = np.abs(samples) > threshold
            if np.any(above_threshold):
                start_idx = np.where(above_threshold)[0][0]
                end_idx = np.where(above_threshold)[0][-1]
                
                # Convert back to milliseconds
                start_ms = (start_idx / len(samples)) * len(audio)
                end_ms = (end_idx / len(samples)) * len(audio)
                
                # Trim audio
                audio = audio[start_ms:end_ms]
            
            # Save processed audio to temporary file
            temp_file = tempfile.NamedTemporaryFile(
                suffix=".wav", delete=False, dir=tempfile.gettempdir()
            )
            temp_path = Path(temp_file.name)
            temp_file.close()
            
            # Export as WAV
            audio.export(temp_path, format="wav")
            
            logger.success(f"Audio processed successfully: {temp_path.name}")
            return temp_path
            
        except Exception as e:
            logger.error(f"Pydub processing failed: {e}")
            return None
    
    def _convert_audio_format(self, audio_path: Path) -> Optional[Path]:
        """Convert audio to supported format using ffmpeg."""
        try:
            logger.info(f"Converting audio format: {audio_path.suffix}")
            
            # Check if ffmpeg is available
            if not shutil.which("ffmpeg"):
                logger.error("ffmpeg not found. Cannot convert audio format.")
                return None
            
            # Create temporary WAV file
            temp_file = tempfile.NamedTemporaryFile(
                suffix=".wav", delete=False, dir=tempfile.gettempdir()
            )
            temp_path = Path(temp_file.name)
            temp_file.close()
            
            # Convert to WAV using ffmpeg
            cmd = [
                "ffmpeg", "-i", str(audio_path),
                "-ar", str(self.config.sample_rate),  # Sample rate
                "-ac", "1",  # Mono
                "-y",  # Overwrite output
                str(temp_path)
            ]
            
            # Run conversion
            result = subprocess.run(
                cmd, capture_output=True, text=True, check=True
            )
            
            if result.returncode == 0:
                logger.success(f"Audio format converted: {temp_path.name}")
                return temp_path
            else:
                logger.error(f"FFmpeg conversion failed: {result.stderr}")
                return None
                
        except subprocess.CalledProcessError as e:
            logger.error(f"FFmpeg conversion error: {e.stderr}")
            return None
        except Exception as e:
            logger.error(f"Format conversion failed: {e}")
            return None
    
    def extract_audio_from_video(self, video_path: Union[str, Path], 
                                output_path: Optional[Union[str, Path]] = None) -> Optional[Path]:
        """
        Extract audio from video file.
        
        Args:
            video_path: Path to video file
            output_path: Optional output path for audio file
            
        Returns:
            Path to extracted audio file or None if failed
        """
        video_path = Path(video_path)
        
        if not video_path.exists():
            logger.error(f"Video file not found: {video_path}")
            return None
        
        if not shutil.which("ffmpeg"):
            logger.error("ffmpeg not found. Cannot extract audio from video.")
            return None
        
        try:
            if output_path is None:
                # Create output path with same name but .wav extension
                output_path = video_path.with_name(video_path.stem + "_extracted.wav")
            else:
                output_path = Path(output_path)
            
            logger.info(f"Extracting audio from: {video_path.name}")
            
            # Extract audio using ffmpeg
            cmd = [
                "ffmpeg", "-i", str(video_path),
                "-vn",  # No video
                "-acodec", "pcm_s16le",  # PCM 16-bit little-endian
                "-ar", str(self.config.sample_rate),  # Sample rate
                "-ac", "1",  # Mono
                "-y",  # Overwrite output
                str(output_path)
            ]
            
            result = subprocess.run(
                cmd, capture_output=True, text=True, check=True
            )
            
            if result.returncode == 0:
                logger.success(f"Audio extracted successfully: {output_path.name}")
                return output_path
            else:
                logger.error(f"FFmpeg extraction failed: {result.stderr}")
                return None
                
        except subprocess.CalledProcessError as e:
            logger.error(f"FFmpeg extraction error: {e.stderr}")
            return None
        except Exception as e:
            logger.error(f"Audio extraction failed: {e}")
            return None
    
    def get_audio_info(self, audio_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        Get information about audio file.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Dictionary with audio information or None if failed
        """
        audio_path = Path(audio_path)
        
        if not audio_path.exists():
            logger.error(f"Audio file not found: {audio_path}")
            return None
        
        try:
            if LIBROSA_AVAILABLE:
                audio, sr = librosa.load(audio_path, sr=None)
                duration = len(audio) / sr
                
                return {
                    "duration": duration,
                    "sample_rate": sr,
                    "channels": 1 if len(audio.shape) == 1 else audio.shape[0],
                    "format": audio_path.suffix.lower(),
                    "file_size": audio_path.stat().st_size
                }
            
            elif PYDUB_AVAILABLE:
                audio = AudioSegment.from_file(audio_path)
                
                return {
                    "duration": len(audio) / 1000.0,  # Convert to seconds
                    "sample_rate": audio.frame_rate,
                    "channels": audio.channels,
                    "format": audio_path.suffix.lower(),
                    "file_size": audio_path.stat().st_size
                }
            
            else:
                logger.error("No audio processing library available for info extraction")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get audio info: {e}")
            return None