"""
Transcription configuration management for <PERSON><PERSON><PERSON>ci Resolve AI Assistant.
"""

from dataclasses import dataclass
from enum import Enum
from typing import Optional, Dict, Any
from pathlib import Path


class TranscriptionModel(str, Enum):
    """Available transcription models."""
    TINY = "tiny"
    BASE = "base" 
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"
    LARGE_V2 = "large-v2"
    LARGE_V3 = "large-v3"


class TranscriptionLanguage(str, Enum):
    """Supported transcription languages."""
    AUTO = "auto"
    ENGLISH = "en"
    SPANISH = "es"
    FRENCH = "fr"
    GERMAN = "de"
    ITALIAN = "it"
    PORTUGUESE = "pt"
    RUSSIAN = "ru"
    CHINESE = "zh"
    JAPANESE = "ja"


@dataclass
class TranscriptionConfig:
    """Configuration for transcription engine."""
    
    # Model settings
    model_size: TranscriptionModel = TranscriptionModel.BASE
    language: TranscriptionLanguage = TranscriptionLanguage.AUTO
    device: str = "auto"  # auto, cpu, cuda, mps
    compute_type: str = "int8"  # int8, int16, float16, float32
    
    # Performance settings - optimized to reduce hallucinations
    beam_size: int = 1      # Lower beam size reduces hallucinations on silence
    best_of: int = 1         # Single best result to avoid over-generation
    temperature: float = 0.0  # Keep deterministic for consistency
    condition_on_previous_text: bool = False  # Disable to prevent repetitive hallucinations
    
    # Audio processing
    sample_rate: int = 16000
    chunk_length: int = 30  # seconds
    overlap: float = 0.1  # 10% overlap between chunks
    
    # Output settings
    output_format: str = "txt"  # txt, srt, vtt, json
    include_word_timestamps: bool = False
    
    # Apple Silicon optimizations
    use_metal: bool = True  # Use Metal Performance Shaders on Apple Silicon
    optimize_for_apple_silicon: bool = True
    
    # Error handling
    max_retries: int = 3
    fallback_model: Optional[TranscriptionModel] = None
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "TranscriptionConfig":
        """Create config from dictionary."""
        return cls(**config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            "model_size": self.model_size.value,
            "language": self.language.value,
            "device": self.device,
            "compute_type": self.compute_type,
            "beam_size": self.beam_size,
            "best_of": self.best_of,
            "temperature": self.temperature,
            "condition_on_previous_text": self.condition_on_previous_text,
            "sample_rate": self.sample_rate,
            "chunk_length": self.chunk_length,
            "overlap": self.overlap,
            "output_format": self.output_format,
            "include_word_timestamps": self.include_word_timestamps,
            "use_metal": self.use_metal,
            "optimize_for_apple_silicon": self.optimize_for_apple_silicon,
            "max_retries": self.max_retries,
            "fallback_model": self.fallback_model.value if self.fallback_model else None,
        }
    
    def validate_device(self) -> str:
        """Validate and return the appropriate device for the current system."""
        import torch
        
        if self.device == "auto":
            # faster-whisper doesn't support MPS, so use CUDA or CPU
            if torch.cuda.is_available():
                return "cuda"
            else:
                # Use CPU for Apple Silicon and other systems
                return "cpu"
        
        # If device is explicitly set to "mps", fall back to "cpu" for faster-whisper
        if self.device == "mps":
            return "cpu"
            
        return self.device