"""
DaVinci Resolve Bridge Module

This module provides the bridge between the AI Assistant and DaVinci Resolve,
allowing for timeline access, audio extraction, and project management.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import subprocess
import tempfile

logger = logging.getLogger(__name__)

@dataclass
class TimelineInfo:
    """Information about a DaVinci Resolve timeline."""
    name: str
    duration: float
    frame_rate: float
    resolution: str
    audio_tracks: int
    video_tracks: int
    media_pool_items: List[str]

@dataclass
class ProjectInfo:
    """Information about a DaVinci Resolve project."""
    name: str
    timeline_count: int
    current_timeline: Optional[str]
    media_pool_items: List[str]

class DaVinciResolveBridge:
    """
    Bridge class for DaVinci Resolve integration.
    """
    
    def __init__(self):
        """Initialize the DaVinci Resolve bridge."""
        self.connected = False
        self.resolve_app = None
        self.project_manager = None
        self.current_project = None
        self.current_timeline = None
        self.fusion_script_path = None
        
    async def connect(self) -> bool:
        """
        Connect to DaVinci Resolve.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            logger.info("Attempting to connect to DaVinci Resolve...")
            
            # Try to import DaVinci Resolve API
            try:
                import DaVinciResolveScript as dvr
                self.resolve_app = dvr.scriptapp("Resolve")
                
                if self.resolve_app:
                    self.project_manager = self.resolve_app.GetProjectManager()
                    self.connected = True
                    logger.info("Successfully connected to DaVinci Resolve")
                    return True
                else:
                    logger.error("Failed to get DaVinci Resolve application instance")
                    return False
                    
            except ImportError:
                logger.warning("DaVinci Resolve API not available, using mock mode")
                return await self._connect_mock()
                
        except Exception as e:
            logger.error(f"Failed to connect to DaVinci Resolve: {e}")
            return False
            
    async def _connect_mock(self) -> bool:
        """
        Mock connection for development/testing when DaVinci Resolve is not available.
        
        Returns:
            True if mock connection successful
        """
        try:
            logger.info("Setting up mock DaVinci Resolve connection")
            self.connected = True
            self.resolve_app = MockResolveApp()
            self.project_manager = MockProjectManager()
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup mock connection: {e}")
            return False
            
    async def disconnect(self):
        """Disconnect from DaVinci Resolve."""
        self.connected = False
        self.resolve_app = None
        self.project_manager = None
        self.current_project = None
        self.current_timeline = None
        logger.info("Disconnected from DaVinci Resolve")
        
    async def get_current_project(self) -> Optional[ProjectInfo]:
        """
        Get information about the current project.
        
        Returns:
            Project information or None if not available
        """
        if not self.connected:
            logger.error("Not connected to DaVinci Resolve")
            return None
            
        try:
            if hasattr(self.resolve_app, 'GetCurrentProject'):
                project = self.resolve_app.GetCurrentProject()
                if project:
                    self.current_project = project
                    
                    # Get project information
                    timeline_count = len(project.GetTimelineList())
                    current_timeline = project.GetCurrentTimeline()
                    current_timeline_name = current_timeline.GetName() if current_timeline else None
                    
                    # Get media pool items
                    media_pool = project.GetMediaPool()
                    media_pool_items = []
                    if media_pool:
                        root_folder = media_pool.GetRootFolder()
                        if root_folder:
                            media_pool_items = [clip.GetName() for clip in root_folder.GetClipList()]
                    
                    return ProjectInfo(
                        name=project.GetName(),
                        timeline_count=timeline_count,
                        current_timeline=current_timeline_name,
                        media_pool_items=media_pool_items
                    )
                    
            logger.error("Failed to get current project")
            return None
            
        except Exception as e:
            logger.error(f"Error getting current project: {e}")
            return None
            
    async def get_current_timeline(self) -> Optional[TimelineInfo]:
        """
        Get information about the current timeline.
        
        Returns:
            Timeline information or None if not available
        """
        if not self.connected:
            logger.error("Not connected to DaVinci Resolve")
            return None
            
        try:
            if self.current_project:
                timeline = self.current_project.GetCurrentTimeline()
                if timeline:
                    self.current_timeline = timeline
                    
                    # Get timeline information
                    return TimelineInfo(
                        name=timeline.GetName(),
                        duration=timeline.GetDuration(),
                        frame_rate=timeline.GetSetting('timelineFrameRate'),
                        resolution=f"{timeline.GetSetting('timelineResolutionWidth')}x{timeline.GetSetting('timelineResolutionHeight')}",
                        audio_tracks=len(timeline.GetItemListInTrack('audio', 1)),
                        video_tracks=len(timeline.GetItemListInTrack('video', 1)),
                        media_pool_items=[]
                    )
                    
            logger.error("Failed to get current timeline")
            return None
            
        except Exception as e:
            logger.error(f"Error getting current timeline: {e}")
            return None
            
    async def extract_audio(self, timeline_path: Optional[str] = None) -> Optional[str]:
        """
        Extract audio from the current timeline or specified timeline.
        
        Args:
            timeline_path: Optional path to specific timeline
            
        Returns:
            Path to extracted audio file or None if failed
        """
        if not self.connected:
            logger.error("Not connected to DaVinci Resolve")
            return None
            
        try:
            # Create temporary file for audio extraction
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                audio_output_path = temp_file.name
                
            if hasattr(self.resolve_app, 'ExtractAudio'):
                # Use DaVinci Resolve's built-in audio extraction
                success = self.resolve_app.ExtractAudio(
                    timeline_path or self.current_timeline,
                    audio_output_path
                )
                
                if success:
                    logger.info(f"Audio extracted to: {audio_output_path}")
                    return audio_output_path
                else:
                    logger.warning("DaVinci Resolve audio extraction failed, trying FFmpeg fallback")
                    # Fallback: Use FFmpeg for audio extraction
                    return await self._extract_audio_ffmpeg(timeline_path, audio_output_path)
            else:
                # Fallback: Use FFmpeg for audio extraction
                return await self._extract_audio_ffmpeg(timeline_path, audio_output_path)
                
        except Exception as e:
            logger.error(f"Error extracting audio: {e}")
            return None
            
    async def _extract_audio_ffmpeg(self, timeline_path: Optional[str], output_path: str) -> Optional[str]:
        """
        Extract audio using FFmpeg as fallback.
        
        Args:
            timeline_path: Path to timeline
            output_path: Output audio path
            
        Returns:
            Path to extracted audio or None if failed
        """
        try:
            # Get timeline media files
            media_files = await self.get_timeline_media(timeline_path)
            
            if not media_files:
                logger.error("No media files found in timeline for audio extraction")
                return None
            
            # For simplicity, extract audio from the first video file
            # In a real implementation, you'd want to handle multiple files and timeline structure
            video_file = media_files[0]
            
            # Check if FFmpeg is available
            ffmpeg_cmd = ['ffmpeg', '-version']
            try:
                subprocess.run(ffmpeg_cmd, check=True, capture_output=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.error("FFmpeg not found. Please install FFmpeg.")
                return None
            
            # Extract audio using FFmpeg
            # Use 16kHz sample rate for better speech recognition
            extract_cmd = [
                'ffmpeg',
                '-i', video_file,
                '-vn',  # No video
                '-acodec', 'pcm_s16le',  # 16-bit PCM
                '-ar', '16000',  # 16kHz sample rate
                '-ac', '1',  # Mono audio
                '-y',  # Overwrite output file
                output_path
            ]
            
            logger.info(f"Extracting audio from {video_file} to {output_path}")
            
            # Run FFmpeg command
            result = subprocess.run(extract_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"Audio extracted successfully to: {output_path}")
                return output_path
            else:
                logger.error(f"FFmpeg audio extraction failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"FFmpeg audio extraction failed: {e}")
            return None
            
    async def get_timeline_media(self, timeline_path: Optional[str] = None) -> List[str]:
        """
        Get media files used in the timeline.
        
        Args:
            timeline_path: Optional path to specific timeline
            
        Returns:
            List of media file paths
        """
        if not self.connected:
            logger.error("Not connected to DaVinci Resolve")
            return []
            
        try:
            if self.current_timeline:
                # Get timeline items
                timeline_items = self.current_timeline.GetItemListInTrack('video', 1)
                media_files = []
                
                for item in timeline_items:
                    # Get media reference for each item
                    media_reference = item.GetMediaPoolItem()
                    if media_reference:
                        file_path = media_reference.GetClipProperty('File Path')
                        if file_path:
                            media_files.append(file_path)
                            
                return media_files
                
            logger.error("No current timeline available")
            return []
            
        except Exception as e:
            logger.error(f"Error getting timeline media: {e}")
            return []
            
    async def create_fusion_script(self, script_content: str, script_name: str) -> Optional[str]:
        """
        Create a Fusion script for advanced operations.
        
        Args:
            script_content: Fusion script content
            script_name: Name for the script
            
        Returns:
            Path to created script or None if failed
        """
        try:
            # Create temporary script file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.comp', delete=False) as script_file:
                script_file.write(script_content)
                script_path = script_file.name
                
            self.fusion_script_path = script_path
            logger.info(f"Created Fusion script: {script_path}")
            return script_path
            
        except Exception as e:
            logger.error(f"Failed to create Fusion script: {e}")
            return None
            
    async def execute_fusion_script(self, script_path: str) -> bool:
        """
        Execute a Fusion script.
        
        Args:
            script_path: Path to Fusion script
            
        Returns:
            True if execution successful
        """
        try:
            if self.connected and hasattr(self.resolve_app, 'ExecuteFusionScript'):
                success = self.resolve_app.ExecuteFusionScript(script_path)
                logger.info(f"Fusion script execution: {'successful' if success else 'failed'}")
                return success
            else:
                logger.warning("Fusion script execution not available")
                return False
                
        except Exception as e:
            logger.error(f"Failed to execute Fusion script: {e}")
            return False


class MockResolveApp:
    """Mock DaVinci Resolve application for development/testing."""
    
    def __init__(self):
        self.name = "Mock DaVinci Resolve"
        self.version = "18.0"
        
    def GetProjectManager(self):
        return MockProjectManager()
        
    def GetCurrentProject(self):
        return MockProject()
        
    def ExtractAudio(self, timeline, output_path):
        # Create a dummy audio file for testing
        try:
            with open(output_path, 'wb') as f:
                # Write minimal WAV header for testing
                f.write(b'RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x44\xac\x00\x00\x88X\x01\x00\x02\x00\x10\x00data\x00\x08\x00\x00')
            return True
        except Exception:
            return False

class MockProjectManager:
    """Mock project manager."""
    
    def __init__(self):
        self.projects = ["Project 1", "Project 2", "Test Project"]
        
    def GetCurrentProject(self):
        return MockProject()

class MockProject:
    """Mock project."""
    
    def __init__(self):
        self.name = "Test Project"
        self.timelines = ["Timeline 1", "Timeline 2"]
        
    def GetName(self):
        return self.name
        
    def GetTimelineList(self):
        return [MockTimeline(name) for name in self.timelines]
        
    def GetCurrentTimeline(self):
        return MockTimeline("Current Timeline")
        
    def GetMediaPool(self):
        return MockMediaPool()

class MockTimeline:
    """Mock timeline."""
    
    def __init__(self, name):
        self.name = name
        self.duration = 120.0  # 2 minutes
        self.frame_rate = 24.0
        self.resolution_width = 1920
        self.resolution_height = 1080
        
    def GetName(self):
        return self.name
        
    def GetDuration(self):
        return self.duration
        
    def GetSetting(self, setting_name):
        settings = {
            'timelineFrameRate': self.frame_rate,
            'timelineResolutionWidth': self.resolution_width,
            'timelineResolutionHeight': self.resolution_height
        }
        return settings.get(setting_name, 0)
        
    def GetItemListInTrack(self, track_type, track_index):
        return []  # Empty list for mock

class MockMediaPool:
    """Mock media pool."""
    
    def GetRootFolder(self):
        return MockMediaPoolFolder()

class MockMediaPoolFolder:
    """Mock media pool folder."""
    
    def GetClipList(self):
        return [MockClip(f"Clip {i}") for i in range(3)]

class MockClip:
    """Mock clip."""
    
    def __init__(self, name):
        self.name = name
        
    def GetName(self):
        return self.name