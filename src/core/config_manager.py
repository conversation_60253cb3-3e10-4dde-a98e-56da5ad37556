"""
Da<PERSON><PERSON>ci Resolve AI Assistant - Configuration Manager

This module manages configuration for the entire application,
including transcription settings, AI models, and DaVinci Resolve integration.
"""

import os
import yaml
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class ConfigDefaults:
    """Default configuration values."""
    
    # Transcription defaults
    TRANSCRIPTION_ENGINE = "faster-whisper"
    TRANSCRIPTION_MODEL = "base"
    TRANSCRIPTION_LANGUAGE = "auto"
    TRANSCRIPTION_DEVICE = "auto"
    TRANSCRIPTION_COMPUTE_TYPE = "float16"
    TRANSCRIPTION_OPTIMIZE_APPLE_SILICON = True
    
    # AI defaults
    AI_PRIMARY_MODEL = "anthropic/claude-3.5-sonnet"
    AI_FALLBACK_MODELS = ["meta-llama/llama-3.3-70b-instruct", "anthropic/claude-3.5-haiku"]
    AI_MAX_TOKENS = 4000
    AI_TEMPERATURE = 0.7
    
    # Model provider defaults
    AI_PROVIDER_TYPE = "openrouter"  # openrouter, local_lmstudio, local_ollama, qwen, deepseek, kimi, glm
    
    # Local model defaults
    LOCAL_LMSTUDIO_URL = "http://localhost:1234"
    LOCAL_OLLAMA_URL = "http://localhost:11434"
    
    # Open-source API defaults
    QWEN_BASE_URL = "https://dashscope.aliyuncs.com/api/v1"
    DEEPSEEK_BASE_URL = "https://api.deepseek.com"
    KIMI_BASE_URL = "https://api.moonshot.cn"
    GLM_BASE_URL = "https://open.bigmodel.cn/api/paas"
    
    # DaVinci Resolve defaults
    RESOLVE_AUTO_CONNECT = True
    RESOLVE_TIMEOUT = 30
    RESOLVE_SCRIPT_PATH = "/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Libraries/Fusion/"
    
    # Logging defaults
    LOG_LEVEL = "INFO"
    LOG_FILE = "logs/app.log"
    LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5

class ConfigManager:
    """
    Configuration manager for DaVinci Resolve AI Assistant.
    Handles loading, validation, and management of application settings.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path or self._find_config_file()
        self.config_data = {}
        self._load_config()
        self._load_environment_variables()
        
    def _find_config_file(self) -> str:
        """Find the configuration file in standard locations."""
        # Search paths in order of preference
        search_paths = [
            "config/config.yaml",
            "config/config.yml", 
            "config.json",
            "~/.davinci-ai-assistant/config.yaml",
            "~/.davinci-ai-assistant/config.json"
        ]
        
        for path in search_paths:
            expanded_path = Path(path).expanduser()
            if expanded_path.exists():
                return str(expanded_path)
                
        # Default to config/config.yaml
        return "config/config.yaml"
        
    def _load_config(self):
        """Load configuration from file."""
        try:
            config_file = Path(self.config_path)
            
            if not config_file.exists():
                logger.warning(f"Config file not found: {self.config_path}")
                self.config_data = self._get_default_config()
                self._save_config()
                return
                
            with open(config_file, 'r', encoding='utf-8') as f:
                if config_file.suffix.lower() in ['.yaml', '.yml']:
                    self.config_data = yaml.safe_load(f) or {}
                else:
                    self.config_data = json.load(f)
                    
            # Merge with defaults
            self.config_data = self._merge_with_defaults(self.config_data)
            
            logger.info(f"Configuration loaded from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self.config_data = self._get_default_config()
            
    def load_config(self):
        """Public method to load configuration from file."""
        self._load_config()
            
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration values."""
        return {
            'ai': {
                'primary_model': ConfigDefaults.AI_PRIMARY_MODEL,
                'fallback_models': ConfigDefaults.AI_FALLBACK_MODELS,
                'max_tokens': ConfigDefaults.AI_MAX_TOKENS,
                'temperature': ConfigDefaults.AI_TEMPERATURE,
                'provider_type': ConfigDefaults.AI_PROVIDER_TYPE,
                'providers': {
                    'openrouter': {
                        'api_key': '',
                        'base_url': 'https://openrouter.ai/api/v1',
                        'models': [
                            'anthropic/claude-3.5-sonnet',
                            'meta-llama/llama-3.3-70b-instruct',
                            'anthropic/claude-3.5-haiku'
                        ]
                    },
                    'local_lmstudio': {
                        'base_url': ConfigDefaults.LOCAL_LMSTUDIO_URL,
                        'timeout': 60,
                        'models': []  # Will be populated dynamically
                    },
                    'local_ollama': {
                        'base_url': ConfigDefaults.LOCAL_OLLAMA_URL,
                        'timeout': 60,
                        'models': []  # Will be populated dynamically
                    },
                    'qwen': {
                        'api_key': '',
                        'base_url': ConfigDefaults.QWEN_BASE_URL,
                        'models': [
                            'qwen-turbo',
                            'qwen-plus',
                            'qwen-max',
                            'qwen2.5-72b-instruct'
                        ]
                    },
                    'deepseek': {
                        'api_key': '',
                        'base_url': ConfigDefaults.DEEPSEEK_BASE_URL,
                        'models': [
                            'deepseek-chat',
                            'deepseek-coder',
                            'deepseek-reasoner'
                        ]
                    },
                    'kimi': {
                        'api_key': '',
                        'base_url': ConfigDefaults.KIMI_BASE_URL,
                        'models': [
                            'moonshot-v1-8k',
                            'moonshot-v1-32k',
                            'moonshot-v1-128k'
                        ]
                    },
                    'glm': {
                        'api_key': '',
                        'base_url': ConfigDefaults.GLM_BASE_URL,
                        'models': [
                            'glm-4',
                            'glm-4-plus',
                            'glm-4-air',
                            'glm-4-flash'
                        ]
                    }
                }
            },
            'transcription': {
                'engine': ConfigDefaults.TRANSCRIPTION_ENGINE,
                'model_size': ConfigDefaults.TRANSCRIPTION_MODEL,
                'language': ConfigDefaults.TRANSCRIPTION_LANGUAGE,
                'device': ConfigDefaults.TRANSCRIPTION_DEVICE,
                'compute_type': ConfigDefaults.TRANSCRIPTION_COMPUTE_TYPE,
                'optimize_for_apple_silicon': ConfigDefaults.TRANSCRIPTION_OPTIMIZE_APPLE_SILICON,
                'fallback_model': 'tiny',
                'beam_size': 5,
                'best_of': 5,
                'temperature': 0.0,
                'condition_on_previous_text': True,
                'initial_prompt': None,
                'suppress_tokens': [-1],
                'suppress_blank': True,
                'word_timestamps': False,
                'prepend_punctuations': '"'"([",
                'append_punctuations': '"'"].!",
                'max_initial_timestamp': 1.0,
                'vad_filter': True,
                'vad_parameters': {
                    'threshold': 0.5,
                    'min_speech_duration_ms': 250,
                    'max_speech_duration_s': 3600,  # 1 hour max duration instead of infinity
                    'min_silence_duration_ms': 2000,
                    'window_size_samples': 1024,
                    'speech_pad_ms': 400
                }
            },
            'resolve': {
                'auto_connect': ConfigDefaults.RESOLVE_AUTO_CONNECT,
                'timeout': ConfigDefaults.RESOLVE_TIMEOUT,
                'script_path': ConfigDefaults.RESOLVE_SCRIPT_PATH,
                'timeline_cache_duration': 300,
                'auto_save_interval': 60
            },
            'logging': {
                'level': ConfigDefaults.LOG_LEVEL,
                'file': ConfigDefaults.LOG_FILE,
                'max_size': ConfigDefaults.LOG_MAX_SIZE,
                'backup_count': ConfigDefaults.LOG_BACKUP_COUNT,
                'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
            },
            'ui': {
                'theme': 'dark',
                'font_size': 12,
                'window_size': '1024x768',
                'auto_refresh_interval': 5000,
                'show_advanced_options': False
            },
            'performance': {
                'max_concurrent_transcriptions': 2,
                'cache_size': 100,
                'cache_ttl': 3600,
                'memory_limit_mb': 2048,
                'cpu_limit_percent': 80
            },
            'security': {
                'api_key_storage': 'env',
                'allow_insecure_connections': False,
                'validate_certificates': True
            }
        }
        
    def _merge_with_defaults(self, user_config: Dict[str, Any]) -> Dict[str, Any]:
        """Merge user configuration with defaults."""
        defaults = self._get_default_config()
        return self._deep_merge(defaults, user_config)
        
    def _deep_merge(self, base: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries."""
        result = base.copy()
        
        for key, value in update.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
                
        return result
        
    def _load_environment_variables(self):
        """Load configuration from environment variables."""
        env_mappings = {
            'DAVINCI_AI_LOG_LEVEL': 'logging.level',
            'DAVINCI_AI_LOG_FILE': 'logging.file',
            'DAVINCI_AI_TRANSCRIPTION_MODEL': 'transcription.model_size',
            'DAVINCI_AI_TRANSCRIPTION_LANGUAGE': 'transcription.language',
            'DAVINCI_AI_AI_MODEL': 'ai.primary_model',
            'DAVINCI_AI_MAX_TOKENS': 'ai.max_tokens',
            'DAVINCI_AI_TEMPERATURE': 'ai.temperature',
            'DAVINCI_AI_RESOLVE_TIMEOUT': 'resolve.timeout',
            'DAVINCI_AI_RESOLVE_SCRIPT_PATH': 'resolve.script_path',
            'DAVINCI_AI_OPTIMIZE_APPLE_SILICON': 'transcription.optimize_for_apple_silicon',
            'DAVINCI_AI_MAX_CONCURRENT_TRANSCRIPTIONS': 'performance.max_concurrent_transcriptions',
            'DAVINCI_AI_CACHE_SIZE': 'performance.cache_size',
            'DAVINCI_AI_MEMORY_LIMIT_MB': 'performance.memory_limit_mb',
            'DAVINCI_AI_CPU_LIMIT_PERCENT': 'performance.cpu_limit_percent'
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Convert string values to appropriate types
                if config_path.endswith(('max_tokens', 'timeout', 'cache_size', 'memory_limit_mb')):
                    try:
                        env_value = int(env_value)
                    except ValueError:
                        logger.warning(f"Invalid integer value for {env_var}: {env_value}")
                        continue
                elif config_path.endswith(('temperature', 'cpu_limit_percent')):
                    try:
                        env_value = float(env_value)
                    except ValueError:
                        logger.warning(f"Invalid float value for {env_var}: {env_value}")
                        continue
                elif config_path.endswith(('optimize_for_apple_silicon',)):
                    # Only convert if it's a valid boolean string, otherwise skip
                    if env_value.lower() in ('true', '1', 'yes', 'on'):
                        env_value = True
                    elif env_value.lower() in ('false', '0', 'no', 'off'):
                        env_value = False
                    else:
                        # Invalid boolean value, skip this environment variable
                        logger.warning(f"Invalid boolean value for {env_var}: {env_value}")
                        continue
                # Handle boolean values for other settings
                elif isinstance(self.get(config_path, None), bool):
                    # Only convert if it's a valid boolean string, otherwise skip
                    if env_value.lower() in ('true', '1', 'yes', 'on'):
                        env_value = True
                    elif env_value.lower() in ('false', '0', 'no', 'off'):
                        env_value = False
                    else:
                        # Invalid boolean value, skip this environment variable
                        logger.warning(f"Invalid boolean value for {env_var}: {env_value}")
                        continue
                
                # Set the value in config
                self._set_nested_value(self.config_data, config_path, env_value)
                logger.info(f"Loaded config from environment: {env_var} -> {config_path}")
                
    def _set_nested_value(self, config: Dict[str, Any], path: str, value: Any):
        """Set a nested configuration value using dot notation."""
        keys = path.split('.')
        current = config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
            
        current[keys[-1]] = value
        
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key_path: Dot-separated key path (e.g., 'transcription.model_size')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key_path.split('.')
        value = self.config_data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
                
        return value
        
    def set(self, key_path: str, value: Any):
        """
        Set configuration value using dot notation.
        
        Args:
            key_path: Dot-separated key path
            value: Value to set
        """
        keys = key_path.split('.')
        config = self.config_data
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
            
        config[keys[-1]] = value
        
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get entire configuration section.
        
        Args:
            section: Section name
            
        Returns:
            Section configuration dictionary
        """
        return self.config_data.get(section, {})
        
    def update_section(self, section: str, data: Dict[str, Any]):
        """
        Update entire configuration section.
        
        Args:
            section: Section name
            data: Section configuration data
        """
        if section not in self.config_data:
            self.config_data[section] = {}
            
        self.config_data[section].update(data)
        
    def _save_config(self):
        """Save configuration to file."""
        try:
            config_file = Path(self.config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                if config_file.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(self.config_data, f, default_flow_style=False, indent=2)
                else:
                    json.dump(self.config_data, f, indent=2)
                    
            logger.info(f"Configuration saved to {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            
    def validate_config(self) -> List[str]:
        """
        Validate configuration and return list of errors.
        
        Returns:
            List of validation error messages
        """
        errors = []
        
        # Validate transcription settings
        transcription_engine = self.get('transcription.engine')
        if transcription_engine not in ['faster-whisper', 'whisper', 'whisperx']:
            errors.append(f"Invalid transcription engine: {transcription_engine}")
            
        model_size = self.get('transcription.model_size')
        valid_models = ['tiny', 'base', 'small', 'medium', 'large', 'large-v1', 'large-v2', 'large-v3']
        if model_size not in valid_models:
            errors.append(f"Invalid model size: {model_size}")
            
        # Validate AI settings
        primary_model = self.get('ai.primary_model')
        if not primary_model or not isinstance(primary_model, str):
            errors.append("Invalid AI primary model configuration")
            
        # Validate paths
        resolve_script_path = self.get('resolve.script_path')
        if resolve_script_path and not Path(resolve_script_path).exists():
            errors.append(f"DaVinci Resolve script path does not exist: {resolve_script_path}")
            
        return errors
        
    def is_apple_silicon_optimized(self) -> bool:
        """
        Check if configuration is optimized for Apple Silicon.
        
        Returns:
            True if optimized for Apple Silicon
        """
        return self.get('transcription.optimize_for_apple_silicon', ConfigDefaults.TRANSCRIPTION_OPTIMIZE_APPLE_SILICON)
        
    def get_transcription_config(self) -> Dict[str, Any]:
        """
        Get transcription configuration.
        
        Returns:
            Transcription configuration dictionary
        """
        return self.get_section('transcription')
    
    def get_ai_config(self) -> Dict[str, Any]:
        """
        Get AI configuration.
        
        Returns:
            AI configuration dictionary
        """
        return self.get_section('ai')
    
    def get_model_provider_config(self, provider_type: str) -> Dict[str, Any]:
        """
        Get configuration for a specific model provider.
        
        Args:
            provider_type: Type of provider (openrouter, local_lmstudio, etc.)
            
        Returns:
            Provider configuration dictionary
        """
        ai_config = self.get_ai_config()
        providers = ai_config.get('providers', {})
        return providers.get(provider_type, {})
    
    def set_model_provider_api_key(self, provider_type: str, api_key: str):
        """
        Set API key for a model provider.
        
        Args:
            provider_type: Type of provider
            api_key: API key to set
        """
        self.set(f'ai.providers.{provider_type}.api_key', api_key)
    
    def get_model_provider_api_key(self, provider_type: str) -> Optional[str]:
        """
        Get API key for a model provider.
        
        Args:
            provider_type: Type of provider
            
        Returns:
            API key or None if not set
        """
        return self.get(f'ai.providers.{provider_type}.api_key')
    
    def set_model_provider_base_url(self, provider_type: str, base_url: str):
        """
        Set base URL for a model provider.
        
        Args:
            provider_type: Type of provider
            base_url: Base URL to set
        """
        self.set(f'ai.providers.{provider_type}.base_url', base_url)
    
    def get_model_provider_base_url(self, provider_type: str) -> Optional[str]:
        """
        Get base URL for a model provider.
        
        Args:
            provider_type: Type of provider
            
        Returns:
            Base URL or None if not set
        """
        return self.get(f'ai.providers.{provider_type}.base_url')
    
    def get_available_models(self, provider_type: str) -> List[str]:
        """
        Get available models for a provider.
        
        Args:
            provider_type: Type of provider
            
        Returns:
            List of available model names
        """
        provider_config = self.get_model_provider_config(provider_type)
        return provider_config.get('models', [])
    
    def add_model_to_provider(self, provider_type: str, model_name: str):
        """
        Add a model to a provider's available models list.
        
        Args:
            provider_type: Type of provider
            model_name: Name of the model to add
        """
        current_models = self.get_available_models(provider_type)
        if model_name not in current_models:
            current_models.append(model_name)
            self.set(f'ai.providers.{provider_type}.models', current_models)
    
    def remove_model_from_provider(self, provider_type: str, model_name: str):
        """
        Remove a model from a provider's available models list.
        
        Args:
            provider_type: Type of provider
            model_name: Name of the model to remove
        """
        current_models = self.get_available_models(provider_type)
        if model_name in current_models:
            current_models.remove(model_name)
            self.set(f'ai.providers.{provider_type}.models', current_models)
    
    def get_current_provider_type(self) -> str:
        """
        Get the currently selected provider type.
        
        Returns:
            Current provider type
        """
        return self.get('ai.provider_type', ConfigDefaults.AI_PROVIDER_TYPE)
    
    def set_current_provider_type(self, provider_type: str):
        """
        Set the current provider type.
        
        Args:
            provider_type: Provider type to set as current
        """
        self.set('ai.provider_type', provider_type)
    
    def get_supported_provider_types(self) -> List[str]:
        """
        Get list of supported provider types.
        
        Returns:
            List of supported provider type names
        """
        ai_config = self.get_ai_config()
        providers = ai_config.get('providers', {})
        return list(providers.keys())
    
    def is_provider_configured(self, provider_type: str) -> bool:
        """
        Check if a provider is properly configured.
        
        Args:
            provider_type: Type of provider to check
            
        Returns:
            True if provider is configured, False otherwise
        """
        provider_config = self.get_model_provider_config(provider_type)
        
        # For API-based providers, check if API key is set
        if provider_type in ['openrouter', 'qwen', 'deepseek', 'kimi', 'glm']:
            api_key = provider_config.get('api_key', '')
            return bool(api_key and api_key.strip())
        
        # For local providers, check if base URL is accessible
        elif provider_type in ['local_lmstudio', 'local_ollama']:
            base_url = provider_config.get('base_url', '')
            return bool(base_url and base_url.strip())
        
        return False