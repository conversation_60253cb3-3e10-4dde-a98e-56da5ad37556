"""
Da<PERSON><PERSON>ci Resolve AI Assistant - Workflow Manager

This module manages complex workflows that combine transcription, AI analysis,
and DaVinci Resolve operations.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from .orchestrator import AIOrchestrator
from .config_manager import ConfigManager

logger = logging.getLogger(__name__)

class WorkflowStatus(Enum):
    """Workflow execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class WorkflowStep:
    """Represents a single step in a workflow."""
    name: str
    description: str
    function: Callable
    args: tuple = ()
    kwargs: Optional[Dict[str, Any]] = None
    timeout: Optional[float] = None
    retry_count: int = 0
    
    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}

@dataclass
class WorkflowResult:
    """Result of a workflow execution."""
    success: bool
    steps_completed: int
    total_steps: int
    results: Dict[str, Any]
    errors: List[str]
    execution_time: float
    status: WorkflowStatus

class WorkflowManager:
    """
    Manages complex workflows for DaVinci Resolve AI Assistant.
    """
    
    def __init__(self, orchestrator: AIOrchestrator, config_manager: ConfigManager):
        """
        Initialize workflow manager.
        
        Args:
            orchestrator: AI orchestrator instance
            config_manager: Configuration manager
        """
        self.orchestrator = orchestrator
        self.config_manager = config_manager
        self.active_workflows: Dict[str, Dict[str, Any]] = {}
        self.workflow_history: List[Dict[str, Any]] = []
        
    async def create_timeline_analysis_workflow(self, timeline_path: Optional[str] = None) -> List[WorkflowStep]:
        """
        Create a workflow for analyzing a DaVinci Resolve timeline.
        
        Args:
            timeline_path: Optional path to timeline
            
        Returns:
            List of workflow steps
        """
        steps = []
        
        # Step 1: Extract audio from timeline
        steps.append(WorkflowStep(
            name="extract_audio",
            description="Extract audio from timeline",
            function=self._extract_timeline_audio,
            args=(timeline_path,),
            timeout=60.0
        ))
        
        # Step 2: Transcribe audio with Apple Silicon optimization
        steps.append(WorkflowStep(
            name="transcribe_audio",
            description="Transcribe audio using faster-whisper",
            function=self._transcribe_timeline_audio,
            timeout=300.0  # 5 minutes for large files
        ))
        
        # Step 3: Analyze transcription content
        steps.append(WorkflowStep(
            name="analyze_content",
            description="Analyze transcribed content",
            function=self._analyze_transcription,
            timeout=60.0
        ))
        
        # Step 4: Generate editing suggestions
        steps.append(WorkflowStep(
            name="generate_suggestions",
            description="Generate editing suggestions",
            function=self._generate_editing_suggestions,
            timeout=120.0
        ))
        
        # Step 5: Clean up temporary files
        steps.append(WorkflowStep(
            name="cleanup",
            description="Clean up temporary files",
            function=self._cleanup_workflow,
            timeout=30.0
        ))
        
        return steps
        
    async def execute_workflow(self, workflow_steps: List[WorkflowStep], 
                              workflow_id: str = "default") -> WorkflowResult:
        """
        Execute a workflow with the given steps.
        
        Args:
            workflow_steps: List of workflow steps
            workflow_id: Unique workflow identifier
            
        Returns:
            Workflow execution result
        """
        logger.info(f"Starting workflow: {workflow_id}")
        
        start_time = asyncio.get_event_loop().time()
        results = {}
        errors = []
        steps_completed = 0
        status = WorkflowStatus.RUNNING
        step_data: Dict[str, Any] = {}
        
        try:
            self.active_workflows[workflow_id] = {
                'steps': workflow_steps,
                'status': WorkflowStatus.RUNNING,
                'start_time': start_time
            }
            
            for i, step in enumerate(workflow_steps):
                logger.info(f"Executing step {i+1}/{len(workflow_steps)}: {step.name}")
                
                try:
                    # Prepare arguments - pass previous step data if no args specified
                    if not step.args and step_data:
                        args = (step_data,)
                    else:
                        args = step.args
                    
                    # Execute the step
                    kwargs = step.kwargs or {}
                    if asyncio.iscoroutinefunction(step.function):
                        result = await asyncio.wait_for(
                            step.function(*args, **kwargs),
                            timeout=step.timeout
                        )
                    else:
                        result = step.function(*args, **kwargs)
                        
                    results[step.name] = result
                    step_data[step.name] = result
                    steps_completed += 1
                    
                    logger.info(f"Step completed: {step.name}")
                    
                except asyncio.TimeoutError:
                    error_msg = f"Step timed out: {step.name} (timeout: {step.timeout}s)"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    
                except Exception as e:
                    error_msg = f"Step failed: {step.name} - {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    
                    # Retry logic if configured
                    if step.retry_count > 0:
                        logger.info(f"Retrying step: {step.name} (retry {step.retry_count})")
                        step.retry_count -= 1
                        # Re-execute the same step
                        continue
                        
            execution_time = asyncio.get_event_loop().time() - start_time
            status = WorkflowStatus.COMPLETED if not errors else WorkflowStatus.FAILED
            
            logger.info(f"Workflow completed: {workflow_id} (status: {status.value})")
            
        except Exception as e:
            error_msg = f"Workflow execution failed: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            status = WorkflowStatus.FAILED
            
        finally:
            execution_time = asyncio.get_event_loop().time() - start_time
            self.active_workflows.pop(workflow_id, None)
            
            result = WorkflowResult(
                success=status == WorkflowStatus.COMPLETED,
                steps_completed=steps_completed,
                total_steps=len(workflow_steps),
                results=results,
                errors=errors,
                execution_time=execution_time,
                status=status
            )
            
            self.workflow_history.append({
                'workflow_id': workflow_id,
                'result': result,
                'timestamp': asyncio.get_event_loop().time()
            })
            
            return result
            
    async def execute_timeline_analysis(self, timeline_path: Optional[str] = None) -> WorkflowResult:
        """
        Execute a complete timeline analysis workflow.
        
        Args:
            timeline_path: Optional path to timeline
            
        Returns:
            Workflow execution result
        """
        workflow_id = f"timeline_analysis_{asyncio.get_event_loop().time()}"
        steps = await self.create_timeline_analysis_workflow(timeline_path)
        
        return await self.execute_workflow(steps, workflow_id)
        
    # Helper methods for workflow steps
    
    async def _extract_timeline_audio(self, timeline_path_or_step_data: Optional[Any] = None) -> Dict[str, Any]:
        """Extract audio from timeline."""
        logger.info("Extracting timeline audio")
        
        try:
            # Handle both timeline_path string and step_data dict
            timeline_path: Optional[str] = None
            if isinstance(timeline_path_or_step_data, str):
                timeline_path = timeline_path_or_step_data
            elif isinstance(timeline_path_or_step_data, dict):
                timeline_path = timeline_path_or_step_data.get('timeline_path')
            
            if not timeline_path:
                timeline_path = await self.orchestrator.resolve_bridge.get_current_timeline()
                
            if not timeline_path:
                raise ValueError("No timeline path available")
                
            audio_path = await self.orchestrator.resolve_bridge.extract_audio(timeline_path)
            
            result = {
                'audio_path': audio_path,
                'timeline_path': timeline_path
            }
            
            logger.info("Timeline audio extraction completed")
            return result
            
        except Exception as e:
            logger.error(f"Failed to extract timeline audio: {e}")
            raise
        
    async def _transcribe_timeline_audio(self, step_data: dict) -> Dict[str, Any]:
        """Transcribe the extracted audio."""
        logger.info("Transcribing audio")
        
        # Get audio file from previous step
        audio_file = step_data.get('extract_audio', {}).get('audio_path')
        
        if not audio_file:
            raise ValueError("No audio file path provided from previous step")
        
        try:
            # Use the orchestrator's transcription engine for actual transcription
            transcription_result = await self.orchestrator.transcribe_audio(audio_file)
            
            # Convert TranscriptionResult to dict format
            transcription = {
                "text": transcription_result.text,
                "segments": transcription_result.segments or [],
                "language": transcription_result.language or "en",
                "confidence": transcription_result.confidence or 0.8
            }
            
            logger.info(f"Audio transcription completed. Text length: {len(transcription['text'])}")
            return {'transcribe_audio': transcription}
            
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            raise RuntimeError(f"Failed to transcribe audio: {e}")
        
    async def _analyze_transcription(self, step_data: dict) -> Dict[str, Any]:
        """Analyze the transcription content."""
        logger.info("Analyzing transcription content")
        
        # Get transcription from previous step - handle double-nesting
        transcription_step_result = step_data.get('transcribe_audio', {})
        # The result is nested: {'transcribe_audio': {'text': '...', ...}}
        transcription_data = transcription_step_result.get('transcribe_audio', {})
        transcription_text = transcription_data.get('text', '')
        
        if not transcription_text:
            raise ValueError("No transcription text available for analysis")
        
        try:
            # Get timeline info for context
            timeline_info = await self.orchestrator.get_timeline_info()
            
            # Use the orchestrator's analyze_content method for real analysis
            analysis_result = await self.orchestrator.analyze_content(transcription_text, timeline_info)
            
            # Add the original transcription data to the result
            analysis_result['transcription'] = transcription_data
            
            logger.info("Transcription analysis completed")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            raise RuntimeError(f"Failed to analyze transcription: {e}")
        
    async def _generate_editing_suggestions(self, step_data: dict) -> Dict[str, Any]:
        """Generate editing suggestions based on analysis."""
        logger.info("Generating editing suggestions")
        
        # Get analysis from previous step
        analysis = step_data.get('analyze_transcription', {})
        
        # Mock suggestions
        suggestions = {
            "cuts": [
                {
                    "time": 2.5,
                    "reason": "Natural pause in speech",
                    "confidence": 0.8
                }
            ],
            "transitions": [
                {
                    "type": "fade",
                    "from": 0.0,
                    "to": 1.0,
                    "reason": "Smooth introduction"
                }
            ],
            "effects": [
                {
                    "type": "text_overlay",
                    "time": 5.0,
                    "text": "Key Point",
                    "reason": "Emphasize important content"
                }
            ],
            "analysis": analysis
        }
        
        logger.info("Editing suggestions generated")
        return suggestions
        
    async def _cleanup_workflow(self, step_data: dict) -> Dict[str, Any]:
        """Clean up temporary files and resources."""
        logger.info("Cleaning up workflow resources")
        
        # Mock cleanup
        cleanup_result = {
            "temp_files_removed": 1,
            "resources_freed": True,
            "status": "completed",
            "final_result": step_data
        }
        
        logger.info("Workflow cleanup completed")
        return cleanup_result
        
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        Get status of an active workflow.
        
        Args:
            workflow_id: Workflow identifier
            
        Returns:
            Workflow status information
        """
        return self.active_workflows.get(workflow_id)
        
    def get_workflow_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent workflow execution history.
        
        Args:
            limit: Maximum number of entries to return
            
        Returns:
            List of workflow history entries
        """
        return self.workflow_history[-limit:]