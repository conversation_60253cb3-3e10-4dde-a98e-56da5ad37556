"""
DaVinci Resolve AI Assistant - Core Orchestrator

This module orchestrates the interaction between transcription services,
AI analysis, and DaVinci Resolve integration.
"""

import asyncio
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path

from ..transcription import TranscriptionEngine, TranscriptionConfig, TranscriptionResult
from ..resolve_bridge import DaVinciResolveBridge
from ..ai_engine import AIEngine
from .config_manager import ConfigManager

logger = logging.getLogger(__name__)

class AIOrchestrator:
    """
    Core orchestrator that manages the workflow between transcription,
    AI analysis, and DaVinci Resolve integration.
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the AI orchestrator.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager or ConfigManager()
        self.transcription_engine = None
        self.resolve_bridge = None
        self.ai_engine = None
        self._setup_logging()
        
    def _setup_logging(self):
        """Set up logging configuration."""
        log_level = self.config_manager.get('logging.level', 'INFO')
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
    async def initialize(self):
        """Initialize all components."""
        logger.info("Initializing AI Orchestrator...")
        
        # Initialize transcription engine with Apple Silicon optimization
        transcription_config = self._get_transcription_config()
        self.transcription_engine = TranscriptionEngine(transcription_config)
        
        # Initialize DaVinci Resolve bridge
        self.resolve_bridge = DaVinciResolveBridge()
        await self.resolve_bridge.connect()
        
        # Initialize AI engine
        ai_config = self.config_manager.get('ai_engine', {})
        self.ai_engine = AIEngine(ai_config)
        
        logger.info("AI Orchestrator initialized successfully")
        
    def _get_transcription_config(self) -> TranscriptionConfig:
        """Get transcription configuration with Apple Silicon optimizations."""
        config_data = self.config_manager.get('transcription', {})
        
        # Filter out non-TranscriptionConfig fields (like 'engine')
        transcription_fields = {k: v for k, v in config_data.items() 
                              if k in ['model_size', 'language', 'device', 'compute_type', 
                                      'beam_size', 'best_of', 'temperature', 'condition_on_previous_text',
                                      'sample_rate', 'chunk_length', 'overlap', 'output_format',
                                      'include_word_timestamps', 'use_metal', 'optimize_for_apple_silicon',
                                      'max_retries', 'fallback_model']}
        
        # Ensure Apple Silicon optimizations are enabled
        if not transcription_fields.get('optimize_for_apple_silicon'):
            transcription_fields['optimize_for_apple_silicon'] = True
            transcription_fields['device'] = 'auto'
            transcription_fields['compute_type'] = 'float16'
            
        return TranscriptionConfig.from_dict(transcription_fields)
        
    async def process_timeline(self, timeline_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a DaVinci Resolve timeline for transcription and analysis.
        
        Args:
            timeline_path: Optional path to timeline file
            
        Returns:
            Dictionary containing transcription results and AI analysis
        """
        logger.info("Processing DaVinci Resolve timeline...")
        
        try:
            # Get current timeline from DaVinci Resolve
            if not timeline_path:
                timeline_path = await self.resolve_bridge.get_current_timeline()
                
            if not timeline_path:
                raise ValueError("No timeline specified and no active timeline in DaVinci Resolve")
                
            # Extract audio from timeline
            logger.info(f"Extracting audio from timeline: {timeline_path}")
            audio_path = await self.resolve_bridge.extract_audio(timeline_path)
            
            # Transcribe the audio
            logger.info("Transcribing audio...")
            transcription_result = await self.transcribe_audio(audio_path)
            
            # Clean up temporary audio file
            if audio_path != timeline_path and Path(audio_path).exists():
                Path(audio_path).unlink()
                
            return {
                'timeline_path': timeline_path,
                'transcription': transcription_result,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Error processing timeline: {e}")
            return {
                'timeline_path': timeline_path,
                'error': str(e),
                'success': False
            }
            
    async def transcribe_audio(self, audio_path: str) -> TranscriptionResult:
        """
        Transcribe audio file using faster-whisper with Apple Silicon optimization.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Transcription result with text and metadata
        """
        logger.info(f"Transcribing audio: {audio_path}")
        
        try:
            # Process audio file
            result = self.transcription_engine.transcribe_audio(audio_path)
            
            if result:
                logger.info(f"Transcription completed successfully. Text length: {len(result.text)}")
                return result
            else:
                raise RuntimeError("Transcription failed - no result returned")
                
        except Exception as e:
            logger.error(f"Transcription error: {e}")
            raise RuntimeError(f"Failed to transcribe audio: {e}")
            
    async def analyze_content(self, transcription: str, timeline_info: dict) -> dict:
        """
        Analyze transcribed content and generate editing suggestions.
        
        Args:
            transcription: Transcribed text content
            timeline_info: Timeline metadata
            
        Returns:
            dict: Analysis results and editing suggestions
        """
        logger.info("Analyzing transcription content...")
        
        if not self.ai_engine:
            logger.error("AI engine not initialized")
            raise RuntimeError("AI engine not initialized")
        
        try:
            # Use AI engine for content analysis and editing suggestions
            analysis_result = self.ai_engine.analyze_content(transcription)
            suggestions = self.ai_engine.generate_editing_suggestions(analysis_result, timeline_info)
            
            result = {
                'key_topics': analysis_result.key_topics,
                'sentiment': analysis_result.sentiment,
                'speakers': analysis_result.speakers,
                'editing_suggestions': {
                    'cuts': [{'time': cut.time, 'reason': cut.reason} for cut in suggestions.cuts],
                    'transitions': [{'time': trans.time, 'type': trans.type} for trans in suggestions.transitions],
                    'effects': [{'time': effect.time, 'type': effect.type} for effect in suggestions.effects]
                }
            }
            
            logger.info("Transcription analysis completed")
            return result
            
        except Exception as e:
            logger.error(f"Content analysis failed: {e}")
            raise RuntimeError("Content analysis failed") from e
        
    async def get_timeline_info(self) -> Dict[str, Any]:
        """
        Get information about the current DaVinci Resolve timeline.
        
        Returns:
            Timeline information dictionary
        """
        return await self.resolve_bridge.get_timeline_info()
        
    async def cleanup(self):
        """Cleanup resources."""
        logger.info("Cleaning up AI Orchestrator...")
        
        if self.transcription_engine:
            self.transcription_engine.cleanup()
            
        if self.resolve_bridge:
            await self.resolve_bridge.disconnect()
            
        if self.ai_engine:
            self.ai_engine.cleanup()
            
        logger.info("AI Orchestrator cleanup completed")
        
    def get_status(self) -> Dict[str, Any]:
        """
        Get current status of the orchestrator.
        
        Returns:
            Status dictionary
        """
        return {
            'transcription_engine': self.transcription_engine is not None,
            'resolve_bridge': self.resolve_bridge is not None,
            'resolve_connected': self.resolve_bridge.connected if self.resolve_bridge else False,
            'ai_engine': self.ai_engine is not None,
            'config_manager': self.config_manager is not None,
            'apple_silicon_optimized': self.config_manager.get('transcription.optimize_for_apple_silicon', False)
        }