"""
Flask web application for DaVinci Resolve AI Assistant UI.
Provides a modern web interface for the AI-powered video editing assistant.
"""

import os
import sys
import json
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import asdict

from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
from flask_cors import CORS
from werkzeug.utils import secure_filename

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.core.orchestrator import AIOrchestrator
from src.core.config_manager import ConfigManager
from src.utils.logger import get_logger

logger = get_logger(__name__)

# Get the directory where this file is located
current_dir = Path(__file__).parent

app = Flask(__name__, 
           template_folder=str(current_dir / 'templates'),
           static_folder=str(current_dir / 'static'))
app.secret_key = 'davinci-ai-assistant-secret-key-2024'
CORS(app)

# Global orchestrator instance
orchestrator = None
config_manager = None

UPLOAD_FOLDER = 'uploads'
ALLOWED_AUDIO_EXTENSIONS = {'wav', 'mp3', 'm4a', 'flac', 'aac'}
ALLOWED_VIDEO_EXTENSIONS = {'mp4', 'mov', 'avi', 'mkv', 'mxf'}

# Create upload folder if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size


def allowed_file(filename: str, allowed_extensions: set) -> bool:
    """Check if file extension is allowed."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions


def initialize_app():
    """Initialize the Flask app with orchestrator and config manager."""
    global orchestrator, config_manager
    
    try:
        config_manager = ConfigManager()
        orchestrator = AIOrchestrator(config_manager)
        
        # Run the async initialization in a new event loop
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        loop.run_until_complete(orchestrator.initialize())
        logger.info("UI application initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize UI application: {e}")
        return False


@app.route('/')
def index():
    """Main dashboard page."""
    try:
        status = orchestrator.get_status() if orchestrator else {"status": "not_initialized"}
        return render_template('index.html', status=status)
    except Exception as e:
        logger.error(f"Error rendering index: {e}")
        return render_template('error.html', error=str(e)), 500


@app.route('/dashboard')
def dashboard():
    """Dashboard with system status and recent activities."""
    try:
        status = orchestrator.get_status() if orchestrator else {"status": "not_initialized"}
        return render_template('dashboard.html', status=status)
    except Exception as e:
        logger.error(f"Error rendering dashboard: {e}")
        return render_template('error.html', error=str(e)), 500


@app.route('/transcription')
def transcription():
    """Transcription interface."""
    return render_template('transcription.html')


@app.route('/ai-analysis')
def ai_analysis():
    """AI analysis interface."""
    return render_template('ai_analysis.html')


@app.route('/settings')
def settings():
    """Settings configuration page."""
    try:
        config = config_manager.config_data if config_manager else {}
        return render_template('settings.html', config=config)
    except Exception as e:
        logger.error(f"Error loading settings: {e}")
        return render_template('error.html', error=str(e)), 500


@app.route('/api/status')
def api_status():
    """API endpoint for system status."""
    try:
        status = orchestrator.get_status() if orchestrator else {"status": "not_initialized"}
        return jsonify(status)
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        return jsonify({"error": str(e), "status": "error"}), 500


@app.route('/api/upload-audio', methods=['POST'])
def upload_audio():
    """Upload audio file for transcription."""
    try:
        if 'audio_file' not in request.files:
            return jsonify({"error": "No audio file provided"}), 400
        
        file = request.files['audio_file']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        if not allowed_file(file.filename, ALLOWED_AUDIO_EXTENSIONS):
            return jsonify({"error": "Invalid file type. Allowed: wav, mp3, m4a, flac, aac"}), 400
        
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        logger.info(f"Audio file uploaded: {filename}")
        return jsonify({
            "message": "Audio file uploaded successfully",
            "filename": filename,
            "path": filepath
        })
        
    except Exception as e:
        logger.error(f"Error uploading audio: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/transcribe', methods=['POST'])
def transcribe_audio():
    """Transcribe audio file."""
    try:
        # Check if file is in request
        if 'audio' not in request.files:
            return jsonify({"error": "No audio file provided"}), 400
        
        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        # Get transcription settings from form data
        model = request.form.get('model', 'faster-whisper')
        language = request.form.get('language', 'auto')
        speaker_diarization = request.form.get('speaker_diarization', 'false').lower() == 'true'
        profanity_filter = request.form.get('profanity_filter', 'false').lower() == 'true'
        
        # Save uploaded file
        if audio_file and allowed_file(audio_file.filename, ALLOWED_AUDIO_EXTENSIONS):
            filename = secure_filename(audio_file.filename)
            audio_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            audio_file.save(audio_path)
            
            logger.info(f"Audio file saved to: {audio_path}")
            
            # Run transcription using orchestrator (async method)
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                transcription_result = loop.run_until_complete(
                    orchestrator.transcribe_audio(audio_path)
                )
                loop.close()
            except Exception as e:
                logger.error(f"Transcription failed: {e}")
                # Clean up uploaded file on error
                try:
                    os.unlink(audio_path)
                except:
                    pass
                return jsonify({"error": f"Transcription failed: {str(e)}"}), 500
            
            logger.info(f"Transcription completed for: {audio_path}")
            
            # Clean up uploaded file
            try:
                os.remove(audio_path)
            except Exception as cleanup_error:
                logger.warning(f"Could not clean up file {audio_path}: {cleanup_error}")
            
            return jsonify({
                "success": True,
                "message": "Transcription completed",
                "transcription": transcription_result.to_dict() if transcription_result else None,
                "transcription_id": f"trans_{int(asyncio.get_event_loop().time())}"
            })
        else:
            return jsonify({"error": "Invalid file type"}), 400
        
    except Exception as e:
        logger.error(f"Error transcribing audio: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/analyze-content', methods=['POST'])
def analyze_content():
    """Analyze content with AI."""
    try:
        data = request.get_json()
        content = data.get('content')
        content_type = data.get('type', 'transcription')
        
        if not content:
            return jsonify({"error": "No content provided"}), 400
        
        # Handle async method call
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # For content analysis, we need timeline info as the second parameter
        timeline_info = {"duration": 0, "name": "unknown"}  # Default timeline info
        analysis_result = loop.run_until_complete(orchestrator.analyze_content(content, timeline_info))
        
        logger.info(f"Content analysis completed")
        return jsonify({
            "message": "Analysis completed",
            "analysis": analysis_result,
            "success": True
        })
        
    except Exception as e:
        logger.error(f"Error analyzing content: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/resolve-connect', methods=['POST'])
def resolve_connect():
    """Connect to DaVinci Resolve."""
    try:
        if not orchestrator or not orchestrator.resolve_bridge:
            return jsonify({"error": "Orchestrator not initialized"}), 500
        
        # Run the async connect method
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        success = loop.run_until_complete(orchestrator.resolve_bridge.connect())
        
        if success:
            logger.info("Connected to DaVinci Resolve")
            return jsonify({"message": "Connected to DaVinci Resolve successfully"})
        else:
            logger.warning("Failed to connect to DaVinci Resolve")
            return jsonify({"error": "Failed to connect to DaVinci Resolve"}), 500
            
    except Exception as e:
        logger.error(f"Error connecting to Resolve: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/resolve-status')
def resolve_status():
    """Get DaVinci Resolve connection status."""
    try:
        if not orchestrator or not orchestrator.resolve_bridge:
            return jsonify({"connected": False, "error": "Bridge not initialized"})
        
        status = {
            "connected": orchestrator.resolve_bridge.connected,
            "current_project": None,
            "current_timeline": None
        }
        
        if status["connected"]:
            try:
                # Get or create event loop for async calls
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                
                project = loop.run_until_complete(orchestrator.resolve_bridge.get_current_project())
                timeline = loop.run_until_complete(orchestrator.resolve_bridge.get_current_timeline())
                
                if project:
                    status["current_project"] = {
                        "name": project.name,
                        "timeline_count": project.timeline_count,
                        "current_timeline": project.current_timeline,
                        "media_pool_items": len(project.media_pool_items) if project.media_pool_items else 0
                    }
                
                if timeline:
                    status["current_timeline"] = {
                        "name": timeline.name,
                        "duration": timeline.duration,
                        "frame_rate": timeline.frame_rate,
                        "resolution": timeline.resolution
                    }
                    
            except Exception as e:
                logger.warning(f"Error getting Resolve project info: {e}")
        
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"Error getting Resolve status: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/extract-audio', methods=['POST'])
def extract_audio():
    """Extract audio from current timeline."""
    try:
        if not orchestrator or not orchestrator.resolve_bridge.connected:
            return jsonify({"error": "Not connected to DaVinci Resolve"}), 400
        
        # Extract audio from current timeline using async handling
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            audio_path = loop.run_until_complete(orchestrator.resolve_bridge.extract_audio())
        finally:
            loop.close()
        
        if audio_path and os.path.exists(audio_path):
            logger.info(f"Audio extracted to: {audio_path}")
            return jsonify({
                "success": True,
                "message": "Audio extracted successfully",
                "audio_path": audio_path
            })
        else:
            return jsonify({"error": "Failed to extract audio"}), 500
            
    except Exception as e:
        logger.error(f"Error extracting audio: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/config', methods=['GET', 'POST'])
def api_config():
    """Get or update configuration."""
    try:
        if request.method == 'GET':
            config = config_manager.config_data if config_manager else {}
            return jsonify(config)
        
        elif request.method == 'POST':
            data = request.get_json()
            if config_manager and data:
                config_manager.update_config(data)
                config_manager.save_config()
                logger.info("Configuration updated")
                return jsonify({"message": "Configuration updated successfully"})
            else:
                return jsonify({"error": "Invalid configuration data"}), 400
                
    except Exception as e:
        logger.error(f"Error handling configuration: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/get-config', methods=['GET'])
def api_get_config():
    """Get configuration (alias for /api/config GET)."""
    try:
        config = config_manager.config_data if config_manager else {}
        return jsonify(config)
    except Exception as e:
        logger.error(f"Error getting configuration: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/system-status', methods=['GET'])
def api_system_status():
    """Get system status (alias for /api/status)."""
    try:
        status = {
            'config_manager': config_manager is not None,
            'orchestrator': orchestrator is not None,
            'resolve_connected': False,
            'resolve_bridge': False,
            'ai_engine': False,
            'transcription_engine': False,
            'apple_silicon_optimized': True
        }
        
        if orchestrator:
            # Check if orchestrator components are initialized
            status['ai_engine'] = hasattr(orchestrator, 'ai_engine') and orchestrator.ai_engine is not None
            status['transcription_engine'] = hasattr(orchestrator, 'transcription_engine') and orchestrator.transcription_engine is not None
            status['resolve_bridge'] = hasattr(orchestrator, 'resolve_bridge') and orchestrator.resolve_bridge is not None
            
            # Check DaVinci Resolve connection if bridge exists
            if status['resolve_bridge']:
                try:
                    status['resolve_connected'] = orchestrator.resolve_bridge.connected
                except:
                    status['resolve_connected'] = False
        
        return jsonify(status)
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/update-config', methods=['POST'])
def api_update_config():
    """Update configuration (alias for /api/config POST)."""
    try:
        data = request.get_json()
        if config_manager and data:
            config_manager.update_config(data)
            config_manager.save_config()
            logger.info("Configuration updated")
            return jsonify({"success": True, "message": "Configuration updated successfully"})
        else:
            return jsonify({"success": False, "error": "Invalid configuration data"}), 400
    except Exception as e:
        logger.error(f"Error updating configuration: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/reset-config', methods=['POST'])
def api_reset_config():
    """Reset configuration to defaults."""
    try:
        if config_manager:
            config_manager.reset_to_defaults()
            config_manager.save_config()
            logger.info("Configuration reset to defaults")
            return jsonify({"success": True, "message": "Configuration reset to defaults"})
        else:
            return jsonify({"success": False, "error": "Configuration manager not available"}), 500
    except Exception as e:
        logger.error(f"Error resetting configuration: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/clear-cache', methods=['POST'])
def api_clear_cache():
    """Clear application cache."""
    try:
        # Clear any cache directories or files
        cache_cleared = False
        
        # Check for common cache directories
        cache_dirs = ['cache', 'tmp', '.cache', '__pycache__']
        for cache_dir in cache_dirs:
            cache_path = Path(cache_dir)
            if cache_path.exists():
                import shutil
                shutil.rmtree(cache_path, ignore_errors=True)
                cache_cleared = True
        
        logger.info("Cache cleared")
        return jsonify({"success": True, "message": "Cache cleared successfully"})
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/clear-logs', methods=['POST'])
def api_clear_logs():
    """Clear application logs."""
    try:
        # Clear log files
        logs_cleared = False
        
        # Check for common log directories and files
        log_paths = ['logs', 'log', 'app.log', 'error.log', 'debug.log']
        for log_path in log_paths:
            path = Path(log_path)
            if path.exists():
                if path.is_file():
                    path.unlink()
                    logs_cleared = True
                elif path.is_dir():
                    import shutil
                    shutil.rmtree(path, ignore_errors=True)
                    logs_cleared = True
        
        logger.info("Logs cleared")
        return jsonify({"success": True, "message": "Logs cleared successfully"})
    except Exception as e:
        logger.error(f"Error clearing logs: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/refresh-resolve-info', methods=['POST'])
def api_refresh_resolve_info():
    """Refresh DaVinci Resolve connection information."""
    try:
        if orchestrator and hasattr(orchestrator, 'resolve_bridge') and orchestrator.resolve_bridge:
            # Try to refresh the connection
            try:
                connection_status = orchestrator.resolve_bridge.connected
                logger.info("DaVinci Resolve connection refreshed")
                return jsonify({
                    "success": True, 
                    "message": "Connection refreshed",
                    "connected": connection_status
                })
            except Exception as bridge_error:
                logger.warning(f"Failed to refresh DaVinci Resolve connection: {bridge_error}")
                return jsonify({
                    "success": False, 
                    "error": "Failed to connect to DaVinci Resolve",
                    "connected": False
                })
        else:
            return jsonify({
                "success": False, 
                "error": "DaVinci Resolve bridge not available",
                "connected": False
            })
    except Exception as e:
        logger.error(f"Error refreshing DaVinci Resolve info: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return render_template('404.html'), 404


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {error}")
    return render_template('error.html', error=str(error)), 500


def run_app(host='127.0.0.1', port=5000, debug=False):
    """Run the Flask application."""
    if not initialize_app():
        logger.error("Failed to initialize application")
        return False
    
    logger.info(f"Starting DaVinci Resolve AI Assistant UI on http://{host}:{port}")
    app.run(host=host, port=port, debug=debug)
    return True


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='DaVinci Resolve AI Assistant UI')
    parser.add_argument('--host', default='127.0.0.1', help='Host to bind to')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    
    args = parser.parse_args()
    
    run_app(host=args.host, port=args.port, debug=args.debug)