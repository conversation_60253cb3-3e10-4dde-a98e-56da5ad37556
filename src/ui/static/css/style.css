/* Da<PERSON><PERSON>ci Resolve AI Assistant - Custom Styles */

:root {
    --primary-color: #ff6b35;
    --secondary-color: #004e89;
    --accent-color: #1a659e;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    --dark-bg: #1a1a1a;
    --dark-card: #2d2d2d;
    --dark-border: #404040;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --sidebar-width: 280px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Sidebar Styles */
.sidebar {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    width: var(--sidebar-width);
    min-height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h4 {
    color: white;
    font-weight: 600;
    margin: 0;
    font-size: 1.1rem;
}

.nav-pills .nav-link {
    color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    margin: 0.25rem 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-pills .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.nav-pills .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: var(--primary-color);
    font-weight: 500;
}

.nav-pills .nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    padding: 2rem;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

.sidebar.collapsed + .main-content {
    margin-left: 70px;
}

/* Cards */
.card {
    background-color: var(--dark-card);
    border: 1px solid var(--dark-border);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid var(--dark-border);
    padding: 1.25rem 1.5rem;
}

.card-header h5 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-connected {
    background-color: rgba(46, 204, 113, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status-disconnected {
    background-color: rgba(231, 76, 60, 0.2);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.status-mock {
    background-color: rgba(243, 156, 18, 0.2);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #ff8c42);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff5722, var(--primary-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Forms */
.form-control, .form-select {
    background-color: var(--dark-card);
    border: 1px solid var(--dark-border);
    color: var(--text-primary);
    border-radius: 8px;
    padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
    background-color: var(--dark-card);
    border-color: var(--primary-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
}

.form-label {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Progress Bars */
.progress {
    background-color: var(--dark-border);
    border-radius: 10px;
    height: 8px;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 10px;
}

/* Tables */
.table-dark {
    --bs-table-bg: var(--dark-card);
    --bs-table-border-color: var(--dark-border);
}

.table-dark th {
    border-color: var(--dark-border);
    color: var(--text-primary);
    font-weight: 600;
}

.table-dark td {
    border-color: var(--dark-border);
    color: var(--text-secondary);
}

/* Modals */
.modal-content {
    background-color: var(--dark-card);
    border: 1px solid var(--dark-border);
    border-radius: 12px;
    color: var(--text-primary);
}

.modal-header {
    border-bottom: 1px solid var(--dark-border);
}

.modal-footer {
    border-top: 1px solid var(--dark-border);
}

/* Toast Notifications */
.toast {
    background-color: var(--dark-card);
    border: 1px solid var(--dark-border);
    border-radius: 8px;
    color: var(--text-primary);
}

.toast-header {
    background-color: transparent;
    border-bottom: 1px solid var(--dark-border);
    color: var(--text-primary);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--dark-border);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        position: fixed;
        z-index: 1050;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
    
    .sidebar.collapsed + .main-content {
        margin-left: 0;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glow {
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

.border-glow {
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 10px rgba(255, 107, 53, 0.2);
}