/**
 * DaVinci Resolve AI Assistant - UI JavaScript
 * Main application logic and interactions
 */

class DavinciAIApp {
    constructor() {
        this.apiBase = '/api';
        this.statusUpdateInterval = null;
        this.currentProject = null;
        this.currentTimeline = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startStatusUpdates();
        this.initializeTooltips();
        this.setupSidebarToggle();
    }

    setupEventListeners() {
        // Global event listeners
        document.addEventListener('DOMContentLoaded', () => {
            this.setupNavigation();
            this.setupFormHandlers();
            this.setupModalHandlers();
        });

        // Sidebar navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                this.handleNavigation(e);
            });
        });

        // File upload handlers
        document.querySelectorAll('input[type="file"]').forEach(input => {
            input.addEventListener('change', (e) => {
                this.handleFileSelect(e);
            });
        });

        // Form submissions
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                this.handleFormSubmit(e);
            });
        });
    }

    setupSidebarToggle() {
        const toggleBtn = document.getElementById('sidebarToggle');
        const sidebar = document.querySelector('.sidebar');
        
        if (toggleBtn && sidebar) {
            toggleBtn.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                this.saveSidebarState(sidebar.classList.contains('collapsed'));
            });

            // Load saved state
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
            }
        }
    }

    saveSidebarState(collapsed) {
        localStorage.setItem('sidebarCollapsed', collapsed);
    }

    setupNavigation() {
        // Highlight current page in navigation
        const currentPath = window.location.pathname;
        document.querySelectorAll('.nav-link').forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href.replace('/', ''))) {
                link.classList.add('active');
            }
        });
    }

    setupFormHandlers() {
        // Settings forms
        const settingsForm = document.getElementById('settingsForm');
        if (settingsForm) {
            settingsForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveSettings(new FormData(settingsForm));
            });
        }

        // Transcription form
        const transcriptionForm = document.getElementById('transcriptionForm');
        if (transcriptionForm) {
            transcriptionForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.startTranscription();
            });
        }

        // AI Analysis form
        const analysisForm = document.getElementById('analysisForm');
        if (analysisForm) {
            analysisForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.startAIAnalysis();
            });
        }
    }

    setupModalHandlers() {
        // Modal event handlers
        document.querySelectorAll('[data-bs-toggle="modal"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const target = btn.getAttribute('data-bs-target');
                if (target) {
                    this.prepareModal(target, btn);
                }
            });
        });
    }

    initializeTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    startStatusUpdates() {
        this.updateStatus();
        this.statusUpdateInterval = setInterval(() => {
            this.updateStatus();
        }, 5000); // Update every 5 seconds
    }

    stopStatusUpdates() {
        if (this.statusUpdateInterval) {
            clearInterval(this.statusUpdateInterval);
            this.statusUpdateInterval = null;
        }
    }

    async updateStatus() {
        try {
            const response = await this.apiRequest('/status');
            this.updateStatusDisplay(response);
        } catch (error) {
            console.error('Failed to update status:', error);
            this.showStatusError();
        }
    }

    updateStatusDisplay(status) {
        // Update connection status
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            const statusClass = status.resolve_connected ? 'status-connected' : 
                              status.mock_mode ? 'status-mock' : 'status-disconnected';
            const statusText = status.resolve_connected ? 'Connected' : 
                             status.mock_mode ? 'Mock Mode' : 'Disconnected';
            
            statusElement.className = `status-indicator ${statusClass}`;
            statusElement.innerHTML = `<i class="fas fa-circle"></i> ${statusText}`;
        }

        // Update project info
        if (status.current_project) {
            this.currentProject = status.current_project;
            this.updateProjectInfo(status.current_project);
        }

        if (status.current_timeline) {
            this.currentTimeline = status.current_timeline;
            this.updateTimelineInfo(status.current_timeline);
        }
    }

    updateProjectInfo(project) {
        const projectElement = document.getElementById('currentProject');
        if (projectElement) {
            projectElement.innerHTML = `
                <h6>${project.name}</h6>
                <small class="text-muted">${project.duration || 'Unknown duration'}</small>
            `;
        }
    }

    updateTimelineInfo(timeline) {
        const timelineElement = document.getElementById('currentTimeline');
        if (timelineElement) {
            timelineElement.innerHTML = `
                <h6>${timeline.name}</h6>
                <small class="text-muted">${timeline.duration || 'Unknown duration'}</small>
            `;
        }
    }

    showStatusError() {
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            statusElement.className = 'status-indicator status-disconnected';
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Connection Error';
        }
    }

    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiBase}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const response = await fetch(url, { ...defaultOptions, ...options });
        
        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    async uploadFile(file, endpoint) {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch(`${this.apiBase}${endpoint}`, {
            method: 'POST',
            body: formData,
        });

        if (!response.ok) {
            throw new Error(`File upload failed: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            const fileInfo = document.getElementById('fileInfo');
            if (fileInfo) {
                fileInfo.innerHTML = `
                    <div class="alert alert-info">
                        <strong>Selected:</strong> ${file.name}<br>
                        <small>Size: ${this.formatFileSize(file.size)}</small>
                    </div>
                `;
            }
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async startTranscription() {
        const fileInput = document.getElementById('audioFile');
        const progressBar = document.getElementById('transcriptionProgress');
        const resultDiv = document.getElementById('transcriptionResult');

        if (!fileInput.files[0]) {
            this.showToast('Please select an audio file', 'warning');
            return;
        }

        try {
            this.showProgress(progressBar, 0);
            
            // Upload file
            this.showToast('Uploading audio file...', 'info');
            const uploadResult = await this.uploadFile(fileInput.files[0], '/upload-audio');
            
            this.showProgress(progressBar, 30);
            
            // Start transcription
            this.showToast('Starting transcription...', 'info');
            const transcriptionResult = await this.apiRequest('/transcribe', {
                method: 'POST',
                body: JSON.stringify({ audio_path: uploadResult.path }),
            });
            
            this.showProgress(progressBar, 100);
            
            // Display results
            this.displayTranscriptionResult(transcriptionResult, resultDiv);
            this.showToast('Transcription completed!', 'success');
            
        } catch (error) {
            console.error('Transcription error:', error);
            this.showToast(`Transcription failed: ${error.message}`, 'error');
            this.showProgress(progressBar, 0);
        }
    }

    async startAIAnalysis() {
        const content = document.getElementById('analysisContent').value;
        const analysisType = document.querySelector('input[name="analysisType"]:checked').value;
        const progressBar = document.getElementById('analysisProgress');
        const resultDiv = document.getElementById('analysisResult');

        if (!content) {
            this.showToast('Please enter content to analyze', 'warning');
            return;
        }

        try {
            this.showProgress(progressBar, 0);
            this.showToast('Starting AI analysis...', 'info');
            
            const result = await this.apiRequest('/analyze-content', {
                method: 'POST',
                body: JSON.stringify({ content, type: analysisType }),
            });
            
            this.showProgress(progressBar, 100);
            this.displayAnalysisResult(result, resultDiv);
            this.showToast('Analysis completed!', 'success');
            
        } catch (error) {
            console.error('Analysis error:', error);
            this.showToast(`Analysis failed: ${error.message}`, 'error');
            this.showProgress(progressBar, 0);
        }
    }

    displayTranscriptionResult(result, container) {
        if (container) {
            container.innerHTML = `
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-check-circle"></i> Transcription Result</h6>
                    </div>
                    <div class="card-body">
                        <div class="transcription-text">${result.transcription.text}</div>
                        ${result.transcription.segments ? `
                            <hr>
                            <h6>Segments:</h6>
                            <div class="transcription-segments">
                                ${result.transcription.segments.map(seg => `
                                    <div class="segment">
                                        <span class="timestamp">[${seg.start.toFixed(1)}s - ${seg.end.toFixed(1)}s]</span>
                                        <span class="text">${seg.text}</span>
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }
    }

    displayAnalysisResult(result, container) {
        if (container) {
            container.innerHTML = `
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-brain"></i> AI Analysis Result</h6>
                    </div>
                    <div class="card-body">
                        <pre class="analysis-result">${JSON.stringify(result.analysis, null, 2)}</pre>
                    </div>
                </div>
            `;
        }
    }

    showProgress(progressBar, percentage) {
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
            
            if (percentage >= 100) {
                setTimeout(() => {
                    progressBar.style.width = '0%';
                    progressBar.setAttribute('aria-valuenow', 0);
                }, 1000);
            }
        }
    }

    async saveSettings(formData) {
        try {
            const settings = Object.fromEntries(formData.entries());
            const response = await this.apiRequest('/config', {
                method: 'POST',
                body: JSON.stringify(settings),
            });
            
            this.showToast('Settings saved successfully!', 'success');
        } catch (error) {
            console.error('Settings save error:', error);
            this.showToast(`Failed to save settings: ${error.message}`, 'error');
        }
    }

    showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;

        const toastId = `toast-${Date.now()}`;
        const toastClass = {
            'success': 'bg-success',
            'error': 'bg-danger',
            'warning': 'bg-warning',
            'info': 'bg-info'
        }[type] || 'bg-info';

        const toastHTML = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header ${toastClass} text-white">
                    <strong class="me-auto">Notification</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        
        const toast = new bootstrap.Toast(document.getElementById(toastId));
        toast.show();

        // Remove toast element after it's hidden
        document.getElementById(toastId).addEventListener('hidden.bs.toast', function() {
            this.remove();
        });
    }

    prepareModal(modalSelector, button) {
        const modal = document.querySelector(modalSelector);
        if (!modal) return;

        const modalTitle = modal.querySelector('.modal-title');
        const modalBody = modal.querySelector('.modal-body');
        
        // Customize modal content based on button data attributes
        const title = button.getAttribute('data-modal-title');
        const content = button.getAttribute('data-modal-content');
        
        if (modalTitle) modalTitle.textContent = title || 'Modal';
        if (modalBody && content) modalBody.innerHTML = content;
    }

    handleFormSubmit(event) {
        // Add loading state to submit buttons
        const submitBtn = event.target.querySelector('[type="submit"]');
        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            submitBtn.disabled = true;
            
            // Re-enable button after a delay (or when form processing completes)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        }
    }

    handleNavigation(event) {
        // Add smooth transitions between pages
        const link = event.target.closest('a');
        if (link && link.href && !link.href.includes('#')) {
            document.body.classList.add('fade-out');
        }
    }

    // Utility methods
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Cleanup method
    destroy() {
        this.stopStatusUpdates();
        document.removeEventListener('DOMContentLoaded', this.setupNavigation);
        document.removeEventListener('DOMContentLoaded', this.setupFormHandlers);
        document.removeEventListener('DOMContentLoaded', this.setupModalHandlers);
    }
}

// Initialize the application
let app;

document.addEventListener('DOMContentLoaded', () => {
    app = new DavinciAIApp();
});

// Global functions for template access
window.refreshProjectInfo = function() {
    if (app) app.updateStatus();
};

window.extractAudio = function() {
    if (app) {
        app.showToast('Extracting audio from current timeline...', 'info');
        // Add audio extraction logic here
    }
};

window.showToast = function(message, type) {
    if (app) app.showToast(message, type);
};

// Add fade-out animation
window.addEventListener('beforeunload', () => {
    document.body.classList.add('fade-out');
});