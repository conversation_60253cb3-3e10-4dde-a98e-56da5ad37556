{% extends "base.html" %}

{% block title %}Settings - <PERSON><PERSON><PERSON><PERSON> Resolve AI Assistant{% endblock %}
{% block page_title %}Configuration Settings{% endblock %}

{% block content %}
<div class="row">
    <!-- Settings Navigation -->
    <div class="col-lg-3">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i>
                    Settings
                </h5>
            </div>
            <div class="list-group list-group-flush">
                <a class="list-group-item list-group-item-action active" data-bs-toggle="tab" href="#general-settings">
                    <i class="bi bi-house"></i>
                    General
                </a>
                <a class="list-group-item list-group-item-action" data-bs-toggle="tab" href="#transcription-settings">
                    <i class="bi bi-mic"></i>
                    Transcription
                </a>
                <a class="list-group-item list-group-item-action" data-bs-toggle="tab" href="#ai-settings">
                    <i class="bi bi-robot"></i>
                    AI Models
                </a>
                <a class="list-group-item list-group-item-action" data-bs-toggle="tab" href="#resolve-settings">
                    <i class="bi bi-film"></i>
                    DaVinci Resolve
                </a>
                <a class="list-group-item list-group-item-action" data-bs-toggle="tab" href="#performance-settings">
                    <i class="bi bi-speedometer2"></i>
                    Performance
                </a>
                <a class="list-group-item list-group-item-action" data-bs-toggle="tab" href="#security-settings">
                    <i class="bi bi-shield-lock"></i>
                    Security
                </a>
                <a class="list-group-item list-group-item-action" data-bs-toggle="tab" href="#advanced-settings">
                    <i class="bi bi-sliders"></i>
                    Advanced
                </a>
            </div>
        </div>
        
        <!-- Configuration Status -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Configuration Status</h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-muted">API Keys</span>
                    <span class="badge bg-success" id="api-keys-status">Configured</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-muted">DaVinci Resolve</span>
                    <span class="badge bg-warning" id="resolve-status">Not Connected</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-muted">FFmpeg</span>
                    <span class="badge bg-success" id="ffmpeg-status">Available</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">AI Models</span>
                    <span class="badge bg-success" id="ai-models-status">Ready</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Settings Content -->
    <div class="col-lg-9">
        <div class="tab-content">
            <!-- General Settings -->
            <div class="tab-pane fade show active" id="general-settings">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">General Settings</h5>
                    </div>
                    <div class="card-body">
                        <form id="general-settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="app-name" class="form-label">Application Name</label>
                                        <input type="text" class="form-control" id="app-name" value="DaVinci Resolve AI Assistant">
                                    </div>
                                    <div class="mb-3">
                                        <label for="theme-select" class="form-label">Theme</label>
                                        <select class="form-select" id="theme-select">
                                            <option value="light">Light</option>
                                            <option value="dark">Dark</option>
                                            <option value="auto">Auto (System)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="language-select" class="form-label">Language</label>
                                        <select class="form-select" id="language-select">
                                            <option value="en">English</option>
                                            <option value="es">Spanish</option>
                                            <option value="fr">French</option>
                                            <option value="de">German</option>
                                            <option value="it">Italian</option>
                                            <option value="pt">Portuguese</option>
                                            <option value="ru">Russian</option>
                                            <option value="ja">Japanese</option>
                                            <option value="zh">Chinese</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="auto-save-interval" class="form-label">Auto-save Interval (minutes)</label>
                                        <input type="number" class="form-control" id="auto-save-interval" min="1" max="60" value="5">
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="check-updates" checked>
                                            <label class="form-check-label" for="check-updates">
                                                Check for updates automatically
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="send-analytics" checked>
                                            <label class="form-check-label" for="send-analytics">
                                                Send anonymous usage analytics
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Transcription Settings -->
            <div class="tab-pane fade" id="transcription-settings">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Transcription Settings</h5>
                    </div>
                    <div class="card-body">
                        <form id="transcription-settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="transcription-model" class="form-label">Transcription Model</label>
                                        <select class="form-select" id="transcription-model">
                                            <option value="faster-whisper">faster-whisper (Default)</option>
                                            <option value="whisper">OpenAI Whisper</option>
                                            <option value="whisper-cpp">Whisper.cpp</option>
                                            <option value="whisper-jax">Whisper JAX</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="whisper-model-size" class="form-label">Model Size</label>
                                        <select class="form-select" id="whisper-model-size">
                                            <option value="tiny">tiny (39M)</option>
                                            <option value="base">base (74M)</option>
                                            <option value="small">small (244M)</option>
                                            <option value="medium" selected>medium (769M)</option>
                                            <option value="large">large (1550M)</option>
                                            <option value="large-v2">large-v2 (1550M)</option>
                                            <option value="large-v3">large-v3 (1550M)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="audio-language" class="form-label">Audio Language</label>
                                        <select class="form-select" id="audio-language">
                                            <option value="auto">Auto-detect</option>
                                            <option value="en">English</option>
                                            <option value="es">Spanish</option>
                                            <option value="fr">French</option>
                                            <option value="de">German</option>
                                            <option value="it">Italian</option>
                                            <option value="pt">Portuguese</option>
                                            <option value="ru">Russian</option>
                                            <option value="ja">Japanese</option>
                                            <option value="zh">Chinese</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-vad" checked>
                                            <label class="form-check-label" for="enable-vad">
                                                Enable Voice Activity Detection (VAD)
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-speaker-diarization">
                                            <label class="form-check-label" for="enable-speaker-diarization">
                                                Enable Speaker Diarization
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-timestamps" checked>
                                            <label class="form-check-label" for="enable-timestamps">
                                                Include Timestamps
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="beam-size" class="form-label">Beam Size</label>
                                        <input type="range" class="form-range" id="beam-size" min="1" max="10" value="5">
                                        <small class="form-text text-muted">Higher values = better accuracy, slower processing</small>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Audio Processing</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sample-rate" class="form-label">Sample Rate (Hz)</label>
                                    <select class="form-select" id="sample-rate">
                                        <option value="16000" selected>16,000 Hz</option>
                                        <option value="22050">22,050 Hz</option>
                                        <option value="44100">44,100 Hz</option>
                                        <option value="48000">48,000 Hz</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="audio-format" class="form-label">Audio Format</label>
                                    <select class="form-select" id="audio-format">
                                        <option value="wav" selected>WAV</option>
                                        <option value="mp3">MP3</option>
                                        <option value="flac">FLAC</option>
                                        <option value="m4a">M4A</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="normalize-audio" checked>
                                        <label class="form-check-label" for="normalize-audio">
                                            Normalize Audio Levels
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remove-noise" checked>
                                        <label class="form-check-label" for="remove-noise">
                                            Remove Background Noise
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="trim-silence" checked>
                                        <label class="form-check-label" for="trim-silence">
                                            Trim Silence
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- AI Settings -->
            <div class="tab-pane fade" id="ai-settings">
                <!-- Model Provider Selection -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-cpu"></i>
                            Model Provider Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="provider-type" class="form-label">AI Provider</label>
                                    <select class="form-select" id="provider-type" onchange="updateProviderSettings()">
                                        <option value="openrouter">OpenRouter (Multi-Model)</option>
                                        <option value="qwen">Qwen (Alibaba Cloud)</option>
                                        <option value="deepseek">DeepSeek</option>
                                        <option value="kimi">Kimi (Moonshot AI)</option>
                                        <option value="glm">GLM (Zhipu AI)</option>
                                        <option value="local_lmstudio">Local LM Studio</option>
                                        <option value="local_ollama">Local Ollama</option>
                                    </select>
                                    <small class="form-text text-muted">Choose your preferred AI model provider</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="primary-ai-model" class="form-label">Primary Model</label>
                                    <select class="form-select" id="primary-ai-model">
                                        <!-- Options will be populated dynamically based on provider -->
                                    </select>
                                    <small class="form-text text-muted">Main model for AI operations</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="fallback-ai-model" class="form-label">Fallback Model</label>
                                    <select class="form-select" id="fallback-ai-model">
                                        <option value="">None</option>
                                        <!-- Options will be populated dynamically -->
                                    </select>
                                    <small class="form-text text-muted">Backup model if primary fails</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max-tokens" class="form-label">Max Tokens</label>
                                    <input type="number" class="form-control" id="max-tokens" min="100" max="32768" value="4096">
                                    <small class="form-text text-muted">Maximum response length</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Provider-Specific Settings -->
                <div class="card mb-4" id="provider-settings">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i>
                            Provider Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- OpenRouter Settings -->
                        <div class="provider-config" id="openrouter-config">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="openrouter-api-key" class="form-label">OpenRouter API Key</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="openrouter-api-key" placeholder="sk-or-...">
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('openrouter-api-key')">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">Get your key from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a></small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="openrouter-base-url" class="form-label">Base URL</label>
                                        <input type="url" class="form-control" id="openrouter-base-url" value="https://openrouter.ai/api/v1" readonly>
                                        <small class="form-text text-muted">OpenRouter API endpoint</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Qwen Settings -->
                        <div class="provider-config" id="qwen-config" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="qwen-api-key" class="form-label">Qwen API Key</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="qwen-api-key" placeholder="sk-...">
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('qwen-api-key')">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">Get your key from <a href="https://dashscope.aliyun.com/" target="_blank">Alibaba Cloud DashScope</a></small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="qwen-base-url" class="form-label">Base URL</label>
                                        <input type="url" class="form-control" id="qwen-base-url" value="https://dashscope.aliyuncs.com/api/v1" readonly>
                                        <small class="form-text text-muted">Qwen API endpoint</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- DeepSeek Settings -->
                        <div class="provider-config" id="deepseek-config" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="deepseek-api-key" class="form-label">DeepSeek API Key</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="deepseek-api-key" placeholder="sk-...">
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('deepseek-api-key')">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">Get your key from <a href="https://platform.deepseek.com/" target="_blank">DeepSeek Platform</a></small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="deepseek-base-url" class="form-label">Base URL</label>
                                        <input type="url" class="form-control" id="deepseek-base-url" value="https://api.deepseek.com/v1" readonly>
                                        <small class="form-text text-muted">DeepSeek API endpoint</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Kimi Settings -->
                        <div class="provider-config" id="kimi-config" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="kimi-api-key" class="form-label">Kimi API Key</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="kimi-api-key" placeholder="sk-...">
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('kimi-api-key')">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">Get your key from <a href="https://platform.moonshot.cn/" target="_blank">Moonshot AI</a></small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="kimi-base-url" class="form-label">Base URL</label>
                                        <input type="url" class="form-control" id="kimi-base-url" value="https://api.moonshot.cn/v1" readonly>
                                        <small class="form-text text-muted">Kimi API endpoint</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- GLM Settings -->
                        <div class="provider-config" id="glm-config" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="glm-api-key" class="form-label">GLM API Key</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="glm-api-key" placeholder="...">
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('glm-api-key')">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">Get your key from <a href="https://open.bigmodel.cn/" target="_blank">Zhipu AI</a></small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="glm-base-url" class="form-label">Base URL</label>
                                        <input type="url" class="form-control" id="glm-base-url" value="https://open.bigmodel.cn/api/paas/v4" readonly>
                                        <small class="form-text text-muted">GLM API endpoint</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Local LM Studio Settings -->
                        <div class="provider-config" id="local_lmstudio-config" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lmstudio-base-url" class="form-label">LM Studio URL</label>
                                        <input type="url" class="form-control" id="lmstudio-base-url" value="http://localhost:1234/v1" placeholder="http://localhost:1234/v1">
                                        <small class="form-text text-muted">Local LM Studio server address</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lmstudio-timeout" class="form-label">Timeout (seconds)</label>
                                        <input type="number" class="form-control" id="lmstudio-timeout" value="60" min="10" max="300">
                                        <small class="form-text text-muted">Request timeout for local models</small>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                <strong>Local Setup:</strong> Make sure LM Studio is running and serving a model on the specified port.
                            </div>
                        </div>

                        <!-- Local Ollama Settings -->
                        <div class="provider-config" id="local_ollama-config" style="display: none;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="ollama-base-url" class="form-label">Ollama URL</label>
                                        <input type="url" class="form-control" id="ollama-base-url" value="http://localhost:11434" placeholder="http://localhost:11434">
                                        <small class="form-text text-muted">Local Ollama server address</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="ollama-timeout" class="form-label">Timeout (seconds)</label>
                                        <input type="number" class="form-control" id="ollama-timeout" value="60" min="10" max="300">
                                        <small class="form-text text-muted">Request timeout for local models</small>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                <strong>Local Setup:</strong> Make sure Ollama is installed and running with your desired models.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Model Parameters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-sliders"></i>
                            Model Parameters
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="temperature" class="form-label">Temperature: <span id="temperature-value">0.7</span></label>
                                    <input type="range" class="form-range" id="temperature" min="0" max="2" step="0.1" value="0.7" oninput="updateRangeValue('temperature')">
                                    <small class="form-text text-muted">Higher = more creative, less predictable</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="top-p" class="form-label">Top P: <span id="top-p-value">0.9</span></label>
                                    <input type="range" class="form-range" id="top-p" min="0" max="1" step="0.05" value="0.9" oninput="updateRangeValue('top-p')">
                                    <small class="form-text text-muted">Nucleus sampling parameter</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="presence-penalty" class="form-label">Presence Penalty: <span id="presence-penalty-value">0.0</span></label>
                                    <input type="range" class="form-range" id="presence-penalty" min="-2" max="2" step="0.1" value="0" oninput="updateRangeValue('presence-penalty')">
                                    <small class="form-text text-muted">Encourages topic diversity</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="system-prompt" class="form-label">System Prompt</label>
                                    <textarea class="form-control" id="system-prompt" rows="4" placeholder="You are a helpful AI assistant for video content analysis and editing suggestions..."></textarea>
                                    <small class="form-text text-muted">Instructions that guide the AI's behavior</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Connection Test -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-wifi"></i>
                            Connection Test
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <p class="mb-1">Test your AI provider connection</p>
                                <small class="text-muted">Verify that your configuration is working correctly</small>
                            </div>
                            <button type="button" class="btn btn-outline-primary" onclick="testAIConnection()">
                                <i class="bi bi-play-circle"></i>
                                Test Connection
                            </button>
                        </div>
                        <div id="connection-test-result" class="mt-3" style="display: none;"></div>
                    </div>
                </div>
            </div>
            
            <!-- DaVinci Resolve Settings -->
            <div class="tab-pane fade" id="resolve-settings">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">DaVinci Resolve Connection</h5>
                    </div>
                    <div class="card-body">
                        <form id="resolve-settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-resolve-integration" checked>
                                            <label class="form-check-label" for="enable-resolve-integration">
                                                Enable DaVinci Resolve Integration
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="resolve-api-path" class="form-label">API Path</label>
                                        <input type="text" class="form-control" id="resolve-api-path" value="/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Modules/">
                                    </div>
                                    <div class="mb-3">
                                        <label for="resolve-timeout" class="form-label">Connection Timeout (seconds)</label>
                                        <input type="number" class="form-control" id="resolve-timeout" min="5" max="60" value="30">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-mock-mode">
                                            <label class="form-check-label" for="enable-mock-mode">
                                                Enable Mock Mode (Development)
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="auto-connect-resolve" checked>
                                            <label class="form-check-label" for="auto-connect-resolve">
                                                Auto-connect on startup
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-audio-extraction" checked>
                                            <label class="form-check-label" for="enable-audio-extraction">
                                                Enable audio extraction from timeline
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Audio Extraction Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="extraction-format" class="form-label">Audio Format</label>
                                    <select class="form-select" id="extraction-format">
                                        <option value="wav" selected>WAV</option>
                                        <option value="mp3">MP3</option>
                                        <option value="flac">FLAC</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="extraction-quality" class="form-label">Audio Quality</label>
                                    <select class="form-select" id="extraction-quality">
                                        <option value="high">High Quality</option>
                                        <option value="medium" selected>Medium Quality</option>
                                        <option value="low">Low Quality</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="temp-directory" class="form-label">Temporary Directory</label>
                                    <input type="text" class="form-control" id="temp-directory" value="/tmp/davinci-ai/">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="delete-after-transcription" checked>
                                        <label class="form-check-label" for="delete-after-transcription">
                                            Delete extracted audio after transcription
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Performance Settings -->
            <div class="tab-pane fade" id="performance-settings">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Performance Settings</h5>
                    </div>
                    <div class="card-body">
                        <form id="performance-settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="max-workers" class="form-label">Max Worker Processes</label>
                                        <input type="number" class="form-control" id="max-workers" min="1" max="16" value="4">
                                    </div>
                                    <div class="mb-3">
                                        <label for="batch-size" class="form-label">Batch Size</label>
                                        <input type="number" class="form-control" id="batch-size" min="1" max="100" value="16">
                                    </div>
                                    <div class="mb-3">
                                        <label for="memory-limit" class="form-label">Memory Limit (GB)</label>
                                        <input type="number" class="form-control" id="memory-limit" min="1" max="64" value="8">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-gpu" checked>
                                            <label class="form-check-label" for="enable-gpu">
                                                Enable GPU Acceleration
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-caching" checked>
                                            <label class="form-check-label" for="enable-caching">
                                                Enable Result Caching
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="cache-size" class="form-label">Cache Size (MB)</label>
                                        <input type="number" class="form-control" id="cache-size" min="100" max="10000" value="1024">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Resource Management</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max-concurrent-transcriptions" class="form-label">Max Concurrent Transcriptions</label>
                                    <input type="number" class="form-control" id="max-concurrent-transcriptions" min="1" max="10" value="2">
                                </div>
                                <div class="mb-3">
                                    <label for="max-concurrent-analyses" class="form-label">Max Concurrent AI Analyses</label>
                                    <input type="number" class="form-control" id="max-concurrent-analyses" min="1" max="10" value="3">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable-compression" checked>
                                        <label class="form-check-label" for="enable-compression">
                                            Enable Result Compression
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable-streaming" checked>
                                        <label class="form-check-label" for="enable-streaming">
                                            Enable Streaming Responses
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Security Settings -->
            <div class="tab-pane fade" id="security-settings">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Security Settings</h5>
                    </div>
                    <div class="card-body">
                        <form id="security-settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-encryption" checked>
                                            <label class="form-check-label" for="enable-encryption">
                                                Enable Data Encryption
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-secure-connections" checked>
                                            <label class="form-check-label" for="enable-secure-connections">
                                                Require Secure Connections (HTTPS)
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="session-timeout" class="form-label">Session Timeout (minutes)</label>
                                        <input type="number" class="form-control" id="session-timeout" min="5" max="1440" value="60">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-rate-limiting" checked>
                                            <label class="form-check-label" for="enable-rate-limiting">
                                                Enable Rate Limiting
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="max-requests-per-minute" class="form-label">Max Requests per Minute</label>
                                        <input type="number" class="form-control" id="max-requests-per-minute" min="10" max="1000" value="100">
                                    </div>
                                    <div class="mb-3">
                                        <label for="max-file-size" class="form-label">Max File Size (MB)</label>
                                        <input type="number" class="form-control" id="max-file-size" min="10" max="10000" value="500">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Privacy Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable-privacy-mode">
                                        <label class="form-check-label" for="enable-privacy-mode">
                                            Enable Privacy Mode (Local Processing Only)
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="auto-delete-data" checked>
                                        <label class="form-check-label" for="auto-delete-data">
                                            Auto-delete processed data after analysis
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="data-retention-days" class="form-label">Data Retention (days)</label>
                                    <input type="number" class="form-control" id="data-retention-days" min="0" max="365" value="7">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="anonymize-data" checked>
                                        <label class="form-check-label" for="anonymize-data">
                                            Anonymize data before processing
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Advanced Settings -->
            <div class="tab-pane fade" id="advanced-settings">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Advanced Settings</h5>
                    </div>
                    <div class="card-body">
                        <form id="advanced-settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log-level" class="form-label">Log Level</label>
                                        <select class="form-select" id="log-level">
                                            <option value="debug">Debug</option>
                                            <option value="info" selected>Info</option>
                                            <option value="warning">Warning</option>
                                            <option value="error">Error</option>
                                            <option value="critical">Critical</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="log-file-path" class="form-label">Log File Path</label>
                                        <input type="text" class="form-control" id="log-file-path" value="logs/davinci-ai.log">
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-debug-mode">
                                            <label class="form-check-label" for="enable-debug-mode">
                                                Enable Debug Mode
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="max-log-size" class="form-label">Max Log File Size (MB)</label>
                                        <input type="number" class="form-control" id="max-log-size" min="1" max="1000" value="100">
                                    </div>
                                    <div class="mb-3">
                                        <label for="log-backup-count" class="form-label">Log Backup Count</label>
                                        <input type="number" class="form-control" id="log-backup-count" min="0" max="100" value="5">
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enable-profiling">
                                            <label class="form-check-label" for="enable-profiling">
                                                Enable Performance Profiling
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">System Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Python Version</label>
                                    <input type="text" class="form-control" value="3.9.7" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Platform</label>
                                    <input type="text" class="form-control" value="macOS-14.0-arm64-arm-64bit" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Available Memory</label>
                                    <input type="text" class="form-control" value="16.0 GB" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">CPU Cores</label>
                                    <input type="text" class="form-control" value="8" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">GPU Available</label>
                                    <input type="text" class="form-control" value="Apple M2 Pro" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Application Version</label>
                                    <input type="text" class="form-control" value="1.0.0" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Settings Actions -->
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <button class="btn btn-primary" onclick="saveSettings()">
                            <i class="bi bi-save"></i>
                            Save Settings
                        </button>
                        <button class="btn btn-outline-secondary" onclick="resetSettings()">
                            <i class="bi bi-arrow-clockwise"></i>
                            Reset to Defaults
                        </button>
                        <button class="btn btn-outline-info" onclick="exportSettings()">
                            <i class="bi bi-download"></i>
                            Export Settings
                        </button>
                        <button class="btn btn-outline-warning" onclick="importSettings()">
                            <i class="bi bi-upload"></i>
                            Import Settings
                        </button>
                    </div>
                    <div>
                        <button class="btn btn-outline-danger" onclick="clearCache()">
                            <i class="bi bi-trash"></i>
                            Clear Cache
                        </button>
                        <button class="btn btn-outline-danger" onclick="clearLogs()">
                            <i class="bi bi-file-earmark-x"></i>
                            Clear Logs
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden file input for import -->
<input type="file" id="import-file-input" accept=".json" style="display: none;" onchange="handleImportFile(this)">
{% endblock %}

{% block extra_js %}
<script>
    let currentSettings = {};
    
    // Load settings on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadSettings();
        checkSystemStatus();
        setupProviderHandlers();
        updateRangeDisplays();
    });
    
    // Setup provider selection handlers
    function setupProviderHandlers() {
        const primaryProvider = document.getElementById('primary-provider');
        const fallbackProvider = document.getElementById('fallback-provider');
        
        if (primaryProvider) {
            primaryProvider.addEventListener('change', function() {
                updateProviderSettings('primary', this.value);
                updateModelOptions('primary-ai-model', this.value);
            });
        }
        
        if (fallbackProvider) {
            fallbackProvider.addEventListener('change', function() {
                updateProviderSettings('fallback', this.value);
                updateModelOptions('fallback-ai-model', this.value);
            });
        }
        
        // Setup range input displays
        document.querySelectorAll('input[type="range"]').forEach(range => {
            range.addEventListener('input', function() {
                updateRangeDisplay(this);
            });
        });
        
        // Setup connection test button
        const testButton = document.getElementById('test-connection');
        if (testButton) {
            testButton.addEventListener('click', testConnection);
        }
    }
    
    // Update provider-specific settings visibility
    function updateProviderSettings(type, provider) {
        const settingsContainer = document.getElementById(`${type}-provider-settings`);
        if (!settingsContainer) return;
        
        // Hide all provider settings
        settingsContainer.querySelectorAll('.provider-settings').forEach(setting => {
            setting.style.display = 'none';
        });
        
        // Show relevant provider settings
        const relevantSetting = settingsContainer.querySelector(`[data-provider="${provider}"]`);
        if (relevantSetting) {
            relevantSetting.style.display = 'block';
        }
    }
    
    // Update model options based on provider
    function updateModelOptions(selectId, provider) {
        const modelSelect = document.getElementById(selectId);
        if (!modelSelect) return;
        
        // Clear existing options
        modelSelect.innerHTML = '';
        
        // Add models based on provider
        const models = getModelsForProvider(provider);
        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.value;
            option.textContent = model.label;
            modelSelect.appendChild(option);
        });
    }
    
    // Get available models for a provider
    function getModelsForProvider(provider) {
        const modelMap = {
            'openai': [
                { value: 'gpt-4o', label: 'GPT-4o' },
                { value: 'gpt-4o-mini', label: 'GPT-4o Mini' },
                { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
                { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' }
            ],
            'anthropic': [
                { value: 'claude-3-5-sonnet-20241022', label: 'Claude 3.5 Sonnet' },
                { value: 'claude-3-5-haiku-20241022', label: 'Claude 3.5 Haiku' },
                { value: 'claude-3-opus-20240229', label: 'Claude 3 Opus' }
            ],
            'google': [
                { value: 'gemini-1.5-pro', label: 'Gemini 1.5 Pro' },
                { value: 'gemini-1.5-flash', label: 'Gemini 1.5 Flash' },
                { value: 'gemini-pro', label: 'Gemini Pro' }
            ],
            'openrouter': [
                { value: 'anthropic/claude-3.5-sonnet', label: 'Claude 3.5 Sonnet (OpenRouter)' },
                { value: 'openai/gpt-4o', label: 'GPT-4o (OpenRouter)' },
                { value: 'meta-llama/llama-3.1-405b-instruct', label: 'Llama 3.1 405B' }
            ],
            'qwen': [
                { value: 'qwen-max', label: 'Qwen Max' },
                { value: 'qwen-plus', label: 'Qwen Plus' },
                { value: 'qwen-turbo', label: 'Qwen Turbo' }
            ],
            'deepseek': [
                { value: 'deepseek-chat', label: 'DeepSeek Chat' },
                { value: 'deepseek-coder', label: 'DeepSeek Coder' }
            ],
            'kimi': [
                { value: 'moonshot-v1-8k', label: 'Kimi K2 8K' },
                { value: 'moonshot-v1-32k', label: 'Kimi K2 32K' },
                { value: 'moonshot-v1-128k', label: 'Kimi K2 128K' }
            ],
            'glm': [
                { value: 'glm-4', label: 'GLM-4' },
                { value: 'glm-4-plus', label: 'GLM-4 Plus' },
                { value: 'glm-4-air', label: 'GLM-4 Air' }
            ],
            'lmstudio': [
                { value: 'local-model', label: 'Local Model (LM Studio)' }
            ],
            'ollama': [
                { value: 'llama3.1', label: 'Llama 3.1' },
                { value: 'mistral', label: 'Mistral' },
                { value: 'codellama', label: 'Code Llama' }
            ]
        };
        
        return modelMap[provider] || [];
    }
    
    // Update range input displays
    function updateRangeDisplays() {
        document.querySelectorAll('input[type="range"]').forEach(range => {
            updateRangeDisplay(range);
        });
    }
    
    function updateRangeDisplay(rangeInput) {
        const display = rangeInput.parentElement.querySelector('.range-value');
        if (display) {
            display.textContent = rangeInput.value;
        }
    }
    
    // Test connection to selected provider
    function testConnection() {
        const primaryProvider = document.getElementById('primary-provider').value;
        const testButton = document.getElementById('test-connection');
        const originalText = testButton.textContent;
        
        testButton.disabled = true;
        testButton.textContent = 'Testing...';
        
        // Get provider-specific settings
        const settings = getProviderSettings(primaryProvider);
        
        fetch('/api/test-provider-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                provider: primaryProvider,
                settings: settings
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Connection successful!', 'success');
                testButton.className = 'btn btn-success';
                testButton.textContent = 'Connected ✓';
            } else {
                showToast(`Connection failed: ${data.error}`, 'error');
                testButton.className = 'btn btn-danger';
                testButton.textContent = 'Failed ✗';
            }
        })
        .catch(error => {
            console.error('Connection test error:', error);
            showToast('Connection test failed', 'error');
            testButton.className = 'btn btn-danger';
            testButton.textContent = 'Error ✗';
        })
        .finally(() => {
            testButton.disabled = false;
            setTimeout(() => {
                testButton.className = 'btn btn-outline-primary';
                testButton.textContent = originalText;
            }, 3000);
        });
    }
    
    // Get provider-specific settings
    function getProviderSettings(provider) {
        const settings = {};
        
        switch (provider) {
            case 'openrouter':
                settings.api_key = document.getElementById('openrouter-api-key')?.value;
                settings.base_url = document.getElementById('openrouter-base-url')?.value;
                break;
            case 'qwen':
                settings.api_key = document.getElementById('qwen-api-key')?.value;
                settings.base_url = document.getElementById('qwen-base-url')?.value;
                break;
            case 'deepseek':
                settings.api_key = document.getElementById('deepseek-api-key')?.value;
                settings.base_url = document.getElementById('deepseek-base-url')?.value;
                break;
            case 'kimi':
                settings.api_key = document.getElementById('kimi-api-key')?.value;
                settings.base_url = document.getElementById('kimi-base-url')?.value;
                break;
            case 'glm':
                settings.api_key = document.getElementById('glm-api-key')?.value;
                settings.base_url = document.getElementById('glm-base-url')?.value;
                break;
            case 'lmstudio':
                settings.base_url = document.getElementById('lmstudio-base-url')?.value;
                settings.model_path = document.getElementById('lmstudio-model-path')?.value;
                break;
            case 'ollama':
                settings.base_url = document.getElementById('ollama-base-url')?.value;
                settings.model_name = document.getElementById('ollama-model-name')?.value;
                break;
            default:
                // For standard providers (OpenAI, Anthropic, Google)
                settings.api_key = document.getElementById(`${provider}-api-key`)?.value;
                break;
        }
        
        return settings;
    }
    
    // Load current settings
    function loadSettings() {
        fetch('/api/get-config')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentSettings = data.config;
                    populateSettings(data.config);
                } else {
                    showToast('Failed to load settings', 'error');
                }
            })
            .catch(error => {
                console.error('Error loading settings:', error);
                showToast('Error loading settings', 'error');
            });
    }
    
    // Populate form fields with current settings
    function populateSettings(config) {
        // General settings
        document.getElementById('app-name').value = config.app_name || 'DaVinci Resolve AI Assistant';
        document.getElementById('theme-select').value = config.theme || 'light';
        document.getElementById('language-select').value = config.language || 'en';
        document.getElementById('auto-save-interval').value = config.auto_save_interval || 5;
        document.getElementById('check-updates').checked = config.check_updates !== false;
        document.getElementById('send-analytics').checked = config.send_analytics !== false;
        
        // Transcription settings
        if (config.transcription) {
            document.getElementById('transcription-model').value = config.transcription.model || 'faster-whisper';
            document.getElementById('whisper-model-size').value = config.transcription.model_size || 'medium';
            document.getElementById('audio-language').value = config.transcription.language || 'auto';
            document.getElementById('enable-vad').checked = config.transcription.enable_vad !== false;
            document.getElementById('enable-speaker-diarization').checked = config.transcription.enable_speaker_diarization || false;
            document.getElementById('enable-timestamps').checked = config.transcription.enable_timestamps !== false;
            document.getElementById('beam-size').value = config.transcription.beam_size || 5;
            document.getElementById('sample-rate').value = config.transcription.sample_rate || 16000;
            document.getElementById('audio-format').value = config.transcription.audio_format || 'wav';
            document.getElementById('normalize-audio').checked = config.transcription.normalize_audio !== false;
            document.getElementById('remove-noise').checked = config.transcription.remove_noise !== false;
            document.getElementById('trim-silence').checked = config.transcription.trim_silence !== false;
        }
        
        // AI settings
        if (config.ai) {
            // Set providers
            document.getElementById('primary-provider').value = config.ai.primary_provider || 'openai';
            document.getElementById('fallback-provider').value = config.ai.fallback_provider || 'openai';
            
            // Update provider settings and models
            updateProviderSettings('primary-provider', config.ai.primary_provider || 'openai');
            updateProviderSettings('fallback-provider', config.ai.fallback_provider || 'openai');
            
            document.getElementById('primary-ai-model').value = config.ai.primary_model || 'gpt-4';
            document.getElementById('fallback-ai-model').value = config.ai.fallback_model || 'gpt-3.5-turbo';
            document.getElementById('max-tokens').value = config.ai.max_tokens || 2048;
            document.getElementById('temperature').value = config.ai.temperature || 0.7;
            document.getElementById('top-p').value = config.ai.top_p || 0.9;
            document.getElementById('presence-penalty').value = config.ai.presence_penalty || 0;
            document.getElementById('system-prompt').value = config.ai.system_prompt || '';
            
            // Update range displays
            updateRangeDisplays();
            
            // API keys
            if (config.ai.api_keys) {
                const apiKeyElements = {
                    'openai-api-key': config.ai.api_keys.openai,
                    'anthropic-api-key': config.ai.api_keys.anthropic,
                    'google-api-key': config.ai.api_keys.google,
                    'openrouter-api-key': config.ai.api_keys.openrouter,
                    'qwen-api-key': config.ai.api_keys.qwen,
                    'deepseek-api-key': config.ai.api_keys.deepseek,
                    'kimi-api-key': config.ai.api_keys.kimi,
                    'glm-api-key': config.ai.api_keys.glm
                };
                
                Object.entries(apiKeyElements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) element.value = value || '';
                });
            }
            
            // Base URLs
            if (config.ai.base_urls) {
                const baseUrlElements = {
                    'openrouter-base-url': config.ai.base_urls.openrouter,
                    'qwen-base-url': config.ai.base_urls.qwen,
                    'deepseek-base-url': config.ai.base_urls.deepseek,
                    'kimi-base-url': config.ai.base_urls.kimi,
                    'glm-base-url': config.ai.base_urls.glm,
                    'lmstudio-base-url': config.ai.base_urls.lmstudio,
                    'ollama-base-url': config.ai.base_urls.ollama
                };
                
                Object.entries(baseUrlElements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) element.value = value || '';
                });
            }
            
            // Local settings
            if (config.ai.local_settings) {
                const localElements = {
                    'lmstudio-model-path': config.ai.local_settings.lmstudio_model_path,
                    'ollama-model-name': config.ai.local_settings.ollama_model_name
                };
                
                Object.entries(localElements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) element.value = value || '';
                });
            }
        }
        
        // DaVinci Resolve settings
        if (config.resolve) {
            document.getElementById('enable-resolve-integration').checked = config.resolve.enabled !== false;
            document.getElementById('resolve-api-path').value = config.resolve.api_path || '/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Modules/';
            document.getElementById('resolve-timeout').value = config.resolve.timeout || 30;
            document.getElementById('enable-mock-mode').checked = config.resolve.enable_mock || false;
            document.getElementById('auto-connect-resolve').checked = config.resolve.auto_connect !== false;
            document.getElementById('enable-audio-extraction').checked = config.resolve.enable_audio_extraction !== false;
            document.getElementById('extraction-format').value = config.resolve.extraction_format || 'wav';
            document.getElementById('extraction-quality').value = config.resolve.extraction_quality || 'medium';
            document.getElementById('temp-directory').value = config.resolve.temp_directory || '/tmp/davinci-ai/';
            document.getElementById('delete-after-transcription').checked = config.resolve.delete_after_transcription !== false;
        }
        
        // Performance settings
        if (config.performance) {
            document.getElementById('max-workers').value = config.performance.max_workers || 4;
            document.getElementById('batch-size').value = config.performance.batch_size || 16;
            document.getElementById('memory-limit').value = config.performance.memory_limit || 8;
            document.getElementById('enable-gpu').checked = config.performance.enable_gpu !== false;
            document.getElementById('enable-caching').checked = config.performance.enable_caching !== false;
            document.getElementById('cache-size').value = config.performance.cache_size || 1024;
            document.getElementById('max-concurrent-transcriptions').value = config.performance.max_concurrent_transcriptions || 2;
            document.getElementById('max-concurrent-analyses').value = config.performance.max_concurrent_analyses || 3;
            document.getElementById('enable-compression').checked = config.performance.enable_compression !== false;
            document.getElementById('enable-streaming').checked = config.performance.enable_streaming !== false;
        }
        
        // Security settings
        if (config.security) {
            document.getElementById('enable-encryption').checked = config.security.enable_encryption !== false;
            document.getElementById('enable-secure-connections').checked = config.security.enable_secure_connections !== false;
            document.getElementById('session-timeout').value = config.security.session_timeout || 60;
            document.getElementById('enable-rate-limiting').checked = config.security.enable_rate_limiting !== false;
            document.getElementById('max-requests-per-minute').value = config.security.max_requests_per_minute || 100;
            document.getElementById('max-file-size').value = config.security.max_file_size || 500;
            document.getElementById('enable-privacy-mode').checked = config.security.enable_privacy_mode || false;
            document.getElementById('auto-delete-data').checked = config.security.auto_delete_data !== false;
            document.getElementById('data-retention-days').value = config.security.data_retention_days || 7;
            document.getElementById('anonymize-data').checked = config.security.anonymize_data !== false;
        }
        
        // Advanced settings
        if (config.advanced) {
            document.getElementById('log-level').value = config.advanced.log_level || 'info';
            document.getElementById('log-file-path').value = config.advanced.log_file_path || 'logs/davinci-ai.log';
            document.getElementById('enable-debug-mode').checked = config.advanced.enable_debug_mode || false;
            document.getElementById('max-log-size').value = config.advanced.max_log_size || 100;
            document.getElementById('log-backup-count').value = config.advanced.log_backup_count || 5;
            document.getElementById('enable-profiling').checked = config.advanced.enable_profiling || false;
        }
    }
    
    // Save settings
    function saveSettings() {
        const newSettings = collectSettings();
        
        fetch('/api/update-config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(newSettings)
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentSettings = newSettings;
                    showToast('Settings saved successfully', 'success');
                } else {
                    showToast('Failed to save settings: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error saving settings:', error);
                showToast('Error saving settings', 'error');
            });
    }
    
    // Collect settings from form
    function collectSettings() {
        return {
            app_name: document.getElementById('app-name').value,
            theme: document.getElementById('theme-select').value,
            language: document.getElementById('language-select').value,
            auto_save_interval: parseInt(document.getElementById('auto-save-interval').value),
            check_updates: document.getElementById('check-updates').checked,
            send_analytics: document.getElementById('send-analytics').checked,
            
            transcription: {
                model: document.getElementById('transcription-model').value,
                model_size: document.getElementById('whisper-model-size').value,
                language: document.getElementById('audio-language').value,
                enable_vad: document.getElementById('enable-vad').checked,
                enable_speaker_diarization: document.getElementById('enable-speaker-diarization').checked,
                enable_timestamps: document.getElementById('enable-timestamps').checked,
                beam_size: parseInt(document.getElementById('beam-size').value),
                sample_rate: parseInt(document.getElementById('sample-rate').value),
                audio_format: document.getElementById('audio-format').value,
                normalize_audio: document.getElementById('normalize-audio').checked,
                remove_noise: document.getElementById('remove-noise').checked,
                trim_silence: document.getElementById('trim-silence').checked
            },
            
            ai: {
                primary_provider: document.getElementById('primary-provider').value,
                fallback_provider: document.getElementById('fallback-provider').value,
                primary_model: document.getElementById('primary-ai-model').value,
                fallback_model: document.getElementById('fallback-ai-model').value,
                max_tokens: parseInt(document.getElementById('max-tokens').value),
                temperature: parseFloat(document.getElementById('temperature').value),
                top_p: parseFloat(document.getElementById('top-p').value),
                presence_penalty: parseFloat(document.getElementById('presence-penalty').value),
                system_prompt: document.getElementById('system-prompt').value,
                api_keys: {
                    openai: document.getElementById('openai-api-key')?.value || '',
                    anthropic: document.getElementById('anthropic-api-key')?.value || '',
                    google: document.getElementById('google-api-key')?.value || '',
                    openrouter: document.getElementById('openrouter-api-key')?.value || '',
                    qwen: document.getElementById('qwen-api-key')?.value || '',
                    deepseek: document.getElementById('deepseek-api-key')?.value || '',
                    kimi: document.getElementById('kimi-api-key')?.value || '',
                    glm: document.getElementById('glm-api-key')?.value || ''
                },
                base_urls: {
                    openrouter: document.getElementById('openrouter-base-url')?.value || '',
                    qwen: document.getElementById('qwen-base-url')?.value || '',
                    deepseek: document.getElementById('deepseek-base-url')?.value || '',
                    kimi: document.getElementById('kimi-base-url')?.value || '',
                    glm: document.getElementById('glm-base-url')?.value || '',
                    lmstudio: document.getElementById('lmstudio-base-url')?.value || '',
                    ollama: document.getElementById('ollama-base-url')?.value || ''
                },
                local_settings: {
                    lmstudio_model_path: document.getElementById('lmstudio-model-path')?.value || '',
                    ollama_model_name: document.getElementById('ollama-model-name')?.value || ''
                }
            },
            
            resolve: {
                enabled: document.getElementById('enable-resolve-integration').checked,
                api_path: document.getElementById('resolve-api-path').value,
                timeout: parseInt(document.getElementById('resolve-timeout').value),
                enable_mock: document.getElementById('enable-mock-mode').checked,
                auto_connect: document.getElementById('auto-connect-resolve').checked,
                enable_audio_extraction: document.getElementById('enable-audio-extraction').checked,
                extraction_format: document.getElementById('extraction-format').value,
                extraction_quality: document.getElementById('extraction-quality').value,
                temp_directory: document.getElementById('temp-directory').value,
                delete_after_transcription: document.getElementById('delete-after-transcription').checked
            },
            
            performance: {
                max_workers: parseInt(document.getElementById('max-workers').value),
                batch_size: parseInt(document.getElementById('batch-size').value),
                memory_limit: parseInt(document.getElementById('memory-limit').value),
                enable_gpu: document.getElementById('enable-gpu').checked,
                enable_caching: document.getElementById('enable-caching').checked,
                cache_size: parseInt(document.getElementById('cache-size').value),
                max_concurrent_transcriptions: parseInt(document.getElementById('max-concurrent-transcriptions').value),
                max_concurrent_analyses: parseInt(document.getElementById('max-concurrent-analyses').value),
                enable_compression: document.getElementById('enable-compression').checked,
                enable_streaming: document.getElementById('enable-streaming').checked
            },
            
            security: {
                enable_encryption: document.getElementById('enable-encryption').checked,
                enable_secure_connections: document.getElementById('enable-secure-connections').checked,
                session_timeout: parseInt(document.getElementById('session-timeout').value),
                enable_rate_limiting: document.getElementById('enable-rate-limiting').checked,
                max_requests_per_minute: parseInt(document.getElementById('max-requests-per-minute').value),
                max_file_size: parseInt(document.getElementById('max-file-size').value),
                enable_privacy_mode: document.getElementById('enable-privacy-mode').checked,
                auto_delete_data: document.getElementById('auto-delete-data').checked,
                data_retention_days: parseInt(document.getElementById('data-retention-days').value),
                anonymize_data: document.getElementById('anonymize-data').checked
            },
            
            advanced: {
                log_level: document.getElementById('log-level').value,
                log_file_path: document.getElementById('log-file-path').value,
                enable_debug_mode: document.getElementById('enable-debug-mode').checked,
                max_log_size: parseInt(document.getElementById('max-log-size').value),
                log_backup_count: parseInt(document.getElementById('log-backup-count').value),
                enable_profiling: document.getElementById('enable-profiling').checked
            }
        };
    }
    
    // Reset settings to defaults
    function resetSettings() {
        if (confirm('Are you sure you want to reset all settings to their default values?')) {
            fetch('/api/reset-config', {
                method: 'POST'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadSettings();
                        showToast('Settings reset to defaults', 'success');
                    } else {
                        showToast('Failed to reset settings', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error resetting settings:', error);
                    showToast('Error resetting settings', 'error');
                });
        }
    }
    
    // Export settings
    function exportSettings() {
        const settingsJson = JSON.stringify(currentSettings, null, 2);
        const blob = new Blob([settingsJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'davinci-ai-settings.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        showToast('Settings exported successfully', 'success');
    }
    
    // Import settings
    function importSettings() {
        document.getElementById('import-file-input').click();
    }
    
    function handleImportFile(input) {
        const file = input.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedSettings = JSON.parse(e.target.result);
                    
                    fetch('/api/update-config', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(importedSettings)
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                currentSettings = importedSettings;
                                populateSettings(importedSettings);
                                showToast('Settings imported successfully', 'success');
                            } else {
                                showToast('Failed to import settings: ' + data.error, 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error importing settings:', error);
                            showToast('Error importing settings', 'error');
                        });
                } catch (error) {
                    showToast('Invalid settings file format', 'error');
                }
            };
            reader.readAsText(file);
        }
    }
    
    // Clear cache
    function clearCache() {
        if (confirm('Are you sure you want to clear the cache? This will remove all cached results.')) {
            fetch('/api/clear-cache', {
                method: 'POST'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('Cache cleared successfully', 'success');
                    } else {
                        showToast('Failed to clear cache', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error clearing cache:', error);
                    showToast('Error clearing cache', 'error');
                });
        }
    }
    
    // Clear logs
    function clearLogs() {
        if (confirm('Are you sure you want to clear all log files?')) {
            fetch('/api/clear-logs', {
                method: 'POST'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('Logs cleared successfully', 'success');
                    } else {
                        showToast('Failed to clear logs', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error clearing logs:', error);
                    showToast('Error clearing logs', 'error');
                });
        }
    }
    
    // Toggle password visibility
    function togglePassword(inputId) {
        const input = document.getElementById(inputId);
        const button = input.nextElementSibling;
        
        if (input.type === 'password') {
            input.type = 'text';
            button.innerHTML = '<i class="bi bi-eye-slash"></i>';
        } else {
            input.type = 'password';
            button.innerHTML = '<i class="bi bi-eye"></i>';
        }
    }
    
    // Check system status
    function checkSystemStatus() {
        fetch('/api/system-status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatusIndicators(data.status);
                }
            })
            .catch(error => {
                console.error('Error checking system status:', error);
            });
    }
    
    function updateStatusIndicators(status) {
        // Update status badges
        const statusElements = {
            'api-keys-status': status.api_keys,
            'resolve-status': status.resolve,
            'ffmpeg-status': status.ffmpeg,
            'ai-models-status': status.ai_models
        };
        
        Object.entries(statusElements).forEach(([elementId, status]) => {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = status.text;
                element.className = `badge bg-${status.color}`;
            }
        });
    }
</script>
{% endblock %}