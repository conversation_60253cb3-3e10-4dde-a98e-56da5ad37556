{% extends "base.html" %}

{% block title %}AI Analysis - DaVinci Resolve AI Assistant{% endblock %}
{% block page_title %}AI Content Analysis{% endblock %}

{% block content %}
<div class="row">
    <!-- Analysis Input -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-text-left"></i>
                    Content to Analyze
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <textarea class="form-control" id="content-text" rows="8" placeholder="Paste your transcribed content here for analysis..."></textarea>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <span id="char-count">0</span> characters
                    </small>
                    <button class="btn btn-primary btn-gradient" onclick="loadFromTranscription()">
                        <i class="bi bi-arrow-up-circle"></i>
                        Load from Transcription
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Analysis Options -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-sliders"></i>
                    Analysis Options
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Analysis Type</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sentiment-analysis" checked>
                                <label class="form-check-label" for="sentiment-analysis">
                                    Sentiment Analysis
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="topic-extraction" checked>
                                <label class="form-check-label" for="topic-extraction">
                                    Topic Extraction
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="keyword-analysis" checked>
                                <label class="form-check-label" for="keyword-analysis">
                                    Keyword Analysis
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="summary-generation" checked>
                                <label class="form-check-label" for="summary-generation">
                                    Summary Generation
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">AI Model</label>
                            <select class="form-select" id="ai-model-select">
                                <option value="gpt-4">GPT-4 (Recommended)</option>
                                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                <option value="claude">Claude</option>
                                <option value="local">Local Model</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="analysis-prompt" class="form-label">Custom Prompt (Optional)</label>
                            <textarea class="form-control" id="analysis-prompt" rows="3" placeholder="Add custom instructions for the AI analysis..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Analysis Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-robot"></i>
                    Analysis Controls
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary btn-gradient" onclick="startAnalysis()" id="start-analysis-btn">
                        <i class="bi bi-play"></i>
                        Start Analysis
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearResults()" id="clear-results-btn" disabled>
                        <i class="bi bi-trash"></i>
                        Clear Results
                    </button>
                    <button class="btn btn-outline-success" onclick="exportAnalysis()" id="export-analysis-btn" disabled>
                        <i class="bi bi-download"></i>
                        Export Analysis
                    </button>
                </div>
                
                <div id="analysis-progress" style="display: none;" class="mt-3">
                    <div class="progress-bar-custom mb-2">
                        <div class="progress-bar-fill" id="analysis-progress-bar"></div>
                    </div>
                    <small class="text-muted" id="analysis-status">Preparing analysis...</small>
                </div>
            </div>
        </div>
        
        <!-- Quick Templates -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i>
                    Quick Templates
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="loadTemplate('interview')">
                        <i class="bi bi-person-lines-fill"></i>
                        Interview Analysis
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="loadTemplate('podcast')">
                        <i class="bi bi-broadcast"></i>
                        Podcast Analysis
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="loadTemplate('meeting')">
                        <i class="bi bi-people"></i>
                        Meeting Analysis
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="loadTemplate('content')">
                        <i class="bi bi-camera-video"></i>
                        Video Content Analysis
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analysis Results -->
<div class="row mt-4" id="analysis-results" style="display: none;">
    <!-- Sentiment Analysis -->
    <div class="col-lg-6 mb-4" id="sentiment-results" style="display: none;">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-emoji-smile"></i>
                    Sentiment Analysis
                </h5>
            </div>
            <div class="card-body">
                <div id="sentiment-content">
                    <!-- Sentiment results will appear here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Topic Extraction -->
    <div class="col-lg-6 mb-4" id="topic-results" style="display: none;">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-tags"></i>
                    Topic Extraction
                </h5>
            </div>
            <div class="card-body">
                <div id="topic-content">
                    <!-- Topic results will appear here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Keyword Analysis -->
    <div class="col-lg-6 mb-4" id="keyword-results" style="display: none;">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-key"></i>
                    Keyword Analysis
                </h5>
            </div>
            <div class="card-body">
                <div id="keyword-content">
                    <!-- Keyword results will appear here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Summary Generation -->
    <div class="col-lg-6 mb-4" id="summary-results" style="display: none;">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-file-text"></i>
                    Summary Generation
                </h5>
            </div>
            <div class="card-body">
                <div id="summary-content">
                    <!-- Summary results will appear here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Raw AI Response -->
    <div class="col-12" id="raw-results" style="display: none;">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-code"></i>
                    Raw AI Response
                </h5>
            </div>
            <div class="card-body">
                <div id="raw-content" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                    <!-- Raw response will appear here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .sentiment-positive {
        color: var(--success-color);
    }
    
    .sentiment-negative {
        color: var(--danger-color);
    }
    
    .sentiment-neutral {
        color: var(--secondary-color);
    }
    
    .topic-tag {
        display: inline-block;
        background-color: var(--primary-color);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.85em;
        margin: 2px;
    }
    
    .keyword-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #dee2e6;
    }
    
    .keyword-item:last-child {
        border-bottom: none;
    }
    
    .keyword-text {
        font-weight: 500;
    }
    
    .keyword-score {
        background-color: var(--primary-color);
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 0.8em;
    }
    
    .analysis-metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #dee2e6;
    }
    
    .analysis-metric:last-child {
        border-bottom: none;
    }
    
    .metric-label {
        font-weight: 500;
        color: var(--secondary-color);
    }
    
    .metric-value {
        font-weight: 600;
        color: var(--dark-color);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let currentAnalysisId = null;
    
    // Character counter
    document.getElementById('content-text').addEventListener('input', function() {
        const charCount = this.value.length;
        document.getElementById('char-count').textContent = charCount;
    });
    
    // Load content from transcription
    function loadFromTranscription() {
        // In a real implementation, this would fetch the latest transcription
        showToast('Loading latest transcription...', 'info');
        
        // Simulate loading transcription
        setTimeout(() => {
            const sampleTranscription = `Speaker 1: Welcome to our podcast about artificial intelligence and its impact on video editing.

Speaker 2: Thank you for having me. AI has revolutionized the way we approach content creation, making complex tasks more accessible to creators of all skill levels.

Speaker 1: Absolutely. What are some of the key developments you've seen in AI-powered video editing?

Speaker 2: Well, automated transcription is a big one. Tools like DaVinci Resolve now integrate AI that can transcribe audio in real-time, making the editing process much more efficient.

Speaker 1: That's fascinating. How accurate are these AI transcription services?

Speaker 2: Modern AI models can achieve accuracy rates above 95% for clear audio, which is quite impressive. The technology continues to improve with each iteration.`;
            
            document.getElementById('content-text').value = sampleTranscription;
            document.getElementById('char-count').textContent = sampleTranscription.length;
            showToast('Transcription loaded successfully', 'success');
        }, 1000);
    }
    
    // Load analysis template
    function loadTemplate(templateType) {
        const templates = {
            interview: {
                prompt: "Analyze this interview transcript for key insights, main themes, speaker dynamics, and notable quotes. Focus on the interviewee's expertise and main points discussed.",
                analysis: ["sentiment-analysis", "topic-extraction", "keyword-analysis", "summary-generation"]
            },
            podcast: {
                prompt: "Analyze this podcast episode for engaging moments, key discussion points, audience appeal, and potential social media clips. Identify the most compelling segments.",
                analysis: ["sentiment-analysis", "topic-extraction", "keyword-analysis", "summary-generation"]
            },
            meeting: {
                prompt: "Analyze this meeting transcript for action items, decisions made, key discussion points, and follow-up requirements. Focus on practical outcomes and next steps.",
                analysis: ["topic-extraction", "keyword-analysis", "summary-generation"]
            },
            content: {
                prompt: "Analyze this video content for engagement factors, key messages, audience retention points, and optimization suggestions for better viewer experience.",
                analysis: ["sentiment-analysis", "topic-extraction", "keyword-analysis", "summary-generation"]
            }
        };
        
        const template = templates[templateType];
        if (template) {
            document.getElementById('analysis-prompt').value = template.prompt;
            
            // Check appropriate analysis types
            document.querySelectorAll('input[type="checkbox"][id$="-analysis"]').forEach(checkbox => {
                checkbox.checked = template.analysis.includes(checkbox.id);
            });
            
            showToast(`Loaded ${templateType} template`, 'success');
        }
    }
    
    // Start AI analysis
    function startAnalysis() {
        const content = document.getElementById('content-text').value.trim();
        if (!content) {
            showToast('Please enter content to analyze', 'error');
            return;
        }
        
        const analysisTypes = [];
        document.querySelectorAll('input[type="checkbox"][id$="-analysis"]:checked').forEach(checkbox => {
            analysisTypes.push(checkbox.id.replace('-analysis', ''));
        });
        
        if (analysisTypes.length === 0) {
            showToast('Please select at least one analysis type', 'error');
            return;
        }
        
        const progress = document.getElementById('analysis-progress');
        const progressBar = document.getElementById('analysis-progress-bar');
        const status = document.getElementById('analysis-status');
        
        // Show progress
        progress.style.display = 'block';
        
        // Disable buttons
        document.getElementById('start-analysis-btn').disabled = true;
        document.getElementById('clear-results-btn').disabled = true;
        document.getElementById('export-analysis-btn').disabled = true;
        
        // Simulate progress
        let progressValue = 0;
        const progressInterval = setInterval(() => {
            progressValue += 3;
            progressBar.style.width = progressValue + '%';
            
            if (progressValue < 25) {
                status.textContent = 'Preparing analysis...';
            } else if (progressValue < 50) {
                status.textContent = 'Processing content...';
            } else if (progressValue < 75) {
                status.textContent = 'Generating insights...';
            } else {
                status.textContent = 'Finalizing results...';
            }
            
            if (progressValue >= 100) {
                clearInterval(progressInterval);
            }
        }, 200);
        
        // Prepare request data
        const requestData = {
            content: content,
            analysis_types: analysisTypes,
            model: document.getElementById('ai-model-select').value,
            custom_prompt: document.getElementById('analysis-prompt').value
        };
        
        fetch('/api/analyze-content', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                if (data.success) {
                    status.textContent = 'Analysis completed!';
                    currentAnalysisId = data.analysis_id;
                    displayAnalysisResults(data);
                    
                    // Enable buttons
                    document.getElementById('clear-results-btn').disabled = false;
                    document.getElementById('export-analysis-btn').disabled = false;
                    
                    showToast('AI analysis completed successfully!', 'success');
                } else {
                    status.textContent = 'Analysis failed';
                    showToast('Analysis failed: ' + data.error, 'error');
                }
            })
            .catch(error => {
                clearInterval(progressInterval);
                console.error('Error during analysis:', error);
                status.textContent = 'Error during analysis';
                showToast('Error during analysis', 'error');
            })
            .finally(() => {
                document.getElementById('start-analysis-btn').disabled = false;
                setTimeout(() => {
                    progress.style.display = 'none';
                }, 2000);
            });
    }
    
    // Display analysis results
    function displayAnalysisResults(data) {
        const resultsContainer = document.getElementById('analysis-results');
        resultsContainer.style.display = 'flex';
        
        // Display sentiment analysis
        if (data.sentiment_analysis) {
            displaySentimentAnalysis(data.sentiment_analysis);
        }
        
        // Display topic extraction
        if (data.topic_extraction) {
            displayTopicExtraction(data.topic_extraction);
        }
        
        // Display keyword analysis
        if (data.keyword_analysis) {
            displayKeywordAnalysis(data.keyword_analysis);
        }
        
        // Display summary generation
        if (data.summary_generation) {
            displaySummaryGeneration(data.summary_generation);
        }
        
        // Display raw response
        if (data.raw_response) {
            displayRawResponse(data.raw_response);
        }
    }
    
    function displaySentimentAnalysis(sentiment) {
        const container = document.getElementById('sentiment-results');
        const content = document.getElementById('sentiment-content');
        
        let html = '';
        
        // Overall sentiment
        if (sentiment.overall) {
            const sentimentClass = sentiment.overall.toLowerCase().includes('positive') ? 'sentiment-positive' :
                                 sentiment.overall.toLowerCase().includes('negative') ? 'sentiment-negative' :
                                 'sentiment-neutral';
            
            html += `
                <div class="analysis-metric">
                    <span class="metric-label">Overall Sentiment:</span>
                    <span class="metric-value ${sentimentClass}">${sentiment.overall}</span>
                </div>
            `;
        }
        
        // Sentiment scores
        if (sentiment.scores) {
            Object.entries(sentiment.scores).forEach(([emotion, score]) => {
                html += `
                    <div class="analysis-metric">
                        <span class="metric-label">${emotion.charAt(0).toUpperCase() + emotion.slice(1)}:</span>
                        <span class="metric-value">${(score * 100).toFixed(1)}%</span>
                    </div>
                `;
            });
        }
        
        content.innerHTML = html;
        container.style.display = 'block';
    }
    
    function displayTopicExtraction(topics) {
        const container = document.getElementById('topic-results');
        const content = document.getElementById('topic-content');
        
        let html = '';
        
        if (Array.isArray(topics)) {
            topics.forEach(topic => {
                html += `<span class="topic-tag">${topic}</span>`;
            });
        } else if (typeof topics === 'object') {
            Object.entries(topics).forEach(([topic, score]) => {
                html += `<span class="topic-tag">${topic} (${(score * 100).toFixed(0)}%)</span>`;
            });
        }
        
        content.innerHTML = html || '<p class="text-muted">No topics extracted</p>';
        container.style.display = 'block';
    }
    
    function displayKeywordAnalysis(keywords) {
        const container = document.getElementById('keyword-results');
        const content = document.getElementById('keyword-content');
        
        let html = '';
        
        if (Array.isArray(keywords)) {
            keywords.forEach(keyword => {
                html += `
                    <div class="keyword-item">
                        <span class="keyword-text">${keyword.word || keyword}</span>
                        ${keyword.score ? `<span class="keyword-score">${(keyword.score * 100).toFixed(0)}%</span>` : ''}
                    </div>
                `;
            });
        }
        
        content.innerHTML = html || '<p class="text-muted">No keywords extracted</p>';
        container.style.display = 'block';
    }
    
    function displaySummaryGeneration(summary) {
        const container = document.getElementById('summary-results');
        const content = document.getElementById('summary-content');
        
        let html = '';
        
        if (typeof summary === 'string') {
            html = `<p>${summary}</p>`;
        } else if (summary.summary) {
            html = `<p>${summary.summary}</p>`;
            
            if (summary.key_points && summary.key_points.length > 0) {
                html += '<h6>Key Points:</h6><ul>';
                summary.key_points.forEach(point => {
                    html += `<li>${point}</li>`;
                });
                html += '</ul>';
            }
        }
        
        content.innerHTML = html || '<p class="text-muted">No summary generated</p>';
        container.style.display = 'block';
    }
    
    function displayRawResponse(rawResponse) {
        const container = document.getElementById('raw-results');
        const content = document.getElementById('raw-content');
        
        content.innerHTML = `<pre class="mb-0">${JSON.stringify(rawResponse, null, 2)}</pre>`;
        container.style.display = 'block';
    }
    
    // Clear results
    function clearResults() {
        currentAnalysisId = null;
        document.getElementById('analysis-results').style.display = 'none';
        
        // Hide all result sections
        ['sentiment-results', 'topic-results', 'keyword-results', 'summary-results', 'raw-results'].forEach(id => {
            document.getElementById(id).style.display = 'none';
        });
        
        // Clear content
        ['sentiment-content', 'topic-content', 'keyword-content', 'summary-content', 'raw-content'].forEach(id => {
            document.getElementById(id).innerHTML = '';
        });
        
        document.getElementById('clear-results-btn').disabled = true;
        document.getElementById('export-analysis-btn').disabled = true;
        showToast('Analysis results cleared', 'info');
    }
    
    // Export analysis
    function exportAnalysis() {
        if (!currentAnalysisId) {
            showToast('No analysis to export', 'error');
            return;
        }
        
        window.open(`/api/export-analysis/${currentAnalysisId}`, '_blank');
        showToast('Analysis exported', 'success');
    }
</script>
{% endblock %}