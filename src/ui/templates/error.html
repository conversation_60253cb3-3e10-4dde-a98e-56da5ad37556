{% extends "base.html" %}

{% block title %}Error - <PERSON><PERSON><PERSON><PERSON> Resolve AI Assistant{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                    Error Occurred
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-danger" role="alert">
                    <h5 class="alert-heading">Something went wrong!</h5>
                    <p class="mb-0">{{ error }}</p>
                </div>
                
                <div class="mt-4">
                    <h6>What you can do:</h6>
                    <ul>
                        <li>Check the application logs for more details</li>
                        <li>Try refreshing the page</li>
                        <li>Return to the <a href="{{ url_for('index') }}" class="text-decoration-none">dashboard</a></li>
                        <li>Check the <a href="{{ url_for('settings') }}" class="text-decoration-none">settings</a> page</li>
                    </ul>
                </div>
                
                <div class="mt-4 d-flex gap-2">
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="bi bi-house-door"></i>
                        Go to Dashboard
                    </a>
                    <button onclick="history.back()" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Go Back
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh after 10 seconds if on settings page
    if (window.location.pathname.includes('settings')) {
        setTimeout(function() {
            window.location.href = '{{ url_for("settings") }}';
        }, 10000);
    }
</script>
{% endblock %}