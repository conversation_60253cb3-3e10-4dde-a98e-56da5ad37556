{% extends "base.html" %}

{% block title %}Dashboard - Da<PERSON><PERSON><PERSON> Resolve AI Assistant{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<div class="row">
    <!-- Status Cards -->
    <div class="col-md-6 mb-4">
        <div class="card status-card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-film"></i>
                    DaVinci Resolve Status
                </h5>
                <div class="d-flex align-items-center mb-3">
                    <div class="status-indicator status-warning" id="resolve-status"></div>
                    <span id="resolve-status-text">Checking connection...</span>
                </div>
                <div id="resolve-info" class="small" style="display: none;">
                    <p class="mb-1"><strong>Project:</strong> <span id="current-project">-</span></p>
                    <p class="mb-1"><strong>Timeline:</strong> <span id="current-timeline">-</span></p>
                    <p class="mb-0"><strong>Duration:</strong> <span id="timeline-duration">-</span></p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card bg-light">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-activity"></i>
                    Quick Actions
                </h5>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary btn-gradient" onclick="refreshResolveInfo()">
                        <i class="bi bi-arrow-clockwise"></i>
                        Refresh Project Info
                    </button>
                    <button class="btn btn-outline-primary" onclick="extractCurrentAudio()">
                        <i class="bi bi-soundwave"></i>
                        Extract Audio from Current Timeline
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Overview -->
<div class="row mt-4">
    <div class="col-12">
        <h4 class="mb-4">Features</h4>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="feature-icon mx-auto">
                    <i class="bi bi-mic"></i>
                </div>
                <h5 class="card-title">Audio Transcription</h5>
                <p class="card-text">Extract and transcribe audio from DaVinci Resolve timelines using advanced AI models.</p>
                <a href="{{ url_for('transcription') }}" class="btn btn-primary btn-gradient">Start Transcribing</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="feature-icon mx-auto">
                    <i class="bi bi-robot"></i>
                </div>
                <h5 class="card-title">AI Content Analysis</h5>
                <p class="card-text">Analyze transcribed content for themes, sentiment, and actionable insights.</p>
                <a href="{{ url_for('ai_analysis') }}" class="btn btn-primary btn-gradient">Analyze Content</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <div class="feature-icon mx-auto">
                    <i class="bi bi-gear"></i>
                </div>
                <h5 class="card-title">Smart Configuration</h5>
                <p class="card-text">Configure AI models, transcription settings, and DaVinci Resolve integration.</p>
                <a href="{{ url_for('settings') }}" class="btn btn-primary btn-gradient">Configure</a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row mt-5">
    <div class="col-12">
        <h4 class="mb-4">Recent Activity</h4>
        <div class="card">
            <div class="card-body">
                <div id="activity-log">
                    <p class="text-muted text-center">No recent activity</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update DaVinci Resolve status
    function updateResolveStatus() {
        fetch('/api/resolve-status')
            .then(response => response.json())
            .then(data => {
                const statusIndicator = document.getElementById('resolve-status');
                const statusText = document.getElementById('resolve-status-text');
                const resolveInfo = document.getElementById('resolve-info');
                
                if (data.connected) {
                    statusIndicator.className = 'status-indicator status-connected';
                    statusText.textContent = 'Connected';
                    resolveInfo.style.display = 'block';
                    
                    // Update project info
                    if (data.project) {
                        document.getElementById('current-project').textContent = data.project.name || 'Unknown';
                    }
                    if (data.timeline) {
                        document.getElementById('current-timeline').textContent = data.timeline.name || 'Unknown';
                        document.getElementById('timeline-duration').textContent = data.timeline.duration || 'Unknown';
                    }
                } else {
                    statusIndicator.className = 'status-indicator status-disconnected';
                    statusText.textContent = 'Disconnected';
                    resolveInfo.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error checking Resolve status:', error);
                const statusIndicator = document.getElementById('resolve-status');
                const statusText = document.getElementById('resolve-status-text');
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'Connection Error';
            });
    }
    
    // Refresh Resolve info
    function refreshResolveInfo() {
        fetch('/api/refresh-resolve-info', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Project information refreshed', 'success');
                    updateResolveStatus();
                } else {
                    showToast('Failed to refresh project info: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error refreshing Resolve info:', error);
                showToast('Error refreshing project info', 'error');
            });
    }
    
    // Extract audio from current timeline
    function extractCurrentAudio() {
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> Extracting...';
        button.disabled = true;
        
        fetch('/api/extract-audio', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Audio extracted successfully!', 'success');
                    addActivityLog('Audio extracted from current timeline');
                } else {
                    showToast('Failed to extract audio: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error extracting audio:', error);
                showToast('Error extracting audio', 'error');
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
    }
    
    // Add activity to log
    function addActivityLog(message) {
        const activityLog = document.getElementById('activity-log');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'mb-2 pb-2 border-bottom';
        logEntry.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>${message}</span>
                <small class="text-muted">${timestamp}</small>
            </div>
        `;
        
        // Remove placeholder text if present
        if (activityLog.querySelector('.text-muted')) {
            activityLog.innerHTML = '';
        }
        
        // Add new entry at the top
        activityLog.insertBefore(logEntry, activityLog.firstChild);
        
        // Keep only last 10 entries
        while (activityLog.children.length > 10) {
            activityLog.removeChild(activityLog.lastChild);
        }
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        updateResolveStatus();
        
        // Update status every 10 seconds
        setInterval(updateResolveStatus, 10000);
    });
</script>
{% endblock %}