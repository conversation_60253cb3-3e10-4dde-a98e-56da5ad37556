<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}DaVinci Resolve AI Assistant{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --dark-color: #212529;
            --light-color: #f8f9fa;
        }
        
        body {
            background-color: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--dark-color) 0%, #2c3e50 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: #ffffff;
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
        }
        
        .sidebar .nav-link i {
            margin-right: 10px;
            width: 16px;
            text-align: center;
        }
        
        .main-content {
            padding: 20px;
            background-color: #ffffff;
            margin: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        
        .status-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
            color: white;
            border: none;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(13, 110, 253, 0.3);
        }
        
        .status-card .card-title {
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-connected { background-color: var(--success-color); }
        .status-disconnected { background-color: var(--danger-color); }
        .status-warning { background-color: var(--warning-color); }
        
        .btn-gradient {
            background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
            border: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(13, 110, 253, 0.4);
        }
        
        .upload-area {
            border: 2px dashed var(--primary-color);
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background-color: rgba(13, 110, 253, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            background-color: rgba(13, 110, 253, 0.1);
            border-color: #0056b3;
        }
        
        .upload-area.dragover {
            background-color: rgba(13, 110, 253, 0.15);
            border-color: #0056b3;
        }
        
        .progress-bar-custom {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
            overflow: hidden;
        }
        
        .progress-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color) 0%, #0056b3 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
        }
        
        .custom-toast {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-left: 4px solid var(--primary-color);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.3rem;
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }
            .main-content {
                margin: 10px;
                padding: 15px;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <i class="bi bi-film"></i>
                            AI Assistant
                        </h4>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                            <i class="bi bi-house-door"></i>
                            Dashboard
                        </a>
                        <a class="nav-link {% if request.endpoint == 'transcription' %}active{% endif %}" href="{{ url_for('transcription') }}">
                            <i class="bi bi-mic"></i>
                            Transcription
                        </a>
                        <a class="nav-link {% if request.endpoint == 'ai_analysis' %}active{% endif %}" href="{{ url_for('ai_analysis') }}">
                            <i class="bi bi-robot"></i>
                            AI Analysis
                        </a>
                        <a class="nav-link {% if request.endpoint == 'settings' %}active{% endif %}" href="{{ url_for('settings') }}">
                            <i class="bi bi-gear"></i>
                            Settings
                        </a>
                    </nav>
                    
                    <hr class="text-white-50">
                    
                    <div class="mt-auto">
                        <small class="text-white-50">
                            <i class="bi bi-info-circle"></i>
                            DaVinci Resolve AI Assistant
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Top Navigation -->
                <nav class="navbar navbar-light bg-white shadow-sm rounded mb-4">
                    <div class="container-fluid">
                        <span class="navbar-brand text-primary">
                            {% block page_title %}Dashboard{% endblock %}
                        </span>
                        <div class="d-flex align-items-center">
                            <div class="status-indicator status-warning" id="connection-status"></div>
                            <small class="text-muted">DaVinci Resolve</small>
                        </div>
                    </div>
                </nav>
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <!-- Main Content -->
                <main class="main-content">
                    {% block content %}{% endblock %}
                </main>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Global functions
        function showToast(message, type = 'info') {
            const toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.innerHTML = `
                <div class="toast custom-toast" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header">
                        <strong class="me-auto">DaVinci AI Assistant</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;
            
            document.body.appendChild(toastContainer);
            const toast = new bootstrap.Toast(toastContainer.querySelector('.toast'));
            toast.show();
            
            // Remove toast container after it's hidden
            toastContainer.querySelector('.toast').addEventListener('hidden.bs.toast', function() {
                document.body.removeChild(toastContainer);
            });
        }
        
        function updateConnectionStatus() {
            fetch('/api/resolve-status')
                .then(response => response.json())
                .then(data => {
                    const statusIndicator = document.getElementById('connection-status');
                    if (data.connected) {
                        statusIndicator.className = 'status-indicator status-connected';
                    } else {
                        statusIndicator.className = 'status-indicator status-disconnected';
                    }
                })
                .catch(error => {
                    console.error('Error checking connection status:', error);
                    document.getElementById('connection-status').className = 'status-indicator status-disconnected';
                });
        }
        
        // Update connection status every 5 seconds
        setInterval(updateConnectionStatus, 5000);
        
        // Initial status check
        document.addEventListener('DOMContentLoaded', function() {
            updateConnectionStatus();
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>