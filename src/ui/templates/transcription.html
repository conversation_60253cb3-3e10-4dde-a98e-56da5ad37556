{% extends "base.html" %}

{% block title %}Transcription - <PERSON><PERSON><PERSON><PERSON> Resolve AI Assistant{% endblock %}
{% block page_title %}Audio Transcription{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <!-- Upload Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-upload"></i>
                    Upload Audio File
                </h5>
            </div>
            <div class="card-body">
                <div class="upload-area" id="upload-area">
                    <i class="bi bi-cloud-arrow-up display-4 text-primary mb-3"></i>
                    <h5>Drag & Drop Audio File Here</h5>
                    <p class="text-muted">or click to browse</p>
                    <input type="file" id="audio-file" accept="audio/*" style="display: none;">
                    <button class="btn btn-primary btn-gradient" onclick="document.getElementById('audio-file').click()">
                        <i class="bi bi-folder2-open"></i>
                        Choose File
                    </button>
                </div>
                
                <div id="file-info" style="display: none;" class="mt-3">
                    <div class="alert alert-info">
                        <strong>Selected File:</strong> <span id="selected-file-name"></span>
                        <br><strong>Size:</strong> <span id="selected-file-size"></span>
                        <br><strong>Duration:</strong> <span id="selected-file-duration">Calculating...</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Extract from DaVinci Resolve -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-film"></i>
                    Extract from DaVinci Resolve
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Extract audio from the current timeline in DaVinci Resolve.</p>
                <button class="btn btn-primary btn-gradient" onclick="extractFromResolve()" id="extract-btn">
                    <i class="bi bi-soundwave"></i>
                    Extract Audio from Current Timeline
                </button>
                <div id="extract-progress" style="display: none;" class="mt-3">
                    <div class="progress-bar-custom">
                        <div class="progress-bar-fill" id="extract-progress-bar"></div>
                    </div>
                    <small class="text-muted" id="extract-status">Extracting audio...</small>
                </div>
            </div>
        </div>
        
        <!-- Transcription Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i>
                    Transcription Settings
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="model-select" class="form-label">AI Model</label>
                            <select class="form-select" id="model-select">
                                <option value="faster-whisper">faster-whisper (Default)</option>
                                <option value="whisper">OpenAI Whisper</option>
                                <option value="azure">Azure Speech</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="language-select" class="form-label">Language</label>
                            <select class="form-select" id="language-select">
                                <option value="auto">Auto-detect</option>
                                <option value="en">English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="de">German</option>
                                <option value="it">Italian</option>
                                <option value="pt">Portuguese</option>
                                <option value="ru">Russian</option>
                                <option value="zh">Chinese</option>
                                <option value="ja">Japanese</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="speaker-diarization">
                                <label class="form-check-label" for="speaker-diarization">
                                    Speaker Diarization
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="profanity-filter" checked>
                                <label class="form-check-label" for="profanity-filter">
                                    Filter Profanity
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Transcription Progress -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-hourglass-split"></i>
                    Transcription Progress
                </h5>
            </div>
            <div class="card-body">
                <div id="transcription-progress" style="display: none;">
                    <div class="progress-bar-custom mb-2">
                        <div class="progress-bar-fill" id="transcription-progress-bar"></div>
                    </div>
                    <small class="text-muted" id="transcription-status">Initializing...</small>
                    <div class="mt-3" id="transcription-stats"></div>
                </div>
                <div id="transcription-ready" class="text-center">
                    <i class="bi bi-mic display-4 text-muted mb-3"></i>
                    <p class="text-muted">Ready to transcribe audio</p>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary btn-gradient" onclick="startTranscription()" id="start-transcription-btn" disabled>
                        <i class="bi bi-play"></i>
                        Start Transcription
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearTranscription()" id="clear-transcription-btn" disabled>
                        <i class="bi bi-trash"></i>
                        Clear Results
                    </button>
                    <button class="btn btn-outline-success" onclick="exportTranscription()" id="export-transcription-btn" disabled>
                        <i class="bi bi-download"></i>
                        Export Results
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transcription Results -->
<div class="row mt-4" id="transcription-results" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-file-text"></i>
                    Transcription Results
                </h5>
                <div>
                    <span class="badge bg-primary" id="confidence-badge">--</span>
                    <span class="badge bg-secondary" id="duration-badge">--</span>
                </div>
            </div>
            <div class="card-body">
                <div id="transcription-text" class="transcription-content" style="max-height: 400px; overflow-y: auto;">
                    <!-- Transcription text will appear here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .transcription-content {
        font-family: 'Courier New', monospace;
        line-height: 1.6;
        white-space: pre-wrap;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }
    
    .speaker-label {
        color: var(--primary-color);
        font-weight: bold;
        margin-right: 8px;
    }
    
    .timestamp {
        color: var(--secondary-color);
        font-size: 0.85em;
        margin-right: 8px;
    }
    
    .confidence-low {
        background-color: rgba(255, 193, 7, 0.2);
        border-bottom: 1px wavy #ffc107;
    }
    
    .confidence-high {
        background-color: rgba(25, 135, 84, 0.1);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let currentAudioFile = null;
    let currentTranscriptionId = null;
    
    // File upload handling
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('audio-file');
    
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type.startsWith('audio/')) {
            handleAudioFile(files[0]);
        } else {
            showToast('Please drop an audio file', 'error');
        }
    });
    
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleAudioFile(e.target.files[0]);
        }
    });
    
    function handleAudioFile(file) {
        currentAudioFile = file;
        
        // Display file info
        document.getElementById('selected-file-name').textContent = file.name;
        document.getElementById('selected-file-size').textContent = formatFileSize(file.size);
        document.getElementById('file-info').style.display = 'block';
        
        // Enable transcription button
        document.getElementById('start-transcription-btn').disabled = false;
        
        // Calculate duration using Web Audio API
        const audio = new Audio();
        audio.src = URL.createObjectURL(file);
        audio.addEventListener('loadedmetadata', () => {
            const duration = Math.floor(audio.duration);
            document.getElementById('selected-file-duration').textContent = formatDuration(duration);
            URL.revokeObjectURL(audio.src);
        });
        
        showToast('Audio file selected successfully', 'success');
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    function formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
    
    // Extract audio from DaVinci Resolve
    function extractFromResolve() {
        const button = document.getElementById('extract-btn');
        const progress = document.getElementById('extract-progress');
        const progressBar = document.getElementById('extract-progress-bar');
        const status = document.getElementById('extract-status');
        
        button.disabled = true;
        progress.style.display = 'block';
        
        // Simulate progress
        let progressValue = 0;
        const progressInterval = setInterval(() => {
            progressValue += 5;
            progressBar.style.width = progressValue + '%';
            if (progressValue >= 100) {
                clearInterval(progressInterval);
            }
        }, 200);
        
        fetch('/api/extract-audio', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                if (data.success) {
                    status.textContent = 'Audio extracted successfully!';
                    showToast('Audio extracted from DaVinci Resolve', 'success');
                    
                    // Create a file object from the extracted audio
                    // In a real implementation, you would handle the file data
                    currentAudioFile = new File(["extracted_audio"], "timeline_audio.wav", { type: 'audio/wav' });
                    document.getElementById('start-transcription-btn').disabled = false;
                    
                    setTimeout(() => {
                        progress.style.display = 'none';
                        button.disabled = false;
                    }, 2000);
                } else {
                    status.textContent = 'Failed to extract audio';
                    showToast('Failed to extract audio: ' + data.error, 'error');
                    button.disabled = false;
                }
            })
            .catch(error => {
                clearInterval(progressInterval);
                console.error('Error extracting audio:', error);
                status.textContent = 'Error extracting audio';
                showToast('Error extracting audio', 'error');
                button.disabled = false;
            });
    }
    
    // Start transcription
    function startTranscription() {
        if (!currentAudioFile) {
            showToast('Please select an audio file first', 'error');
            return;
        }
        
        const formData = new FormData();
        formData.append('audio', currentAudioFile);
        formData.append('model', document.getElementById('model-select').value);
        formData.append('language', document.getElementById('language-select').value);
        formData.append('speaker_diarization', document.getElementById('speaker-diarization').checked);
        formData.append('profanity_filter', document.getElementById('profanity-filter').checked);
        
        const progress = document.getElementById('transcription-progress');
        const progressBar = document.getElementById('transcription-progress-bar');
        const status = document.getElementById('transcription-status');
        const stats = document.getElementById('transcription-stats');
        const ready = document.getElementById('transcription-ready');
        const results = document.getElementById('transcription-results');
        
        // Show progress, hide ready state
        progress.style.display = 'block';
        ready.style.display = 'none';
        results.style.display = 'none';
        
        // Disable buttons
        document.getElementById('start-transcription-btn').disabled = true;
        document.getElementById('clear-transcription-btn').disabled = true;
        document.getElementById('export-transcription-btn').disabled = true;
        
        // Simulate transcription progress
        let progressValue = 0;
        const progressInterval = setInterval(() => {
            progressValue += 2;
            progressBar.style.width = progressValue + '%';
            
            if (progressValue < 30) {
                status.textContent = 'Initializing transcription...';
            } else if (progressValue < 70) {
                status.textContent = 'Processing audio...';
            } else if (progressValue < 90) {
                status.textContent = 'Generating transcript...';
            } else {
                status.textContent = 'Finalizing results...';
            }
            
            if (progressValue >= 100) {
                clearInterval(progressInterval);
            }
        }, 300);
        
        fetch('/api/transcribe', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                if (data.success) {
                    status.textContent = 'Transcription completed!';
                    currentTranscriptionId = data.transcription_id;
                    
                    // Display results
                    displayTranscriptionResults(data);
                    
                    // Enable buttons
                    document.getElementById('clear-transcription-btn').disabled = false;
                    document.getElementById('export-transcription-btn').disabled = false;
                    
                    showToast('Transcription completed successfully!', 'success');
                } else {
                    status.textContent = 'Transcription failed';
                    showToast('Transcription failed: ' + data.error, 'error');
                }
            })
            .catch(error => {
                clearInterval(progressInterval);
                console.error('Error during transcription:', error);
                status.textContent = 'Error during transcription';
                showToast('Error during transcription', 'error');
            })
            .finally(() => {
                document.getElementById('start-transcription-btn').disabled = false;
                setTimeout(() => {
                    progress.style.display = 'none';
                }, 2000);
            });
    }
    
    // Display transcription results
    function displayTranscriptionResults(data) {
        const results = document.getElementById('transcription-results');
        const transcriptionText = document.getElementById('transcription-text');
        const confidenceBadge = document.getElementById('confidence-badge');
        const durationBadge = document.getElementById('duration-badge');
        
        // Format transcription text
        let formattedText = '';
        if (data.segments) {
            data.segments.forEach(segment => {
                const timestamp = formatTimestamp(segment.start);
                const speaker = segment.speaker ? `<span class="speaker-label">Speaker ${segment.speaker}:</span>` : '';
                const confidence = segment.confidence < 0.8 ? 'confidence-low' : 'confidence-high';
                
                formattedText += `<span class="timestamp">[${timestamp}]</span> ${speaker}<span class="${confidence}">${segment.text}</span>\n\n`;
            });
        } else if (data.text) {
            formattedText = data.text;
        }
        
        transcriptionText.innerHTML = formattedText;
        
        // Update badges
        if (data.confidence) {
            confidenceBadge.textContent = `Confidence: ${(data.confidence * 100).toFixed(1)}%`;
        }
        if (data.duration) {
            durationBadge.textContent = `Duration: ${formatDuration(data.duration)}`;
        }
        
        results.style.display = 'block';
    }
    
    function formatTimestamp(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    // Clear transcription
    function clearTranscription() {
        currentTranscriptionId = null;
        document.getElementById('transcription-results').style.display = 'none';
        document.getElementById('transcription-text').innerHTML = '';
        document.getElementById('clear-transcription-btn').disabled = true;
        document.getElementById('export-transcription-btn').disabled = true;
        showToast('Transcription cleared', 'info');
    }
    
    // Export transcription
    function exportTranscription() {
        if (!currentTranscriptionId) {
            showToast('No transcription to export', 'error');
            return;
        }
        
        window.open(`/api/export-transcription/${currentTranscriptionId}`, '_blank');
        showToast('Transcription exported', 'success');
    }
</script>
{% endblock %}