#!/usr/bin/env python3
"""
DaVinci Resolve Auto-Caption Workflow Example

This script demonstrates how to use DaVinci Resolve's caption scripting APIs
to automate caption import, export, and management tasks.

Requirements:
- DaVinci Resolve installed
- Python 3.6+ or Python 2.7
- Proper environment variables set (see resolve_scripting_guide.md)
"""

import os
import sys
import json
import time
from datetime import datetime

# Add the Resolve scripting API to Python path
try:
    import DaVinciResolveScript as dvr_script
except ImportError:
    print("Error: Unable to import DaVinciResolveScript")
    print("Please ensure Resolve scripting environment is properly configured")
    print("See docs/resolve_scripting_guide.md for setup instructions")
    sys.exit(1)


class ResolveCaptionManager:
    """Manages caption operations in DaVinci Resolve"""
    
    def __init__(self):
        """Initialize Resolve connection and get project objects"""
        self.resolve = None
        self.project_manager = None
        self.project = None
        self.timeline = None
        self.media_pool = None
        
        self._connect_to_resolve()
    
    def _connect_to_resolve(self):
        """Establish connection to DaVinci Resolve"""
        try:
            # Get Resolve application object
            self.resolve = dvr_script.scriptapp("Resolve")
            if not self.resolve:
                raise Exception("Failed to connect to DaVinci Resolve")
            
            # Get project manager
            self.project_manager = self.resolve.GetProjectManager()
            if not self.project_manager:
                raise Exception("Failed to get project manager")
            
            # Get current project
            self.project = self.project_manager.GetCurrentProject()
            if not self.project:
                raise Exception("No active project found")
            
            # Get current timeline
            self.timeline = self.project.GetCurrentTimeline()
            if not self.timeline:
                raise Exception("No active timeline found")
            
            # Get media pool
            self.media_pool = self.project.GetMediaPool()
            
            print(f"✓ Connected to Resolve: Project '{self.project.GetName()}'")
            print(f"✓ Active timeline: '{self.timeline.GetName()}'")
            
        except Exception as e:
            print(f"✗ Connection failed: {e}")
            raise
    
    def get_caption_info(self):
        """Get information about caption tracks in current timeline"""
        try:
            caption_count = self.timeline.GetCaptionsCount()
            print(f"Caption tracks: {caption_count}")
            
            if caption_count > 0:
                captions = self.timeline.GetCaptions()
                for i, caption in enumerate(captions):
                    print(f"  Track {i+1}: {caption}")
                
                current_caption = self.timeline.GetCurrentCaption()
                print(f"  Current caption: {current_caption}")
            
            return caption_count
            
        except Exception as e:
            print(f"Error getting caption info: {e}")
            return 0
    
    def import_captions(self, caption_file, offset=0):
        """Import captions from a file"""
        try:
            # Validate file exists
            if not os.path.exists(caption_file):
                print(f"✗ Caption file not found: {caption_file}")
                return False
            
            # Get file info
            file_size = os.path.getsize(caption_file)
            print(f"Importing captions from: {caption_file}")
            print(f"File size: {file_size} bytes, Offset: {offset} frames")
            
            start_time = time.time()
            
            # Import captions
            result = self.timeline.ImportCaptions(caption_file, offset)
            
            duration = time.time() - start_time
            
            if result:
                print(f"✓ Successfully imported captions in {duration:.2f} seconds")
                
                # Verify import
                new_count = self.timeline.GetCaptionsCount()
                print(f"  Current caption tracks: {new_count}")
                
                return True
            else:
                print(f"✗ Failed to import captions")
                return False
                
        except Exception as e:
            print(f"✗ Import error: {e}")
            return False
    
    def export_captions(self, export_path, format_type="srt", caption_index=1):
        """Export captions to a file"""
        try:
            # Validate caption index
            caption_count = self.timeline.GetCaptionsCount()
            if caption_count == 0:
                print("✗ No caption tracks available for export")
                return False
            
            if caption_index > caption_count:
                print(f"✗ Caption index {caption_index} exceeds available tracks ({caption_count})")
                return False
            
            # Ensure export directory exists
            export_dir = os.path.dirname(export_path)
            if export_dir and not os.path.exists(export_dir):
                os.makedirs(export_dir)
            
            print(f"Exporting caption track {caption_index} to: {export_path}")
            print(f"Format: {format_type}")
            
            start_time = time.time()
            
            # Export captions
            result = self.timeline.ExportCaptions(export_path, format_type, caption_index)
            
            duration = time.time() - start_time
            
            if result:
                if os.path.exists(export_path):
                    file_size = os.path.getsize(export_path)
                    print(f"✓ Successfully exported captions in {duration:.2f} seconds")
                    print(f"  File size: {file_size} bytes")
                else:
                    print(f"✓ Export completed but file not found at: {export_path}")
                return True
            else:
                print(f"✗ Failed to export captions")
                return False
                
        except Exception as e:
            print(f"✗ Export error: {e}")
            return False
    
    def batch_import_captions(self, caption_folder, file_extension=".srt", offset=0):
        """Batch import caption files from a folder"""
        try:
            if not os.path.exists(caption_folder):
                print(f"✗ Caption folder not found: {caption_folder}")
                return False
            
            # Find all caption files
            caption_files = []
            for file in os.listdir(caption_folder):
                if file.endswith(file_extension):
                    full_path = os.path.join(caption_folder, file)
                    caption_files.append(full_path)
            
            if not caption_files:
                print(f"✗ No caption files found with extension {file_extension}")
                return False
            
            print(f"Found {len(caption_files)} caption files")
            
            # Import each file
            success_count = 0
            for caption_file in caption_files:
                print(f"\nProcessing: {os.path.basename(caption_file)}")
                if self.import_captions(caption_file, offset):
                    success_count += 1
            
            print(f"\n✓ Batch import completed: {success_count}/{len(caption_files)} successful")
            return success_count > 0
            
        except Exception as e:
            print(f"✗ Batch import error: {e}")
            return False
    
    def create_timestamped_export(self, format_type="srt", caption_index=1):
        """Create timestamped caption export"""
        try:
            # Generate timestamped filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            timeline_name = self.timeline.GetName()
            
            # Clean timeline name for filename
            safe_timeline_name = "".join(c for c in timeline_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            
            export_filename = f"{safe_timeline_name}_captions_{timestamp}.{format_type}"
            export_path = os.path.join(os.path.expanduser("~/Desktop"), export_filename)
            
            return self.export_captions(export_path, format_type, caption_index)
            
        except Exception as e:
            print(f"✗ Timestamped export error: {e}")
            return False


def main():
    """Main function demonstrating caption workflow"""
    print("=== DaVinci Resolve Caption Manager ===\n")
    
    try:
        # Initialize caption manager
        manager = ResolveCaptionManager()
        
        # Display current caption information
        print("\n--- Current Caption Status ---")
        manager.get_caption_info()
        
        # Example workflow (comment/uncomment as needed)
        
        # Example 1: Import captions
        # caption_file = "/path/to/your/captions.srt"
        # manager.import_captions(caption_file, offset=0)
        
        # Example 2: Export current captions
        # manager.create_timestamped_export(format_type="srt", caption_index=1)
        
        # Example 3: Batch import from folder
        # caption_folder = "/path/to/caption/folder"
        # manager.batch_import_captions(caption_folder, file_extension=".srt")
        
        # Example 4: Custom export
        # custom_export = "/Users/<USER>/Desktop/my_captions.vtt"
        # manager.export_captions(custom_export, format_type="vtt", caption_index=1)
        
        print("\n--- Workflow Complete ---")
        
    except KeyboardInterrupt:
        print("\n✗ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n✗ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Display usage information
    print("DaVinci Resolve Auto-Caption Script")
    print("====================================")
    print("This script demonstrates caption import/export functionality.")
    print("Edit the main() function to customize the workflow.\n")
    
    # Check if running in headless mode
    if len(sys.argv) > 1 and sys.argv[1] == "--headless":
        print("Running in headless mode")
    
    main()