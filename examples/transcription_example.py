#!/usr/bin/env python3
"""
Example usage of the DaVinci Resolve AI Assistant transcription engine.

This script demonstrates how to use the faster-whisper based transcription
engine for optimal performance on Apple Silicon Macs.
"""

import sys
import time
from pathlib import Path
from loguru import logger

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from transcription import TranscriptionEngine, TranscriptionConfig, TranscriptionModel


def setup_logging():
    """Setup logging for the example."""
    logger.remove()  # Remove default handler
    logger.add(
        sys.stderr,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )


def example_basic_transcription():
    """Basic transcription example."""
    logger.info("=== Basic Transcription Example ===")
    
    # Create configuration
    config = TranscriptionConfig(
        model_size=TranscriptionModel.BASE,  # Use base model for speed
        language="auto",  # Auto-detect language
        device="auto",  # Auto-select best device (MPS on Apple Silicon)
        compute_type="int8",  # Use int8 for faster processing
        optimize_for_apple_silicon=True,  # Enable Apple Silicon optimizations
    )
    
    # Initialize engine
    engine = TranscriptionEngine(config)
    
    # Example audio file (replace with your actual audio file)
    audio_file = Path("path/to/your/audio/file.wav")
    
    if not audio_file.exists():
        logger.warning(f"Example audio file not found: {audio_file}")
        logger.info("Please provide a valid audio file path to test transcription")
        return
    
    # Perform transcription
    logger.info(f"Transcribing: {audio_file.name}")
    result = engine.transcribe_audio(audio_file)
    
    if result:
        logger.success("Transcription completed!")
        logger.info(f"Text: {result.text}")
        logger.info(f"Language: {result.language}")
        logger.info(f"Confidence: {result.confidence:.2f}")
        logger.info(f"Processing time: {result.processing_time:.2f}s")
        
        # Show segments if word timestamps are enabled
        if result.segments and len(result.segments) > 1:
            logger.info(f"Segments: {len(result.segments)}")
            for i, segment in enumerate(result.segments[:3]):  # Show first 3 segments
                logger.info(f"  Segment {i+1}: [{segment['start']:.1f}s - {segment['end']:.1f}s] {segment['text']}")
    else:
        logger.error("Transcription failed")
    
    # Cleanup
    engine.cleanup()


def example_apple_silicon_optimization():
    """Apple Silicon optimization example."""
    logger.info("=== Apple Silicon Optimization Example ===")
    
    # Configuration optimized for Apple Silicon
    config = TranscriptionConfig(
        model_size=TranscriptionModel.SMALL,  # Small model for balance of speed/accuracy
        device="mps",  # Force Metal Performance Shaders
        compute_type="float16",  # Use float16 on MPS for better performance
        use_metal=True,
        optimize_for_apple_silicon=True,
        beam_size=3,  # Smaller beam for faster processing
        best_of=3,
        temperature=0.0,
    )
    
    engine = TranscriptionEngine(config)
    
    # Check device info
    model_info = engine.get_model_info()
    logger.info(f"Model info: {model_info}")
    
    # Example would continue with actual audio file...
    logger.info("Configuration optimized for Apple Silicon")


def example_with_fallback():
    """Example with fallback model."""
    logger.info("=== Fallback Model Example ===")
    
    config = TranscriptionConfig(
        model_size=TranscriptionModel.MEDIUM,  # Primary model
        fallback_model=TranscriptionModel.BASE,  # Fallback if primary fails
        max_retries=2,
    )
    
    engine = TranscriptionEngine(config)
    
    # This would use the fallback mechanism if primary model fails
    logger.info("Fallback model configured for reliability")


def example_video_audio_extraction():
    """Example of extracting audio from video."""
    logger.info("=== Video Audio Extraction Example ===")
    
    config = TranscriptionConfig()
    engine = TranscriptionEngine(config)
    
    # Example video file
    video_file = Path("path/to/your/video.mp4")
    
    if video_file.exists():
        from transcription import AudioProcessor
        
        audio_processor = AudioProcessor(config)
        
        # Extract audio from video
        logger.info(f"Extracting audio from: {video_file.name}")
        audio_file = audio_processor.extract_audio_from_video(video_file)
        
        if audio_file:
            logger.success(f"Audio extracted to: {audio_file}")
            
            # Get audio info
            info = audio_processor.get_audio_info(audio_file)
            if info:
                logger.info(f"Audio info: {info}")
            
            # Now transcribe the extracted audio
            result = engine.transcribe_audio(audio_file)
            
            if result:
                logger.success("Video transcription completed!")
                logger.info(f"Transcribed text: {result.text}")
        else:
            logger.error("Audio extraction failed")
    else:
        logger.warning(f"Example video file not found: {video_file}")


def example_batch_transcription():
    """Example of batch transcription."""
    logger.info("=== Batch Transcription Example ===")
    
    config = TranscriptionConfig(
        model_size=TranscriptionModel.BASE,
        optimize_for_apple_silicon=True,
    )
    
    engine = TranscriptionEngine(config)
    
    # Example audio files
    audio_files = [
        Path("audio1.wav"),
        Path("audio2.mp3"),
        Path("audio3.m4a"),
    ]
    
    # Filter existing files
    existing_files = [f for f in audio_files if f.exists()]
    
    if not existing_files:
        logger.warning("No audio files found for batch processing")
        return
    
    logger.info(f"Processing {len(existing_files)} audio files")
    
    results = []
    total_time = 0
    
    for i, audio_file in enumerate(existing_files, 1):
        logger.info(f"Processing file {i}/{len(existing_files)}: {audio_file.name}")
        
        start_time = time.time()
        result = engine.transcribe_audio(audio_file)
        processing_time = time.time() - start_time
        
        if result:
            results.append({
                "file": audio_file.name,
                "text": result.text,
                "language": result.language,
                "confidence": result.confidence,
                "processing_time": processing_time
            })
            total_time += processing_time
            logger.success(f"✓ Transcribed: {audio_file.name}")
        else:
            logger.error(f"✗ Failed: {audio_file.name}")
    
    # Summary
    logger.info(f"=== Batch Processing Summary ===")
    logger.info(f"Total files processed: {len(results)}/{len(existing_files)}")
    logger.info(f"Total processing time: {total_time:.2f}s")
    logger.info(f"Average time per file: {total_time/len(results) if results else 0:.2f}s")
    
    for result in results:
        logger.info(f"  {result['file']}: {len(result['text'])} chars, "
                   f"lang: {result['language']}, "
                   f"confidence: {result['confidence']:.2f}, "
                   f"time: {result['processing_time']:.2f}s")


def main():
    """Main function to run examples."""
    setup_logging()
    
    logger.info("DaVinci Resolve AI Assistant - Transcription Examples")
    logger.info("=" * 60)
    
    # Run examples (comment/uncomment as needed)
    try:
        example_basic_transcription()
        # example_apple_silicon_optimization()
        # example_with_fallback()
        # example_video_audio_extraction()
        # example_batch_transcription()
        
    except KeyboardInterrupt:
        logger.info("Examples interrupted by user")
    except Exception as e:
        logger.error(f"Example failed: {e}")
        raise
    
    logger.info("Examples completed!")


if __name__ == "__main__":
    main()