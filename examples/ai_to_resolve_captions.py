#!/usr/bin/env python3
"""
AI Transcription to DaVinci Resolve Captions Integration

This example demonstrates a complete workflow for:
1. Extracting audio from a Resolve timeline
2. Generating AI transcription
3. Converting to SRT format
4. Importing captions back into Resolve

This bridges the AI transcription capabilities of this project with
DaVinci Resolve's caption management system.
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path
from datetime import datetime

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import DaVinciResolveScript as dvr_script
except ImportError:
    print("Error: Unable to import DaVinciResolveScript")
    print("Please ensure Resolve scripting environment is properly configured")
    print("See docs/resolve_scripting_guide.md for setup instructions")
    sys.exit(1)

# Import project modules
from utils.srt_generator import SRTGenerator
from src.transcription.transcription_engine import TranscriptionEngine
from src.transcription.audio_processor import AudioProcessor


class AIResolveCaptionWorkflow:
    """Complete workflow for AI transcription to Resolve captions"""
    
    def __init__(self):
        self.resolve = None
        self.project_manager = None
        self.project = None
        self.timeline = None
        self.transcription_engine = None
        self.audio_processor = None
        self.srt_generator = None
        
        self._initialize_resolve()
        self._initialize_ai_components()
    
    def _initialize_resolve(self):
        """Initialize DaVinci Resolve connection"""
        try:
            self.resolve = dvr_script.scriptapp("Resolve")
            if not self.resolve:
                raise Exception("Failed to connect to DaVinci Resolve")
            
            self.project_manager = self.resolve.GetProjectManager()
            self.project = self.project_manager.GetCurrentProject()
            self.timeline = self.project.GetCurrentTimeline()
            
            print(f"✓ Connected to Resolve: Project '{self.project.GetName()}'")
            print(f"✓ Active timeline: '{self.timeline.GetName()}'")
            
        except Exception as e:
            print(f"✗ Resolve initialization failed: {e}")
            raise
    
    def _initialize_ai_components(self):
        """Initialize AI transcription components"""
        try:
            # Initialize transcription engine with default config
            self.transcription_engine = TranscriptionEngine()
            
            # Initialize audio processor with transcription config
            from src.transcription.config import TranscriptionConfig
            audio_config = TranscriptionConfig()
            self.audio_processor = AudioProcessor(audio_config)
            
            self.srt_generator = SRTGenerator()
            
            print("✓ AI transcription components initialized")
            
        except Exception as e:
            print(f"✗ AI components initialization failed: {e}")
            raise
    
    def extract_audio_from_timeline(self, output_path: str = None) -> str:
        """
        Extract audio from current timeline
        
        Args:
            output_path: Optional output path, otherwise uses temp file
            
        Returns:
            Path to extracted audio file
        """
        try:
            # Use temp file if no output path specified
            if not output_path:
                temp_dir = tempfile.mkdtemp()
                output_path = os.path.join(temp_dir, "timeline_audio.wav")
            
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            print(f"Extracting audio from timeline: {self.timeline.GetName()}")
            print(f"Output path: {output_path}")
            
            # Get timeline information
            duration = self.timeline.GetDuration()
            frame_rate = self.timeline.GetSetting("timelineFrameRate")
            
            print(f"Timeline info: {duration} frames @ {frame_rate} fps")
            
            # Note: This is a simplified approach. In a real implementation,
            # you would need to use Resolve's media pool and export functionality
            # or use external tools like FFmpeg to extract audio from rendered output
            
            # For this example, we'll create a placeholder and provide guidance
            print("⚠️  Audio extraction requires additional setup:")
            print("  1. Render timeline to audio file using Deliver page")
            print("  2. Use external tool to extract from source media")
            print("  3. Provide audio file path as parameter")
            
            # Create a placeholder (in real implementation, this would be actual audio)
            with open(output_path, 'wb') as f:
                f.write(b'placeholder_audio_data')
            
            return output_path
            
        except Exception as e:
            print(f"✗ Audio extraction failed: {e}")
            raise
    
    def generate_ai_transcription(self, audio_file: str) -> dict:
        """
        Generate AI transcription from audio file
        
        Args:
            audio_file: Path to audio file
            
        Returns:
            Transcription data dictionary
        """
        try:
            print(f"Generating AI transcription for: {audio_file}")
            
            # Process audio if needed
            processed_audio = self.audio_processor.process_audio(audio_file)
            
            # Generate transcription
            transcription_result = self.transcription_engine.transcribe(processed_audio)
            
            if not transcription_result:
                raise Exception("Transcription generation failed")
            
            print(f"✓ Transcription generated: {len(transcription_result.get('segments', []))} segments")
            
            return transcription_result
            
        except Exception as e:
            print(f"✗ AI transcription failed: {e}")
            raise
    
    def create_srt_from_transcription(self, transcription_data: dict, output_file: str, 
                                    include_speakers: bool = False) -> str:
        """
        Create SRT file from transcription data
        
        Args:
            transcription_data: Transcription data dictionary
            output_file: Output SRT file path
            include_speakers: Whether to include speaker labels
            
        Returns:
            Path to created SRT file
        """
        try:
            print(f"Creating SRT file: {output_file}")
            
            # Load transcription data into SRT generator
            success = self.srt_generator.load_from_transcription_data(transcription_data)
            if not success:
                raise Exception("Failed to load transcription data")
            
            # Validate segments
            if not self.srt_generator.validate_segments():
                print("⚠️  Validation warnings - check output")
            
            # Generate and save SRT
            success = self.srt_generator.save_srt(
                output_file, 
                include_speaker_labels=include_speakers,
                max_line_length=80
            )
            
            if not success:
                raise Exception("Failed to save SRT file")
            
            print(f"✓ SRT file created successfully")
            return output_file
            
        except Exception as e:
            print(f"✗ SRT creation failed: {e}")
            raise
    
    def import_captions_to_resolve(self, srt_file: str, offset: int = 0) -> bool:
        """
        Import SRT captions into current Resolve timeline
        
        Args:
            srt_file: Path to SRT file
            offset: Frame offset for captions
            
        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"Importing captions to timeline: {self.timeline.GetName()}")
            print(f"SRT file: {srt_file}")
            print(f"Offset: {offset} frames")
            
            # Import captions using Resolve API
            result = self.timeline.ImportCaptions(srt_file, offset)
            
            if result:
                # Verify import
                caption_count = self.timeline.GetCaptionsCount()
                print(f"✓ Captions imported successfully")
                print(f"  Total caption tracks: {caption_count}")
                return True
            else:
                print(f"✗ Caption import failed")
                return False
                
        except Exception as e:
            print(f"✗ Caption import error: {e}")
            return False
    
    def run_complete_workflow(self, audio_file: str = None, offset: int = 0, 
                            include_speakers: bool = False) -> bool:
        """
        Run the complete AI transcription to captions workflow
        
        Args:
            audio_file: Optional audio file path (auto-extract if not provided)
            offset: Frame offset for captions in timeline
            include_speakers: Whether to include speaker labels
            
        Returns:
            True if successful, False otherwise
        """
        try:
            print("=== Starting AI Transcription to Captions Workflow ===\n")
            
            # Step 1: Get audio file
            if not audio_file:
                print("--- Step 1: Extracting Audio from Timeline ---")
                audio_file = self.extract_audio_from_timeline()
                if not audio_file or not os.path.exists(audio_file):
                    print("✗ Audio extraction failed")
                    return False
            else:
                if not os.path.exists(audio_file):
                    print(f"✗ Audio file not found: {audio_file}")
                    return False
                print(f"--- Using provided audio file: {audio_file} ---")
            
            # Step 2: Generate AI transcription
            print("\n--- Step 2: Generating AI Transcription ---")
            transcription_data = self.generate_ai_transcription(audio_file)
            
            # Step 3: Create SRT file
            print("\n--- Step 3: Creating SRT Caption File ---")
            
            # Generate output filename
            timeline_name = self.timeline.GetName()
            safe_name = "".join(c for c in timeline_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Use temp directory for SRT file
            temp_dir = tempfile.mkdtemp()
            srt_file = os.path.join(temp_dir, f"{safe_name}_ai_captions_{timestamp}.srt")
            
            self.create_srt_from_transcription(
                transcription_data, 
                srt_file, 
                include_speakers=include_speakers
            )
            
            # Step 4: Import to Resolve
            print("\n--- Step 4: Importing Captions to Resolve ---")
            success = self.import_captions_to_resolve(srt_file, offset)
            
            if success:
                print("\n=== Workflow Completed Successfully ===")
                print(f"✓ Audio processed: {audio_file}")
                print(f"✓ AI transcription generated: {len(transcription_data.get('segments', []))} segments")
                print(f"✓ SRT file created: {srt_file}")
                print(f"✓ Captions imported to timeline: '{self.timeline.GetName()}'")
                
                # Display final caption count
                final_count = self.timeline.GetCaptionsCount()
                print(f"✓ Total caption tracks: {final_count}")
                
                return True
            else:
                print("\n=== Workflow Failed at Import Step ===")
                return False
                
        except Exception as e:
            print(f"\n=== Workflow Error ===")
            print(f"✗ {e}")
            return False


def main():
    """Main function with command-line interface"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="AI Transcription to DaVinci Resolve Captions Workflow",
        epilog="Example: python ai_to_resolve_captions.py --audio /path/to/audio.wav --offset 0"
    )
    
    parser.add_argument("--audio", "-a", help="Audio file path (auto-extract if not provided)")
    parser.add_argument("--offset", "-o", type=int, default=0, help="Frame offset for captions")
    parser.add_argument("--speakers", "-s", action="store_true", help="Include speaker labels")
    parser.add_argument("--extract-only", action="store_true", help="Only extract audio from timeline")
    parser.add_argument("--transcribe-only", help="Only transcribe audio file (no Resolve import)")
    
    args = parser.parse_args()
    
    try:
        # Initialize workflow
        workflow = AIResolveCaptionWorkflow()
        
        # Handle different operation modes
        if args.extract_only:
            print("=== Audio Extraction Mode ===")
            audio_file = workflow.extract_audio_from_timeline()
            print(f"Audio would be extracted to: {audio_file}")
            print("Note: Implement actual audio extraction based on your workflow")
            
        elif args.transcribe_only:
            print("=== Transcription Only Mode ===")
            if not os.path.exists(args.transcribe_only):
                print(f"✗ Audio file not found: {args.transcribe_only}")
                return 1
            
            transcription = workflow.generate_ai_transcription(args.transcribe_only)
            
            # Create SRT
            base_name = os.path.splitext(args.transcribe_only)[0]
            srt_file = f"{base_name}_ai.srt"
            workflow.create_srt_from_transcription(transcription, srt_file, args.speakers)
            
            print(f"✓ Transcription complete: {srt_file}")
            
        else:
            # Run complete workflow
            success = workflow.run_complete_workflow(
                audio_file=args.audio,
                offset=args.offset,
                include_speakers=args.speakers
            )
            
            return 0 if success else 1
            
    except KeyboardInterrupt:
        print("\n✗ Operation cancelled by user")
        return 1
    except Exception as e:
        print(f"✗ Fatal error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())